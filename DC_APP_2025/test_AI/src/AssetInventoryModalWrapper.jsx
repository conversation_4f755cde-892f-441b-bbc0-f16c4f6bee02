import React, { useState, useRef, useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { OverlayToaster, Intent } from '@blueprintjs/core';
import AssetInventoryModal from './AssetInventoryModal';
import { AppContext } from './AppContextProvider';
import { setSelectedAsset, setSelectedAssetClass } from './redux/appConfigurationSlice';
import { setActiveTab } from './redux/filesHandlerSlice';

const AssetInventoryModalWrapper = ({ isOpen, onClose, isDarkTheme }) => {
  const dispatch = useDispatch();
  const {
    allAssetsData,
    setAssetData,
    setCurrentAssetComponents,
    userEmail,
    sendMessage
  } = useContext(AppContext);

  // Get data from Redux store
  const selectedAsset = useSelector((state) => state.appConfiguration.selectedAsset);

  // Local state for AssetInventory
  const [focusedCell, setFocusedCell] = useState(null);
  const [validPropertiesChange, setValidPropertiesChange] = useState(false);

  const gridContainerRef = useRef(null);
  const myToaster = useRef(null);

  // Use the allAssetsData from context, or create placeholder data
  const data = allAssetsData && allAssetsData.length > 0 ? allAssetsData : [];
  const placeholderData = Array(10).fill(["", "", "", "", "", "", "", ""]);
  const headers = ["Owner", "Ready", "Tag Name", "Asset Classification", "Equipment Type", "Plant", "Unit", "System"];
  const isLoading = data.length === 0;
  const selectedSheet = "Storage Tank"; // Default value

  // Simplified handleFocusAssetClick that works with the modal
  const handleFocusAssetClick = async (cellData) => {
    try {
      console.log('Asset clicked in modal:', cellData);

      // Set the selected asset in Redux store
      const tagName = cellData['Tag Name'] || cellData.tagName || cellData[2]; // Try different possible keys
      const assetClass = cellData['Asset Classification'] || cellData.assetClassification || cellData[3];
      const template = cellData['Template'] || cellData.template || assetClass;

      if (tagName) {
        dispatch(setSelectedAsset(tagName));

        if (assetClass) {
          dispatch(setSelectedAssetClass(assetClass));
        }

        // Switch to the first tab to show the loaded data
        dispatch(setActiveTab("generalProperties"));

        // Send WebSocket message if available
        if (sendMessage && tagName) {
          sendMessage(tagName);
        }

        // Close the modal after selecting an asset
        onClose();

        // Show success message
        if (myToaster.current) {
          myToaster.current.show({
            message: `Asset ${tagName} selected successfully`,
            intent: Intent.SUCCESS,
            timeout: 3000
          });
        }
      } else {
        throw new Error('No tag name found in selected asset data');
      }

    } catch (error) {
      console.error('Error handling asset click:', error);
      if (myToaster.current) {
        myToaster.current.show({
          message: 'Error selecting asset. Please try again.',
          intent: Intent.DANGER,
          timeout: 3000
        });
      }
    }
  };

  // Update cell function - simplified for modal use
  const updateCell = async (rowIndex, columnKey, newValue) => {
    console.log('Cell update requested:', { rowIndex, columnKey, newValue });
    // For the modal, we'll just log the update request
    // The actual update functionality would need to be connected to the main app's API
  };

  // Set loading function - simplified for modal use
  const setIsloading = (loading) => {
    console.log('Loading state change requested:', loading);
  };

  // Dummy functions for props that AssetInventory expects
  const setData = () => {};
  const setIsSearchFocused = () => {};

  return (
    <>
      <OverlayToaster ref={myToaster} />
      <AssetInventoryModal
        isOpen={isOpen}
        onClose={onClose}
        isDarkTheme={isDarkTheme}
        placeholderData={placeholderData}
        headers={headers}
        isLoading={isLoading}
        searchTerm=""
        isSearchFocused={false}
        data={data}
        setData={setData}
        setFocusedCell={setFocusedCell}
        setIsSearchFocused={setIsSearchFocused}
        handleFocusAssetClick={handleFocusAssetClick}
        focusedCell={focusedCell}
        setIsloading={setIsloading}
        updateCell={updateCell}
        assetData={data} // Use the same data for assetData
        selectedSheet={selectedSheet}
        myToaster={myToaster}
        validPropertiesChange={validPropertiesChange}
        setValidPropertiesChange={setValidPropertiesChange}
        gridContainerRef={gridContainerRef}
      />
    </>
  );
};

export default AssetInventoryModalWrapper;
