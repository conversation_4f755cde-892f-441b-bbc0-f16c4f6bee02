import React, { useState, useEffect, useRef, useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { OverlayToaster } from '@blueprintjs/core';
import AssetInventoryModal from './AssetInventoryModal';
import { AppContext } from './AppContextProvider';

// Import the same hooks and functions used in DataCollectionApp
import { setSelectedAsset } from './redux/appConfigurationSlice';
import { setActiveTab } from './redux/filesHandlerSlice';

const AssetInventoryModalWrapper = ({ isOpen, onClose, isDarkTheme }) => {
  const dispatch = useDispatch();
  const { 
    assetData, 
    setAssetData,
    data,
    setData,
    isLoading,
    setIsLoading,
    headers,
    selectedSheet,
    setSelectedSheet,
    lookupValues,
    selectKeys
  } = useContext(AppContext);

  // Local state for AssetInventory
  const [focusedCell, setFocusedCell] = useState(null);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [validPropertiesChange, setValidPropertiesChange] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  
  const gridContainerRef = useRef(null);
  const myToaster = useRef(null);

  // Placeholder data for loading state
  const placeholderData = Array(10).fill(["", "", "", "", "", "", "", ""]);

  // Handle asset click - this is the key function that loads data into components
  const handleFocusAssetClick = async (cellData) => {
    try {
      console.log('Asset clicked in modal:', cellData);
      
      // Set the selected asset in Redux store
      dispatch(setSelectedAsset(cellData));
      
      // Switch to the first tab to show the loaded data
      dispatch(setActiveTab("generalProperties"));
      
      // Close the modal after selecting an asset
      onClose();
      
      // Show success message
      if (myToaster.current) {
        myToaster.current.show({
          message: `Asset ${cellData.tagName || cellData['Tag Name'] || 'selected'} loaded successfully`,
          intent: 'success',
          timeout: 3000
        });
      }
      
    } catch (error) {
      console.error('Error handling asset click:', error);
      if (myToaster.current) {
        myToaster.current.show({
          message: 'Error loading asset data',
          intent: 'danger',
          timeout: 3000
        });
      }
    }
  };

  // Update cell function - for editing asset data
  const updateCell = async (rowIndex, columnKey, newValue) => {
    try {
      console.log('Updating cell:', { rowIndex, columnKey, newValue });
      
      // Update the local data
      const updatedData = [...data];
      if (updatedData[rowIndex]) {
        updatedData[rowIndex] = {
          ...updatedData[rowIndex],
          [columnKey]: newValue
        };
        setData(updatedData);
      }
      
      // Here you would typically make an API call to save the changes
      // For now, just show a success message
      if (myToaster.current) {
        myToaster.current.show({
          message: 'Cell updated successfully',
          intent: 'success',
          timeout: 2000
        });
      }
      
    } catch (error) {
      console.error('Error updating cell:', error);
      if (myToaster.current) {
        myToaster.current.show({
          message: 'Error updating cell',
          intent: 'danger',
          timeout: 3000
        });
      }
    }
  };

  // Set loading function
  const setIsloading = (loading) => {
    setIsLoading(loading);
  };

  return (
    <>
      <OverlayToaster ref={myToaster} />
      <AssetInventoryModal
        isOpen={isOpen}
        onClose={onClose}
        isDarkTheme={isDarkTheme}
        placeholderData={placeholderData}
        headers={headers}
        isLoading={isLoading}
        searchTerm={searchTerm}
        isSearchFocused={isSearchFocused}
        data={data}
        setData={setData}
        setFocusedCell={setFocusedCell}
        setIsSearchFocused={setIsSearchFocused}
        handleFocusAssetClick={handleFocusAssetClick}
        focusedCell={focusedCell}
        setIsloading={setIsloading}
        updateCell={updateCell}
        assetData={assetData}
        selectedSheet={selectedSheet}
        myToaster={myToaster}
        validPropertiesChange={validPropertiesChange}
        setValidPropertiesChange={setValidPropertiesChange}
        gridContainerRef={gridContainerRef}
      />
    </>
  );
};

export default AssetInventoryModalWrapper;
