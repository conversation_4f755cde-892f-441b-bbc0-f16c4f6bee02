import { useState, useEffect, useMemo, useContext } from "react";
import { Layout, Model } from "flexlayout-react";
import { useSelector } from "react-redux";
import { AppContext } from "./AppContextProvider";
import ComponentsData from "./DataCollectionApp/ComponentProperties/ComponentsData";
import DataCollectionApp from "./DataCollectionApp";

// Import styles
import "./Styles/vaim-flexlayout-dark.css";
import "./Styles/vaim-flexlayout-light.css";

const DataCollectionAppRestructured = () => {
  const { isDarkTheme, currentAssetComponents } = useContext(AppContext);
  const selectedAsset = useSelector((state) => state.appConfiguration.selectedAsset);
  
  // Create dynamic layout based on whether components exist
  const createLayoutModel = useMemo(() => {
    const hasComponents = currentAssetComponents && currentAssetComponents.length > 0;

    const baseLayout = {
      global: {
        splitterEnableHandle: true,
        tabEnablePopout: false,
        tabSetEnableActiveIcon: true,
        tabSetMinWidth: 130,
        tabSetMinHeight: 100,
        tabSetEnableTabScrollbar: true,
        borderMinSize: 100,
        borderEnableTabScrollbar: true
      },
      layout: {
        type: "row",
        id: "main-row",
        children: [
          {
            type: "tabset",
            id: "data-collection-tabset",
            weight: hasComponents ? 50 : 100,
            children: [
              {
                type: "tab",
                id: "data-collection-main",
                name: "Data Collection",
                component: "dataCollectionMain",
                enableClose: false,
                icon: "database"
              }
            ]
          }
        ]
      }
    };

    // Add components tab if components exist
    if (hasComponents) {
      baseLayout.layout.children.push({
        type: "tabset",
        id: "components-tabset",
        weight: 50,
        children: [
          {
            type: "tab",
            id: "components-data",
            name: "Components Data",
            component: "componentsData",
            enableClose: false,
            icon: "cog"
          }
        ]
      });
    }

    return Model.fromJson(baseLayout);
  }, [currentAssetComponents]);

  const [layoutModel, setLayoutModel] = useState(createLayoutModel);

  // Update layout when components change
  useEffect(() => {
    setLayoutModel(createLayoutModel);
  }, [createLayoutModel]);

  const factory = (node) => {
    const component = node.getComponent();

    switch (component) {
      case "dataCollectionMain":
        return (
          <div style={{ height: "100%", overflow: "hidden" }}>
            <DataCollectionApp hideAssetInventory={true} />
          </div>
        );

      case "componentsData":
        return (
          <div style={{ height: "100%", overflow: "auto", padding: "10px" }}>
            <h4 style={{ margin: "2px", fontWeight: "bold", marginBottom: "10px" }}>
              Components Properties
            </h4>
            <div style={{ height: "calc(100% - 40px)", overflow: "auto" }}>
              <ComponentsData />
            </div>
          </div>
        );

      default:
        return <div>Component not found: {component}</div>;
    }
  };

  if (!selectedAsset) {
    return (
      <div style={{
        height: "100%",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: isDarkTheme ? "#f5f8fa" : "#182026"
      }}>
        <div style={{ textAlign: "center" }}>
          <h3>No Asset Selected</h3>
          <p>Please select an asset from the Asset Inventory to view its data.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        height: "100%",
        width: "100%",
        backgroundColor: isDarkTheme ? "#30404d" : "#f5f8fa"
      }}
      className={isDarkTheme ? "bp5-dark" : ""}
    >
      <Layout
        model={layoutModel}
        factory={factory}
        onModelChange={setLayoutModel}
        classNameMapper={(className) => {
          if (isDarkTheme) {
            return `${className} bp5-dark`;
          }
          return className;
        }}
      />
    </div>
  );
};

export default DataCollectionAppRestructured;
