import React, { useState, useContext, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Navbar,
  NavbarGroup,
  NavbarHeading,
  Alignment,
  Button,
  Icon,
  ProgressBar,
  NonIdealState,
  NavbarDivider,
  Position,
  Popover,
  Menu,
  MenuItem,
  H5,
  H6,
  Tag,
} from "@blueprintjs/core";
import FilesHandler from "./FilesHandler";
import { AILoadingContext } from "./FilesHandler";
import AILoadingIndicator from "./AILoadingIndicator";
import NewWindow from "react-new-improved-window"; // Corrected package name
import DataCollectionAppRestructured from "./DataCollectionAppRestructured";
import AssetInventoryModalWrapper from "./AssetInventoryModalWrapper";
import logo from "./images/VisualAIM_icon.png";
import userIcon from "./images/developer.png";
import "./Styles/App.css";
import { Badge } from "antd";
import {
  setIsFilesHandlerOpen,
  setLayoutModel,
  setSelectedAsset,
  setSelectedAssetClass,
  setSelectedAssetSearchKey,
  setLatestAssetClickParams,
  setQCVOwner,
  setOwnerDict,
  setNotifications,
  setIsProgressBarLoading,
  setProgressValue,
  setLookupValuesDict,
  setAppUsers,
} from "./redux/appConfigurationSlice";
import { setInspectionData } from "./redux/inspectionDataSlice";
import { setIsLoggedIn, setUser, setPlant } from "./redux/authSlice";
import { Layout, Model } from "flexlayout-react";
import "./Styles/vaim-flexlayout-dark.css";
import "./Styles/vaim-flexlayout-light.css";
import useNewWindow from "./useNewWindow";
import NotificationsPanel from "./NotificationsPanel/NotificationsPanel";
import { AppContext } from "./AppContextProvider";
import { Select } from "@blueprintjs/select";
import { useNavigate } from 'react-router-dom';
import { setHistoryOpen } from './redux/actionHistorySlice';
import ActionHistoryDialog from './ActionHistoryDialog';
import { clearAllViewers } from './redux/currentPdfSlice';
import { clearSelectedRow } from './redux/selectedRowsSlice';
import { 
  setSelectedFiles,
  setCurrentInspectionFiles,
  setCurrentInspectionFilesPath,
  setAllCurrentInspectionsFiles,
  setCurrentAssetFiles,
  setCurrentGeneralFile,
  setAllCurrentGeneralFiles,
  setQuickFilterText,
  setActiveModule,
} from './redux/filesHandlerSlice';
import { clearDataRows } from './redux/selectedAssetPropertiesSlice';
import { updateGridState } from './redux/componentsGridSlice';
import { AppToaster } from './CustomComponents/AppToaster';

const plantsMapping = {
  TOLEDO: "1",
  BRUNSWUICK: 2,
  MONTICELLO: 3,
  "BIG ISLAND": 4,
  "ALABAMA RIVER": 5,
  WAUNA: 6,
  NAHEOLA: 7,
  "BREWTON": 8,
  "LEAF RIVER": 9,
  "BREWTON_2": 8.2,
  "CROSSETT": 10,
  MEMPHIS: 11,
  CAMAS: 12,
  "GREEN BAY": 13,
  "SAVANNAH RIVER": 14,
  "PLATTSBURGH": 15,
  "HATTIESBURG": 16,
  "HALSEY TISSUE": 17,
  "PORT HUDSON": 18,
  "MUSKOGEE": 19,
  "DIXIE": 20,
 
};

const initialLayoutModel = Model.fromJson({
  global: {
    splitterEnableHandle: true,
    tabEnablePopout: true,
    tabSetEnableActiveIcon: true,
    tabSetMinWidth: 130,
    tabSetMinHeight: 100,
    tabSetEnableTabScrollbar: true,
    borderMinSize: 100,
    borderEnableTabScrollbar: true
  },
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 30,
        children: [
          {
            type: "tab",
            component: "dataCollection",
            name: "Equipment Data",
            enableClose: false,
            icon: "database"
          },
        ],
      },
      {
        type: "tabset",
        weight: 70,
        children: [
          {
            type: "tab",
            component: "filesHandler",
            name: "Files",
            enableClose: false,
            icon: "folder-open"
          },
        ],
      },
    ],
  },
});

const collapsedLayoutModel = Model.fromJson({
  global: {
    splitterEnableHandle: true,
    tabEnablePopout: true,
    tabSetEnableActiveIcon: true,
    tabSetMinWidth: 130,
    tabSetMinHeight: 100,
    tabSetEnableTabScrollbar: true,
    borderMinSize: 100,
    borderEnableTabScrollbar: true
  },
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 100,
        children: [
          {
            type: "tab",
            component: "dataCollection",
            name: "Data Collection",
            enableClose: false,
            icon: "database"
          },
        ],
      },
    ],
  },
});

function MainApp() {
  const dispatch = useDispatch();
  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const { setCurrentAssetComponents } = useContext(AppContext);
  const navigate = useNavigate();
  const { isLoggedIn } = useSelector((state) => state.auth);
  const {
    isFilesHandlerOpen,
    layoutModel,
    selectedAsset,
    selectedAssetClass,
    selectedWord,
    notifications,
  } = useSelector((state) => state.appConfiguration);

  const { user } = useSelector((state) => state.auth);
  const plant = useSelector((state) => state.auth.plant);
  const [popupRef, setPopupRef] = useState(null);
  const panZoomRef = useRef(null);

  let QCV_Owner = useSelector((state) => state.appConfiguration.QCV_Owner);

  const handleLogout = () => {
    // Clear local storage
    localStorage.removeItem("session_id");
    
    // Reset auth state
    dispatch(setIsLoggedIn(false));
    dispatch(setUser(null));
    dispatch(setPlant(null));

    // Reset asset-related states
    dispatch(setSelectedAsset(null));
    dispatch(setSelectedAssetClass(''));
    dispatch(setSelectedAssetSearchKey(''));
    dispatch(setLatestAssetClickParams({}));
    dispatch(setQCVOwner(''));
    dispatch(setOwnerDict({
      collector: null,
      qc: null,
      qcv: null
    }));

    // Reset data collections
    dispatch(setInspectionData([]));
    dispatch(setLookupValuesDict({}));
    dispatch(setAppUsers([]));

    // Reset files handler state
    dispatch(setSelectedFiles([]));
    dispatch(setCurrentInspectionFiles([]));
    dispatch(setCurrentInspectionFilesPath([]));
    dispatch(setAllCurrentInspectionsFiles([]));
    dispatch(setCurrentAssetFiles([]));
    dispatch(setCurrentGeneralFile([]));
    dispatch(setAllCurrentGeneralFiles([]));
    dispatch(setQuickFilterText(''));
    dispatch(setActiveModule(null));

    // Clear selected rows and component properties
    dispatch(clearSelectedRow({ gridId: 'inspections-grid' }));
    dispatch(clearDataRows());
    dispatch(updateGridState({
      columnState: null,
      filterState: null,
      sortState: null
    }));

    // Reset current asset components in AppContext
    setCurrentAssetComponents([]);

    // Reset UI states
    dispatch(setNotifications([]));
    dispatch(setIsProgressBarLoading(false));
    dispatch(setProgressValue(0));
    dispatch(setIsFilesHandlerOpen(false));
    dispatch(setLayoutModel('initial'));
    dispatch(setHistoryOpen(false));
    dispatch(clearAllViewers());
  };

  const { openNewWindow } = useNewWindow(
    <FilesHandler
      isDarkTheme={isDarkTheme}
      windowMode={true}
      ownerDocument={popupRef?.document}
      panZoomRef={panZoomRef}
    />,
    () => {
      setTimeout(() => {
        dispatch(setIsFilesHandlerOpen(false));
      }, 0);
    },
    setPopupRef
  );

  const handleOpenFilesHandler = () => {
    dispatch(setIsFilesHandlerOpen(true));
    dispatch(setLayoutModel("collapsed"));
    openNewWindow();
  };

  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [isAssetInventoryOpen, setIsAssetInventoryOpen] = useState(true); // Open by default
  const toggleOverlay = () => setIsOverlayOpen(!isOverlayOpen);
  const toggleAssetInventory = () => setIsAssetInventoryOpen(!isAssetInventoryOpen);

  const handleCopyAssetName = () => {
    if (selectedAsset) {
      navigator.clipboard.writeText(selectedAsset);
      AppToaster.show({
        message: `Copied "${selectedAsset}" to clipboard`,
        intent: "primary",
        timeout: 2000
      });
    }
  };

  const [showCopyButtonSpotlight, setShowCopyButtonSpotlight] = useState(() => {
    const shouldHideSpotlight = localStorage.getItem('hideCopyButtonSpotlight') === 'true';
    return !shouldHideSpotlight;
  });

  useEffect(() => {
    const shouldHideSpotlight = localStorage.getItem('hideCopyButtonSpotlight') === 'true';
    setShowCopyButtonSpotlight(!shouldHideSpotlight);
  }, []);

  const handleHideSpotlight = (e) => {
    if (e.target.checked) {
      localStorage.setItem('hideCopyButtonSpotlight', 'true');
    } else {
      localStorage.removeItem('hideCopyButtonSpotlight');
    }
    setShowCopyButtonSpotlight(!e.target.checked);
  };

  useEffect(() => {
    const shouldHideSpotlight = localStorage.getItem('hideCopyButtonSpotlight') === 'true';
    setShowCopyButtonSpotlight(!shouldHideSpotlight);
  }, []);

  function factory(node) {
    const component = node.getComponent();
    if (component === "dataCollection") {
      return (
        <div style={{ paddingTop: "0px", height: "100%" }}>
          <DataCollectionAppRestructured />
        </div>
      );
    } else if (component === "filesHandler") {
      return (
        <div style={{ paddingTop: "0px", height: "100%" }}>
          <FilesHandler
            selectedWord={selectedWord}
            isDarkTheme={isDarkTheme}
            windowMode={false}
            panZoomRef={panZoomRef}
          />
        </div>
      );
    }
  }

  const userMenu = (
    <Menu className={isDarkTheme ? "bp5-dark" : ""}>
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text={`User: ${user?.name || ""}`}
        icon="user"
      />
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text="Logout"
        icon="log-out"
        onClick={handleLogout}
      />
    </Menu>
  );

  const ModeSelect = ({ options, onItemSelect }) => {
    const [selectedMode, setSelectedMode] = useState(options[0]);

    const renderOption = (option, { handleClick, modifiers }) => {
      if (!modifiers.matchesPredicate) {
        return null;
      }
      return (
        <MenuItem
          active={modifiers.active}
          key={option.value}
          onClick={handleClick}
          text={option.label}
        />
      );
    };

    const handleItemSelect = (item) => {
      setSelectedMode(item);
      onItemSelect(item);
    };

    return (
      <Select
        items={options}
        itemRenderer={renderOption}
        onItemSelect={handleItemSelect}
        filterable={false}
      >
        <Button
          minimal={true}
          rightIcon="caret-down"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
          }}
        >
          <H5 style={{ marginTop: "10px" }}>{selectedMode.label}</H5>
        </Button>
      </Select>
    );
  };

  const options = [
    { label: "Collection", value: "Collection" },
    { label: "Validation", value: "Validation" },
    { label: "Off", value: "Off" },
  ];

  const CustomTag = ({ intent, large, style, children }) => {
    const baseStyle = {
      display: "inline-block",
      padding: "4px 8px",
      fontSize: large ? "14px" : "12px",
      fontWeight: "bold",
      color: "black",
      backgroundColor: "#FBB360",
      border: "2px solid black",
      boxShadow: "3px 3px 0px 0px rgba(0,0,0,1)",
      transition: "all 0.1s ease",
      cursor: "default",
      ...style,
    };

    return (
      <span
        style={baseStyle}
        onMouseEnter={(e) => {
          e.target.style.transform = "translate(2px, 2px)";
          e.target.style.boxShadow = "1px 1px 0px 0px rgba(0,0,0,1)";
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = "translate(0, 0)";
          e.target.style.boxShadow = "3px 3px 0px 0px rgba(0,0,0,1)";
        }}
      >
        {children}
      </span>
    );
  };

  return (
    <div className={`app-container ${isDarkTheme ? "bp5-dark" : ""}`}>
      <Navbar
        style={{}}
        fixedToTop={true}
        className={`${isDarkTheme ? "bp5-dark" : ""}`}
      >
        <NavbarGroup align={Alignment.LEFT} style={{ flexGrow: 1 }}>
          <NavbarHeading>
            <div className="heading-container">
              <img src={logo} className="App-logo" alt="logo" width={45} />
            </div>
          </NavbarHeading>

          {/* Asset Inventory Button */}
          <Button
            minimal
            icon="database"
            text="Asset Inventory"
            onClick={() => setIsAssetInventoryOpen(true)}
            style={{ marginLeft: "10px" }}
            title="Open Asset Inventory"
          />

          {QCV_Owner !== "" && (
            <div>
              <div>
                <Tag
                  intent="warning"
                  large
                  style={{ margin: 0, marginRight: "10px", fontWeight: "bold" }}
                >
                  QCV Owner: {QCV_Owner.split("@")[0]}
                </Tag>
              </div>
            </div>
          )}
        </NavbarGroup>
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            display: "flex",
            justifyContent: "center", // Centers horizontally
            alignItems: "center", // Centers vertically (along the cross axis)
          }}
        >
          {selectedAsset === null ? (
            <>
              <NonIdealState title="No Asset Selected" />
            </>
          ) : (
            <>
              <div className="asset-name-container" style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                margin: "0px",
                position: "relative"
              }}>
                <Button
                  minimal={true}
                  onClick={handleCopyAssetName}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '6px 12px',
                    height: 'auto'
                  }}
                >
                  <span style={{
                    fontWeight: "bold",
                    fontSize: '20px'
                  }}>
                    {selectedAsset}
                  </span>
                  <Icon icon="duplicate" size={16} style={{ marginLeft: '5px' }} />
                </Button>
              </div>
              <span
                className="bp5-tag bp5-intent-primary bp5-large bp5-minimal"
                style={{
                  minWidth: "70px",
                  fontWeight: "bold",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  fontSize: "14px",
                }}
              >
                {selectedAssetClass}
              </span>
            </>
          )}
        </div>
        <NavbarGroup align={Alignment.RIGHT} style={{ flexGrow: 1 }}>
          {/* Plant Info */}
          <H6 style={{ margin: 0, marginRight: "10px" }}>
            {plantsMapping[plant] ? `${plantsMapping[plant]} - ${plant}` : plant}
          </H6>
          <NavbarDivider />

          {/* Admin Dashboard - only for specific users */}
          {['<EMAIL>'].includes(user?.email) && (
            <>
              <Button
                icon={<Icon icon="vertical-bar-chart-asc" />}
                minimal={true}
                onClick={() => navigate('/dashboard')}
              />
              <NavbarDivider />
            </>
          )}

          {/* Main Actions Group */}
          <Button
            icon={<Icon icon="applications" />}
            minimal={true}
            onClick={handleOpenFilesHandler}
            disabled={isFilesHandlerOpen}
          />
          <NavbarDivider />

          {/* Notifications Group */}
          <Badge
            count={notifications?.errors?.length ?? 0}
            dot={true}
            color="#E76A6E"
            offset={[-6, 5]}
          >
            <Button
              role="button"
              icon="notifications"
              minimal={true}
              intent={(notifications?.errors?.length ?? 0) > 0 ? "danger" : "none"}
              onClick={toggleOverlay}
            />
          </Badge>
          <NavbarDivider />
          
          {/* AI Loading Indicator */}
          <AILoadingContext.Consumer>
            {({ isAILoading }) => (
              <AILoadingIndicator isLoading={isAILoading} />
            )}
          </AILoadingContext.Consumer>

          {/* History and User Group */}
          <Button 
            icon={<Icon icon="history" />} 
            minimal={true} 
            onClick={() => dispatch(setHistoryOpen(true))}
          />
          <NavbarDivider />

          {/* User and Theme Group */}
          <Popover
            content={userMenu}
            position={Position.BOTTOM}
            interactionKind="hover"
            className={isDarkTheme ? "bp5-dark" : ""}
          >
            <div className="circle-image">
              <img src={user?.picture || userIcon} alt="User" />
            </div>
          </Popover>
          <NavbarDivider />
          <Button
            icon={
              <Icon
                icon={isDarkTheme ? "flash" : "moon"}
                style={{ color: isDarkTheme ? "#d39c43" : "#d39c43" }}
              />
            }
            minimal={true}
            onClick={() => setIsDarkTheme(!isDarkTheme)}
          />
        </NavbarGroup>
      </Navbar>

      <div
        className={`main-content ${isDarkTheme ? "" : "light-theme"} ${
          isDarkTheme ? "bp5-dark" : ""
        }`}
      >
        <div className="split-pane-container">
          <Layout
            className="flexlayout__layout-dark"
            model={
              layoutModel === "initial"
                ? initialLayoutModel
                : collapsedLayoutModel
            }
            factory={factory}
          />
        </div>
        <NotificationsPanel
          isOverlayOpen={isOverlayOpen}
          toggleOverlay={toggleOverlay}
          isDarkTheme={isDarkTheme}
          setIsOverlayOpen={setIsOverlayOpen}
          notifications={notifications}
        />

        {/* Asset Inventory Modal */}
        <AssetInventoryModalWrapper
          isOpen={isAssetInventoryOpen}
          onClose={() => setIsAssetInventoryOpen(false)}
          isDarkTheme={isDarkTheme}
        />
      </div>
      <ActionHistoryDialog />
    </div>
  );
}

export default MainApp;
