/* Asset Inventory Modal Styles */

.asset-inventory-backdrop {
  background-color: rgba(0, 0, 0, 0.3) !important;
  z-index: 9999 !important;
}

.asset-inventory-modal {
  z-index: 10000 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  max-width: none !important;
  max-height: none !important;
}

.asset-inventory-modal .bp5-dialog {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  max-width: none !important;
  max-height: none !important;
  overflow: hidden !important;
}

.asset-inventory-modal .bp5-dialog-container {
  align-items: flex-start !important;
  justify-content: flex-start !important;
  padding: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
}

.asset-inventory-modal-content {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bp5-app-background-color);
  overflow: hidden;
}

.asset-inventory-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid var(--bp5-border-color);
  background-color: var(--bp5-background-color);
  flex-shrink: 0;
  z-index: 1;
  height: 50px;
  min-height: 50px;
  max-height: 50px;
}

.asset-inventory-modal-header.light {
  background-color: #f5f8fa;
  border-bottom-color: #e1e8ed;
  color: #182026;
}

.asset-inventory-modal-header.dark {
  background-color: #394b59;
  border-bottom-color: #30404d;
  color: #f5f8fa;
}

.modal-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.modal-controls {
  display: flex;
  gap: 8px;
}

.modal-control-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-control-button:hover {
  background-color: var(--bp5-button-background-color-hover);
}

.asset-inventory-modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 50px);
  min-height: 0;
}

/* Minimized State */
.asset-inventory-minimized {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 9998;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.asset-inventory-minimized.light {
  background-color: #ffffff;
  border: 1px solid #e1e8ed;
}

.asset-inventory-minimized.dark {
  background-color: #394b59;
  border: 1px solid #30404d;
}

.asset-inventory-minimized:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.minimized-button {
  border: none !important;
  box-shadow: none !important;
  font-weight: 600;
  padding: 12px 16px;
  border-radius: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .asset-inventory-modal-header {
    padding: 8px 12px;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .asset-inventory-minimized {
    top: 70px;
    right: 10px;
  }
  
  .minimized-button {
    padding: 10px 14px;
    font-size: 14px;
  }
}

/* Animation for modal opening/closing */
.asset-inventory-modal .bp5-dialog {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Ensure modal content doesn't interfere with other elements */
.asset-inventory-modal * {
  box-sizing: border-box;
}

/* Override any conflicting styles */
.asset-inventory-modal .bp5-dialog-container {
  align-items: flex-start !important;
  justify-content: flex-start !important;
  padding: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
  pointer-events: auto !important;
}

.asset-inventory-modal .bp5-dialog-body {
  margin: 0 !important;
  padding: 0 !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* Ensure all child elements can receive pointer events */
.asset-inventory-modal * {
  pointer-events: auto !important;
}

/* Make sure the modal is fully interactive */
.asset-inventory-modal {
  pointer-events: auto !important;
}

.asset-inventory-modal .bp5-dialog {
  pointer-events: auto !important;
}
