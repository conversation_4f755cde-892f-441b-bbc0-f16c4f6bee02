import React, { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { Spin<PERSON>, SpinnerSize } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";
import { Document, Page, pdfjs } from 'react-pdf';
import { PdfHighlighter } from 'react-pdf-highlighter';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf-highlighter/dist/style.css';

// Use a specific compatible version to avoid version mismatch
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.js`;

const PDFJsViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [highlights, setHighlights] = useState([]);
  const [isPanning, setIsPanning] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const pdfRef = useRef();

  useEffect(() => {
    async function fetchFile() {
      setPdfFile(null);
      setDownloadProgress(0);

      try {
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / contentLength);
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
      }
    }

    fetchFile();

    return () => {
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile); // Memory management
      }
    };
  }, [fileId]);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  const highlightArea = (position) => {
    const newHighlight = {
      position,
      content: { text: 'Highlighted text' }
    };
    setHighlights(prevHighlights => [...prevHighlights, newHighlight]);
  };

  const handleZoomIn = useCallback(() => {
    setScale(prevScale => Math.min(prevScale + 0.1, 3.0)); // Limit max zoom level
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prevScale => Math.max(prevScale - 0.1, 0.1)); // Limit min zoom level
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  }, []);

  const startPanning = useCallback((e) => {
    setIsPanning(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  }, [position]);

  const onPanning = useCallback((e) => {
    if (!isPanning) return;
    setPosition({ x: e.clientX - startPos.x, y: e.clientY - startPos.y });
  }, [isPanning, startPos]);

  const stopPanning = useCallback(() => {
    setIsPanning(false);
  }, []);

  if (!pdfFile) {
    return (
      <div className="spinner-container">
        <Spinner
          className="spinner-center"
          intent="primary"
          size={SpinnerSize.STANDARD}
          value={downloadProgress}
        />
      </div>
    );
  }

  const setThemeInIframe = (iframeDocument, theme) => {
    const setThemeFunctionString = `
      function setTheme(theme) {
        if (theme == 'Dark') {
          PDFViewerApplicationOptions.set('viewerCssTheme', 1);
        }
        else {
          PDFViewerApplicationOptions.set('viewerCssTheme', 2);
        }
        PDFViewerApplication._forceCssTheme();
      }
    `;

    const script = iframeDocument.createElement('script');
    script.textContent = setThemeFunctionString;
    iframeDocument.head.appendChild(script);

    iframeDocument.defaultView.setTheme(isDarkTheme ? 'Dark' : 'Light');
  };

  const simulateClickOnHandTool = (iframeDocument) => {
    const script = iframeDocument.createElement('script');
    script.textContent = `
      function simulateClick() {
        const handToolButton = document.getElementById('cursorHandTool');
        if (handToolButton) {
          handToolButton.click();
        }
      }
      document.addEventListener('DOMContentLoaded', simulateClick);
      if (document.readyState === 'complete') {
        simulateClick();
      }
    `;
    iframeDocument.head.appendChild(script);
  };

  return (
    <div
      onMouseDown={startPanning}
      onMouseMove={onPanning}
      onMouseUp={stopPanning}
      onMouseLeave={stopPanning}
      style={{ overflow: 'hidden', cursor: isPanning ? 'grabbing' : 'grab', height: '100vh' }}
    >
      <div style={{ transform: `translate(${position.x}px, ${position.y}px)` }}>
        <div>
          <button onClick={handleZoomIn}>Zoom In</button>
          <button onClick={handleZoomOut}>Zoom Out</button>
          <button onClick={handleRotate}>Rotate</button>
          <button onClick={() => setPageNumber(prevPageNumber => Math.max(prevPageNumber - 1, 1))}>Previous Page</button>
          <button onClick={() => setPageNumber(prevPageNumber => Math.min(prevPageNumber + 1, numPages))}>Next Page</button>
        </div>
        <p>
          Page {pageNumber} of {numPages}
        </p>
        <PdfHighlighter
          pdfDocument={pdfRef.current?.pdfDocument}
          enableAreaSelection={(event) => event.key === 'Alt'}
          onScrollChange={() => {}}
          scrollRef={(scrollTo) => {
            pdfRef.current.scrollTo = scrollTo;
          }}
          onSelectionFinished={(
            position,
            content,
            hideTipAndSelection,
            transformSelection
          ) => {
            highlightArea(transformSelection());
            hideTipAndSelection();
          }}
          highlightTransform={(
            highlight,
            index,
            setTip,
            hideTip,
            viewportToScaled,
            screenshot,
            isScrolledTo
          ) => {
            const highlightArea = viewportToScaled(highlight.position);
            return (
              <div
                key={index}
                style={{
                  position: 'absolute',
                  transform: `translate(${highlightArea.boundingRect.x}px, ${highlightArea.boundingRect.y}px)`,
                  width: `${highlightArea.boundingRect.width}px`,
                  height: `${highlightArea.boundingRect.height}px`,
                  backgroundColor: 'yellow',
                  opacity: 0.4
                }}
              />
            );
          }}
          highlights={highlights}
        >
          {({ getPageProps }) => (
            <Document
              file={pdfFile}
              onLoadSuccess={onDocumentLoadSuccess}
            >
              <Page
                {...getPageProps({
                  pageNumber,
                  scale,
                  rotate: rotation
                })}
              />
            </Document>
          )}
        </PdfHighlighter>
      </div>
    </div>
  );
};

export default PDFJsViewer;
