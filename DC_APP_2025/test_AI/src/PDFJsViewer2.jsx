import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Spin<PERSON>, SpinnerSize } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Use a working CDN for the PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const PDFJsViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [isPanning, setIsPanning] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const pdfRef = useRef();

  useEffect(() => {
    async function fetchFile() {
      setPdfFile(null);
      setDownloadProgress(0);

      try {
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / contentLength);
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
      }
    }

    fetchFile();

    return () => {
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile); // Memory management
      }
    };
  }, [fileId]);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  // Removed highlight functionality to avoid version conflicts

  const handleZoomIn = useCallback(() => {
    setScale(prevScale => Math.min(prevScale + 0.1, 3.0)); // Limit max zoom level
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prevScale => Math.max(prevScale - 0.1, 0.1)); // Limit min zoom level
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  }, []);

  const startPanning = useCallback((e) => {
    setIsPanning(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  }, [position]);

  const onPanning = useCallback((e) => {
    if (!isPanning) return;
    setPosition({ x: e.clientX - startPos.x, y: e.clientY - startPos.y });
  }, [isPanning, startPos]);

  const stopPanning = useCallback(() => {
    setIsPanning(false);
  }, []);

  if (!pdfFile) {
    return (
      <div className="spinner-container">
        <Spinner
          className="spinner-center"
          intent="primary"
          size={SpinnerSize.STANDARD}
          value={downloadProgress}
        />
      </div>
    );
  }

  // Removed unused iframe functions since we're using react-pdf directly

  return (
    <div
      onMouseDown={startPanning}
      onMouseMove={onPanning}
      onMouseUp={stopPanning}
      onMouseLeave={stopPanning}
      style={{ overflow: 'hidden', cursor: isPanning ? 'grabbing' : 'grab', height: '100vh' }}
    >
      <div style={{ transform: `translate(${position.x}px, ${position.y}px)` }}>
        <div>
          <button onClick={handleZoomIn}>Zoom In</button>
          <button onClick={handleZoomOut}>Zoom Out</button>
          <button onClick={handleRotate}>Rotate</button>
          <button onClick={() => setPageNumber(prevPageNumber => Math.max(prevPageNumber - 1, 1))}>Previous Page</button>
          <button onClick={() => setPageNumber(prevPageNumber => Math.min(prevPageNumber + 1, numPages))}>Next Page</button>
        </div>
        <p>
          Page {pageNumber} of {numPages}
        </p>
        <Document
          file={pdfFile}
          onLoadSuccess={onDocumentLoadSuccess}
          ref={pdfRef}
        >
          <Page
            pageNumber={pageNumber}
            scale={scale}
            rotate={rotation}
            renderTextLayer={true}
            renderAnnotationLayer={true}
          />
        </Document>
      </div>
    </div>
  );
};

export default PDFJsViewer;
