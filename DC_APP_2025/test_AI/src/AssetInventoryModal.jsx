import React, { useState, useContext } from 'react';
import { Dialog, Button, Icon } from '@blueprintjs/core';
import AssetInventory from './DataCollectionApp/AssetInventory/AssetInventory';
import { AppContext } from './AppContextProvider';
import './Styles/AssetInventoryModal.css';

const AssetInventoryModal = ({ isOpen, onClose, isDarkTheme }) => {
  const [isMinimized, setIsMinimized] = useState(false);

  const handleMinimize = () => {
    setIsMinimized(true);
    onClose();
  };

  const modalClasses = `asset-inventory-modal ${isDarkTheme ? 'bp5-dark' : ''}`;
  
  return (
    <>
      {/* Full Screen Modal */}
      <Dialog
        isOpen={isOpen && !isMinimized}
        onClose={onClose}
        canEscapeKeyClose={true}
        canOutsideClickClose={false}
        hasBackdrop={true}
        backdropClassName="asset-inventory-backdrop"
        className={modalClasses}
        style={{
          width: '100vw',
          height: '100vh',
          margin: 0,
          padding: 0,
          borderRadius: 0,
          maxWidth: 'none',
          maxHeight: 'none',
          top: 0,
          left: 0,
          transform: 'none'
        }}
      >
        <div className="asset-inventory-modal-content">
          {/* Modal Header */}
          <div className={`asset-inventory-modal-header ${isDarkTheme ? 'dark' : 'light'}`}>
            <div className="modal-title">
              <Icon icon="database" size={20} style={{ marginRight: '8px' }} />
              <span>Asset Inventory</span>
            </div>
            <div className="modal-controls">
              <Button
                minimal
                icon="minus"
                onClick={handleMinimize}
                title="Minimize"
                className="modal-control-button"
              />
              <Button
                minimal
                icon="cross"
                onClick={onClose}
                title="Close"
                className="modal-control-button"
              />
            </div>
          </div>

          {/* Modal Body */}
          <div className="asset-inventory-modal-body">
            <AssetInventory />
          </div>
        </div>
      </Dialog>

      {/* Minimized State - Floating Button */}
      {isMinimized && (
        <div className={`asset-inventory-minimized ${isDarkTheme ? 'dark' : 'light'}`}>
          <Button
            large
            intent="primary"
            icon="database"
            onClick={() => setIsMinimized(false)}
            className="minimized-button"
            title="Open Asset Inventory"
          >
            Asset Inventory
          </Button>
        </div>
      )}
    </>
  );
};

export default AssetInventoryModal;
