import React, { useState, useRef } from 'react';
import { Dialog, Button, Icon, OverlayToaster } from '@blueprintjs/core';
import AssetInventory from './DataCollectionApp/AssetInventory/AssetInventory';
import './Styles/AssetInventoryModal.css';

const AssetInventoryModal = ({
  isOpen,
  onClose,
  isDarkTheme,
  // All the props needed for AssetInventory functionality
  placeholderData,
  headers,
  isLoading,
  searchTerm,
  isSearchFocused,
  data,
  setData,
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  setIsloading,
  updateCell,
  assetData,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef
}) => {
  const [isMinimized, setIsMinimized] = useState(false);

  const handleMinimize = () => {
    setIsMinimized(true);
    onClose();
  };

  const modalClasses = `asset-inventory-modal ${isDarkTheme ? 'bp5-dark' : ''}`;

  return (
    <>
      {/* Custom Backdrop */}
      {isOpen && !isMinimized && (
        <div
          className="custom-backdrop"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            zIndex: 9998,
            pointerEvents: 'none' // This allows interactions to pass through
          }}
        />
      )}

      {/* Full Screen Modal */}
      <Dialog
        isOpen={isOpen && !isMinimized}
        onClose={onClose}
        canEscapeKeyClose={true}
        canOutsideClickClose={false}
        hasBackdrop={false}
        className={modalClasses}
        style={{
          width: '100vw',
          height: '100vh',
          margin: 0,
          padding: 0,
          borderRadius: 0,
          maxWidth: 'none',
          maxHeight: 'none',
          top: 0,
          left: 0,
          transform: 'none',
          position: 'fixed',
          zIndex: 10000
        }}
      >
        <div className="asset-inventory-modal-content">
          {/* Modal Header */}
          <div className={`asset-inventory-modal-header ${isDarkTheme ? 'dark' : 'light'}`}>
            <div className="modal-title">
              <Icon icon="database" size={20} style={{ marginRight: '8px' }} />
              <span>Asset Inventory</span>
            </div>
            <div className="modal-controls">
              <Button
                minimal
                icon="minus"
                onClick={handleMinimize}
                title="Minimize"
                className="modal-control-button"
              />
              <Button
                minimal
                icon="cross"
                onClick={onClose}
                title="Close"
                className="modal-control-button"
              />
            </div>
          </div>

          {/* Modal Body */}
          <div className="asset-inventory-modal-body">
            <div style={{
              height: '100%',
              width: '100%',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <AssetInventory
                placeholderData={placeholderData}
                headers={headers}
                isLoading={isLoading}
                searchTerm={searchTerm}
                isSearchFocused={isSearchFocused}
                data={data}
                setData={setData}
                setFocusedCell={setFocusedCell}
                setIsSearchFocused={setIsSearchFocused}
                handleFocusAssetClick={handleFocusAssetClick}
                focusedCell={focusedCell}
                setIsloading={setIsloading}
                updateCell={updateCell}
                assetData={assetData}
                selectedSheet={selectedSheet}
                myToaster={myToaster}
                validPropertiesChange={validPropertiesChange}
                setValidPropertiesChange={setValidPropertiesChange}
                gridContainerRef={gridContainerRef}
              />
            </div>
          </div>
        </div>
      </Dialog>

      {/* Minimized State - Floating Button */}
      {isMinimized && (
        <div className={`asset-inventory-minimized ${isDarkTheme ? 'dark' : 'light'}`}>
          <Button
            large
            intent="primary"
            icon="database"
            onClick={() => setIsMinimized(false)}
            className="minimized-button"
            title="Open Asset Inventory"
          >
            Asset Inventory
          </Button>
        </div>
      )}
    </>
  );
};

export default AssetInventoryModal;
