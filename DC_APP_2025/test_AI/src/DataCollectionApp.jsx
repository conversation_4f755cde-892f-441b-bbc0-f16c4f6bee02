// DataCollectionApp.js
import { propertyDataTypes } from "./DataCollectionApp/AssetProperties/dataTypes";
import React, { useState, useEffect, useRef, useMemo, useContext } from "react";

import axios from "axios";
import {
  Button,
  ButtonGroup,
  Icon,
  OverlayToaster,
  Intent,
  Card,
  Spinner,
  Tooltip,
  Position,
} from "@blueprintjs/core";
import AssetInventory from "./DataCollectionApp/AssetInventory/AssetInventory";
import GeneralProperties from "./DataCollectionApp/AssetProperties/AssetProperties";
import ComponentsData from "./DataCollectionApp/ComponentProperties/ComponentsData";
import PreviousInspections from "./DataCollectionApp/PreviousInspections/PreviousInspections";
import GeneralFiles from "./DataCollectionApp/GeneralFiles/GeneralFiles";
import NcrData from "./DataCollectionApp/NCRs/NcrData";
import Nozzles from "./DataCollectionApp/Nozzles/Nozzles";
import Posts from "./DataCollectionApp/Posts/Posts";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExpand, faCompress } from "@fortawesome/free-solid-svg-icons"; // or free-regular-svg-icons

import { RxCalendar } from "react-icons/rx";
import { AppContext } from "./AppContextProvider";
import { SlGraph } from "react-icons/sl";

import SplitPane from "react-split-pane";

import { HiOutlineWrench } from "react-icons/hi2";

import { ImFilesEmpty } from "react-icons/im";
import { useSelector, useDispatch } from "react-redux";

import {
  setIsProgressBarLoading,
  setSelectedAssetClass,
  setSelectedAsset,
  setLatestAssetClickParams,
} from "./redux/appConfigurationSlice";
import { setActiveTab, setCurrentAssetFiles } from "./redux/filesHandlerSlice";
import { setPostIds, setPostsIndices } from "./redux/postsSlice";
import { MdOutlineList } from "react-icons/md";
import { PiChats } from "react-icons/pi";

import CMLs from "./DataCollectionApp/CMLs/CMLs";

import { GiStraightPipe } from "react-icons/gi";
import makeApiCallWithRetry from "./DataCollectionApp/Helpers/apiHelper";
import { setInspectionData } from "./redux/inspectionDataSlice";
import { setQCVOwner } from "./redux/appConfigurationSlice";
import NozzleIcon from "./NozzleIcon";

import { addAction } from './redux/actionHistorySlice';

//import { Tooltip } from "antd";
// import auth slice
// import all at ComponentsData.css file
function DataCollectionApp({ selectedWord, setSelectedWord, hideAssetInventory = false }) {
  const placeholderData = Array(10).fill(["", "", "", "", "", "", "", ""]);
  const placeholderHeaders = [
    "Owner",
    "Ready",
    "Tag Name",
    "Asset Classification",
    "Plant",
    "Unit",
    "System",
    "Equipment Type",
    "Template",
  ];

  const [data, setData] = useState(placeholderData);
  const [headers, setHeaders] = useState(placeholderHeaders);
  const [isLoading, setIsLoading] = useState(true);

  const [searchTerm, setSearchTerm] = useState("");
  const [focusedCell, setFocusedCell] = useState(null);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(null);
  const { selectedSheet, setselectedSheet } = useContext(AppContext);
  const [propertyValues, setPropertyValues] = useState({});

  const [inputIntents, setInputIntents] = useState({});
  const [selectKeys, setSelectKeys] = useState([]);
  const { assetData, setAssetData } = useContext(AppContext);
  // Redux states
  const latestAssetClickParams = useSelector(
    (state) => state.appConfiguration.latestAssetClickParams
  );

  const currentPage = useSelector((state) => state.pdfViewer.pageNumber);
  const pdfFile = useSelector((state) => state.pdfViewer.pdfFile);
  const activeModule = useSelector((state) => state.filesHandler.activeModule);

  const [isExpanded, setIsExpanded] = useState(false);

  const lastFocusedRef = useRef(null);

  const inspectionData = useSelector(
    (state) => state.inspectionData.inspectionData
  );

  const [isPreviousInspectionsExpanded, setIsPreviousInspectionsExpanded] =
    useState(false);
  const [previousInspectionsPosition] = useState({ x: 0, y: 0 });
  const [inspectionDataRows, setInspectionDataRows] = useState([]);
  const [generalFilesDataRows, setGeneralFilesDataRows] = useState([]);
  const [ncrsDataRows, setNcrsDataRows] = useState([]);

  const { nozzlesData, setNozzlesData } = useContext(AppContext);

  const [nozzlesDataRows, setNozzlesDataRows] = useState([]);
  const [postDataRows, setPostDataRows] = useState([]);

  const [validPropertiesChange, setValidPropertiesChange] = useState(false);
  const [paneSize] = useState("24%"); // Default size
  const gridContainerRef = useRef(null);
  const { isDarkTheme } = useContext(AppContext);

  const [generalFilesData, setGeneralFilesData] = useState([]);
  const [ncrsData, setNcrsData] = useState([]);

  const { setPreviousInspectionDates } = useContext(AppContext);
  const { lookupValues } = useContext(AppContext);

  const togglePreviousInspectionsExpand = () => {
    setIsPreviousInspectionsExpanded(!isPreviousInspectionsExpanded);
  };

  // get email from authSLice user.email
  const userEmail = useSelector((state) => state.auth.user.email);

  const { currentAssetComponents, setCurrentAssetComponents } =
    useContext(AppContext);
  const { userData, selectedAssetClass } = useContext(AppContext);

  const [isValidPropertyBeingChanged, setIsValidPropertyBeingChanged] =
    useState(false);

  //All files at inspection module
  const { allCurrentInspectionsFiles, setAllCurrentInspectionsFiles } =
    useContext(AppContext);

  // All Files at General Module

  const { allCurrentGeneralFiles, setAllCurrentGeneralFiles } =
    useContext(AppContext);

  // ALl files from the asset:

  const { setCurrentGeneralFile } = useContext(AppContext);

  const activeTab = useSelector((state) => state.filesHandler.activeTab);
  const dispatch = useDispatch();

  const { assetClassification } = useContext(AppContext);

  const { setAssetClassification } = useContext(AppContext);

  const { currentInspectionFiles, setCurrentInspectionFiles } =
    useContext(AppContext);

  const visibleViewers = useSelector((state) => {
    const { viewers, visibleViewers } = state.currentPdf;
    return visibleViewers
      .map((id) => viewers[id])
      .filter((viewer) => viewer?.isVisible);
  });

  const isProgressBarLoading = useSelector(
    (state) => state.appConfiguration.isProgressBarLoading
  );

  const SheetsMapping = {
    "Storage Tank": "Data ST",
    "Pressure Vessel": "Data PV",
    Piping: "Data Piping",
    PRD: "PRD",
    "Additional Assets": "Additional Assets",
  };

  useEffect(() => {
    if (inspectionData) {
      let allInspectionFiles = [];

      inspectionData.forEach((row) => {
        if (row.Files && row.Files !== "") {
          try {
            // Parse the JSON string to an array of objects
            const filesArray = JSON.parse(row.Files);

            // Extract the "path" values from each object and add them to the list
            allInspectionFiles.push(...filesArray.map((file) => file.path));
          } catch (error) {
            console.error("Error parsing JSON:", error);
          }
        }
      });

      allInspectionFiles = [...new Set(allInspectionFiles)];
      setAllCurrentInspectionsFiles(allInspectionFiles);
    }
  }, [inspectionData]);

  //set All current General Files
 useEffect(() => {
    if (generalFilesData) {
      console.log("General Files Data", generalFilesData);
      
      // Create a new list to hold all general files
      let allGeneralFiles = [];
  
      // Loop through each row data in generalFilesData
      generalFilesData.forEach((row) => {
        // Check if the Path column exists and is not empty
        if (row.Path && typeof row.Path === 'string' && row.Path.trim() !== "") {
          try {
            // First attempt to parse as JSON in case it's a JSON string
            const parsedPaths = JSON.parse(row.Path);
            if (Array.isArray(parsedPaths)) {
              allGeneralFiles.push(...parsedPaths);
            } else if (typeof parsedPaths === 'string') {
              allGeneralFiles.push(parsedPaths);
            }
          } catch (e) {
            // If JSON parsing fails, treat it as a single path
            allGeneralFiles.push(row.Path.trim());
          }
        }
      });
  
      // Remove duplicates while preserving the original path strings
      allGeneralFiles = [...new Set(allGeneralFiles)];
  
      console.log("All General Files", allGeneralFiles);
      setAllCurrentGeneralFiles(allGeneralFiles);
    }
  }, [generalFilesData, setAllCurrentGeneralFiles]);

  // Effect to combine and deduplicate files
  useEffect(() => {
    // Combine both arrays
    const combinedFiles = [
      ...allCurrentInspectionsFiles,
      ...allCurrentGeneralFiles,
    ];
    // Remove duplicates
    const uniqueFiles = [...new Set(combinedFiles)];
    // Set the combined list without duplicates to currentAssetFiles
    dispatch(setCurrentAssetFiles(uniqueFiles));
    console.log("Combined Files", uniqueFiles);
  }, [
    allCurrentInspectionsFiles,
    allCurrentGeneralFiles,
    setCurrentAssetFiles,
  ]);

  // NEW: Function to toggle between expanded and default mode
  const toggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  const myToaster = React.useRef(null);

  console.log("logging", inspectionData);

  const handleFocusAssetClick = async (specificFocusedCell = null) => {
    //refreshing the data
    setCurrentInspectionFiles([]);
    setAllCurrentGeneralFiles([]);
    setCurrentGeneralFile([]);
    setCurrentInspectionFiles([]);

    try {
      console.log("Email", specificFocusedCell["Email"]);
      console.log("userEmail", userEmail);
      // Assuming userEmail is the variable that holds the email address to be checked.
      let email = specificFocusedCell["Email"].toLowerCase();

      // List of strings to check in the email

      console.log("Email:", email);
      console.log("User Email:", userEmail);

      // Normalize the values to handle case sensitivity and trim spaces
      const emailNormalized = email.trim().toLowerCase();
      const userEmailNormalized = userEmail.trim().toLowerCase();

      const emailList = userData["User Email"].map((email) =>
        email.toLowerCase()
      );

      const userInUserList = emailList.includes(userEmailNormalized);

      const userInUserListAndemailALL =
        emailList.includes(userEmailNormalized) && emailNormalized === "all";

      const superAdminList = process.env.REACT_APP_ALLOWED_LIST.split(",").map(
        (email) => email.toLowerCase()
      );

      const userInSuperAdminList = superAdminList.includes(userEmailNormalized);

      console.log("User in User List:", userInUserList);
      console.log(
        "User in User List and email ALL:",
        userInUserListAndemailALL
      );
      console.log("User in super admin list:", userInSuperAdminList);

      console.log("Email List:", emailList);

      // Check if email is not equal to userEmail
      // Except when email is 'ALL' and userEmail is in emailList
      if (
        emailNormalized !== userEmailNormalized &&
        !userInUserListAndemailALL &&
        !userInSuperAdminList
      ) {
        console.log("Access Denied");
        const message = `User Authentication Failed`;
        
        // Show toast
        if (myToaster.current) {
          myToaster.current.show({
            message: message,
            intent: Intent.WARNING,
            timeout: 6000,
            className: "font-size-11",
            icon: "warning-sign",
          });
        }

        // Add to action history
        dispatch(addAction({
          type: 'WARNING',
          message: message,
          source: 'Asset Access'
        }));

        return;
      }

      const cellToUse = specificFocusedCell || focusedCell;
      dispatch(setLatestAssetClickParams(cellToUse));
      const focusedRowDict = specificFocusedCell;
      dispatch(setIsProgressBarLoading(true));

      console.log("row", focusedRowDict);
      console.log("template", focusedRowDict["Template"]);

      const allAssetDataRequest = makeApiCallWithRetry(
        `${process.env.REACT_APP_DATA_API}/data/combined`,
        {
          method: "get",
          params: {
            plant: focusedRowDict["Plant"],
            unit: focusedRowDict["Unit"],
            system: focusedRowDict["System"],
            tag_name: focusedRowDict["Tag Name"],
            asset_classification: focusedRowDict["Asset Classification"],
            equipment_type: focusedRowDict["Equipment Type"],
            template: SheetsMapping[focusedRowDict["Template"]],
            user: String(userEmail),
          },
        }
      );

      const allActions = Promise.all([allAssetDataRequest]);

      const resolveAllActions = async () => {
        try {
          const [allAssetDataResponse] = await allActions;

          let allAssetData = allAssetDataResponse.data;

          dispatch(setQCVOwner(allAssetData.data_rows.rows[0]["Owner QCV"]));

          let propertiesData = allAssetData.data_rows;
          let previousInspectionsData = allAssetData.previous_inspections;
          let generalFilesData = allAssetData.general_files;
          let ncrsData = allAssetData.ncrs;
          let postsMetadata = allAssetData.posts;
          let nozzlesData = allAssetData.nozzles;
          let postrows = postsMetadata.rows;

          // for each row at postrows get post_id
          let posts = postrows.map((row) => row["post_id"]);

          console.log("posts_ids", posts);
          dispatch(setPostIds(posts));
          dispatch(setPostsIndices(postsMetadata.rowIndices));

          // set Asset Properties
          setAssetData(null);
          console.log("Data", allAssetData);
          let rowData = propertiesData.rows[0];
          let rowIndices = propertiesData.rowIndices;

          setAssetData(allAssetData.data_rows.rows);

          const initialAssetComponents = allAssetData.data_rows.rows.map(
            (row) => row["Component Tag Name"]
          );
          // Set the currentAssetComponents state
          console.log("initialAssetComponents", initialAssetComponents);
          setCurrentAssetComponents(initialAssetComponents);

          console.log("piping asset data", allAssetData.data_rows.rows);

          //set Inspection Data
          dispatch(setInspectionData(previousInspectionsData.rows));

          console.log("inspectionData", inspectionData);

          let inspRows = previousInspectionsData.rowIndices;
          setInspectionDataRows(inspRows);

          // create a list with each Inspection Date for each obejct in inspResponse.rows
          let inspectionDatesList = [];

          if (
            previousInspectionsData &&
            previousInspectionsData.rows &&
            Array.isArray(previousInspectionsData.rows)
          ) {
            inspectionDatesList = previousInspectionsData.rows.map(
              (row) => row["Inspection Date"]
            ); // Replace 'Inspection Date' with the exact field name in your objects
          }

          setPreviousInspectionDates(inspectionDatesList);

          console.log("Asset Data", assetData);

          const componentTagNames = assetData.map(
            (item) => item["Component Tag Name"]
          );
          console.log("Component Tag Names", componentTagNames);

          setGeneralFilesData(generalFilesData.rows);
          let generalRows = generalFilesData.rowIndices;
          setGeneralFilesDataRows(generalRows);

          setNcrsData(ncrsData.rows);
          let ncrsRows = ncrsData.rowIndices;
          setNcrsDataRows(ncrsRows);
          console.log("NCRS Rows", ncrsRows);

          setNozzlesData(nozzlesData.rows);
          let nozzleRows = nozzlesData.rowIndices;
          setNozzlesDataRows(nozzleRows);

          setPostDataRows(postsMetadata.rows);
          let postRows = postsMetadata.rowIndices;
          setPostDataRows(postRows);

          // Your existing code for handling focusedRow template types
          if (focusedRowDict.Template === "Piping") {
            const updatedIndices = rowIndices.map((index) => index + 5);
            setSelectedIndex(updatedIndices);
          }
          if (focusedRowDict.Template === "Pressure Vessel") {
            const updatedIndices = rowIndices.map((index) => index + 7);
            setSelectedIndex(updatedIndices);
          }
          if (focusedRowDict.Template === "Storage Tank") {
            const updatedIndices = rowIndices.map((index) => index + 6);
            setSelectedIndex(updatedIndices);
          }
          if (focusedRowDict.Template === "Additional Assets") {
            const updatedIndices = rowIndices.map((index) => index + 2);
            setSelectedIndex(updatedIndices);
          }
          if (focusedRowDict.Template === "PRD") {
            const updatedIndices = rowIndices.map((index) => index + 2);
            setSelectedIndex(updatedIndices);
          }

          dispatch(setSelectedAsset(rowData["Tag Name"]));
          console.log("currentTempaltehere", focusedRowDict.Template);
          dispatch(setSelectedAssetClass(focusedRowDict.Template));
          setselectedSheet(focusedRowDict.Template);

          // Process data and set properties and intents
          let newPropertyValues = {};
          let newInputIntents = {};

          const currentProperties = propertyDataTypes[focusedRowDict.Template];
          for (let key in currentProperties) {
            if (rowData.hasOwnProperty(key) && rowData[key]) {
              newPropertyValues[key] = rowData[key];
              newInputIntents[key] = Intent.SUCCESS;
            }
          }

          setPropertyValues(newPropertyValues);
          setInputIntents(newInputIntents);

          if (lookupValues) {
            let newSelectKeys = [];
            for (let key in currentProperties) {
              if (
                currentProperties[key].type === "Select" &&
                lookupValues.hasOwnProperty(key)
              ) {
                newSelectKeys.push(key);
              }
            }
            setSelectKeys(newSelectKeys);
          }
          console.log("Focused asset data:", rowData);
        } catch (error) {
          console.error("Error fetching data:", error);
          const message = `Error Fetching Data`;
          
          // Show toast
          if (myToaster.current) {
            myToaster.current.show({
              message: message,
              intent: Intent.WARNING,
              timeout: 4000,
              className: "font-size-11",
              icon: "warning-sign",
            });
          }

          // Add to action history
          dispatch(addAction({
            type: 'WARNING',
            message: message,
            source: 'Asset Loading'
          }));

          dispatch(setIsProgressBarLoading(false));
        } finally {
          dispatch(setIsProgressBarLoading(false));
        }
      };

      setTimeout(resolveAllActions, 1000); // 1-second delay before processing
    } catch (error) {
      console.error("Error initiating requests:", error);
      const message = `Error initiating requests`;
      
      // Show toast
      if (myToaster.current) {
        myToaster.current.show({
          message: message,
          intent: Intent.WARNING,
          timeout: 6000,
          className: "font-size-11",
          icon: "warning-sign",
        });
      }

      // Add to action history
      dispatch(addAction({
        type: 'WARNING',
        message: message,
        source: 'Asset Loading'
      }));

      dispatch(setIsProgressBarLoading(false));
    }
  };

  // Add useEffect here
  // Add other dependencies as needed

  function validateInput(value, type) {
    switch (type) {
      case "Text":
        return typeof value === "string";
      case "Numeric":
        return !isNaN(value); // Ensure value is not an empty string
      case "Date":
        if (value === "") return true; // Allow empty value for Date
        const datePattern =
          /^(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])\/\d{4}$|(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])\/\d{2}$/;
        return datePattern.test(value);
      // Add other types as needed
      default:
        return true; // default to true for types not explicitly checked
    }
  }

  const updateCell = async (
    property,
    value,
    singleRowIndex = null,
    template = selectedSheet
  ) => {
    console.log("value to update", property, value);
    console.log("Visible PDFs:", visibleViewers);
  
    // If value is null replace it with an empty string
    if (value.value === null) {
      value = "";
    }
  
    const validProperties = [
      "Plant",
      "Unit",
      "System",
      "Tag Name",
      "Asset Classification",
      "Equipment Type",
    ];
  
    let updateMadeForValidProperty = false;
  
    const makeUpdateRequest = async (indexRows, template, header, newValue) => {
      const url = `${process.env.REACT_APP_DATA_API}/data/update_cell/`;
      const options = {
        method: "PUT",
        params: {
          indexrows: indexRows,
          template: template,
          header: header,
          new_value: newValue,
          user: userEmail,
        },
      };
  
      try {
        const response = await makeApiCallWithRetry(url, options);
        console.log(response.data);
        const currentAsset = latestAssetClickParams["Tag Name"];
        const message = `${header} updated with value: ${newValue} at ${template} (${currentAsset})`;
        
        // Show toast
        myToaster.current?.show({
          message: message,
          intent: Intent.SUCCESS,
          timeout: 6000,
          className: "toaster-font",
          icon: "updated",
        });

        // Add to action history
        dispatch(addAction({
          type: 'SUCCESS',
          message: message,
          source: 'Cell Update'
        }));

        updateMadeForValidProperty = true;
  
        if (validProperties.includes(property)) {
          // Find the index of the row that needs to be updated
          const rowIndexToUpdate = data.findIndex(
            (row) =>
              row[4] === latestAssetClickParams["Tag Name"] &&
              row[10] === latestAssetClickParams["Template"]
          );
  
          console.log("currentData", data);
          console.log("Tag Name", latestAssetClickParams["Tag Name"]);
          console.log("Template", latestAssetClickParams["Template"]);
          console.log("Row Index to Update:", rowIndexToUpdate);
  
          if (rowIndexToUpdate !== -1) {
            const propertyMap = {
              Plant: 8,
              Unit: 9,
              "Equipment Type": 7,
              "Asset Classification": 6,
              "Tag Name": 5,
              System: 8,
            };
  
            const propertyIndex = propertyMap[property];
  
            // Update the data state
            const updatedData = [...data];
            updatedData[rowIndexToUpdate] = {
              ...updatedData[rowIndexToUpdate],
              [propertyIndex]: value,
            };
            setData(updatedData);
            console.log("updatedData", updatedData);
          } else {
            console.log(
              "No matching row found for the given Tag Name and Template."
            );
          }
        }
  
        // Call property_location_tracker API after successful update
        if (updateMadeForValidProperty && visibleViewers.length > 0) {
          await createPropertyLocationTracker(property, value, template);
        }
      } catch (error) {
        console.error("Error updating cell:", error);
        const currentAsset = latestAssetClickParams["Tag Name"];
        const message = `Error updating ${header} at ${template} (${currentAsset})`;
        
        // Show toast
        myToaster.current?.show({
          message: message,
          intent: Intent.WARNING,
          timeout: 6000,
          className: "font-size-11",
          icon: "warning-sign",
        });

        // Add to action history
        dispatch(addAction({
          type: 'WARNING',
          message: message,
          source: 'Cell Update'
        }));
      }
    };
  
    try {
      const indexToUse =
        singleRowIndex !== null ? [singleRowIndex] : selectedIndex;
      await makeUpdateRequest(indexToUse.join(","), template, property, value);
    } catch (error) {
      console.error("Error updating cell:", error);
    }
  
    if (validProperties.includes(property)) {
      const templates = [
        { name: "Previous Inspections", rows: inspectionDataRows },
        { name: "Nozzles", rows: nozzlesDataRows },
        { name: "Posts", rows: postDataRows },
        { name: "NCRs", rows: ncrsDataRows },
        { name: "General Files", rows: generalFilesDataRows },
      ];
  
      for (const { name, rows } of templates) {
        if (rows.length > 0) {
          try {
            const updatedIndices = rows.map((index) => index + 3).join(",");
            await makeUpdateRequest(updatedIndices, name, property, value);
          } catch (error) {
            console.error(`Error updating cell in ${name}:`, error);
          }
        }
      }
    }
  };
  
  const createPropertyLocationTracker = async (property, value, template) => {
    const url = `${process.env.REACT_APP_DATA_API}/property_location_tracker/`;
  
    const trackerPromises = visibleViewers.map(async (viewer) => {
      const data = {
        file_path: viewer.filePath,
        file_page: viewer.page,
        submitter: userEmail,
        tag_name: latestAssetClickParams["Tag Name"],
        plant: latestAssetClickParams["Plant"],
        unit: latestAssetClickParams["Unit"],
        system: latestAssetClickParams["System"],
        asset_classification: latestAssetClickParams["Asset Classification"],
        equipment_type: latestAssetClickParams["Equipment Type"],
        module: template,
        header_property: property,
        property_value: value
      };
  
      try {
        const response = await axios.post(url, data);
        console.log("Property location tracker created:", response.data);
        return response.data;
      } catch (error) {
        console.error("Error creating property location tracker:", error);
        throw error;
      }
    });
  
    try {
      const results = await Promise.all(trackerPromises);
      console.log("All property location trackers created:", results);
    } catch (error) {
      console.error("Error creating one or more property location trackers:", error);
    }
  };
  
  

  // Handler to update the Google Sheet cell when user hits Enter or onBlur
  const handlePropertyInput = (e, header) => {
    console.log("logging e", e);

    const inputValue = e.target.value || ""; // We can use the event object directly
    const propertyType = propertyDataTypes[selectedSheet][header].type;

    if (validateInput(inputValue, propertyType)) {
      // If input is valid
      inputIntents[header] = Intent.SUCCESS;
      updateCell(header, inputValue); // update the value only if valid
    } else {
      // If input is invalid
      inputIntents[header] = Intent.DANGER;
    }

    // Force the component to re-render
    setInputIntents({ ...inputIntents });
  };

  const handlePropertyChange = (id, value) => {
    setAssetData((assetData) => {
      return assetData.map((item) => {
        const updatedItem = { ...item, [id]: value };

        // Handle linked properties logic

        if (id === "Asset Classification") {
          setAssetClassification(value);
          // Clear the values of properties that are not "Asset Classification"
          Object.keys(propertyDataTypes[selectedSheet]).forEach((prop) => {
            if (
              prop !== "Asset Classification" &&
              propertyDataTypes[selectedSheet][prop].type === "SelectRule"
            ) {
              updatedItem[prop] = "";
            }
          });
        }

        return updatedItem;
      });
    });

    setPropertyValues((prevValues) => {
      const updatedValues = { ...prevValues, [id]: value };

      if (id === "Asset Classification") {
        // Clear the values of properties that are not "Asset Classification"
        Object.keys(propertyDataTypes[selectedSheet]).forEach((prop) => {
          if (
            prop !== "Asset Classification" &&
            propertyDataTypes[selectedSheet][prop].type === "SelectRule"
          ) {
            updatedValues[prop] = "";
          }
        });
      }

      return updatedValues;
    });

    setInputIntents((prevIntents) => ({
      ...prevIntents,
      [id]: Intent.PRIMARY,
    }));
  };
  let CardContent = (
    <>
      <div className="card-header">
        <h4 style={{ margin: "2px", fontWeight: "bold" }}>
          Components Properties
        </h4>
        <Button onClick={toggleExpand}>
          <FontAwesomeIcon icon={isExpanded ? faCompress : faExpand} />
        </Button>
      </div>

      <ComponentsData
        key={assetData.length}
        selectKeys={selectKeys}
        assetData={assetData}
        setAssetData={setAssetData}
        lookupDict={lookupValues}
        updateCell={updateCell}
        selectedSheet={selectedSheet}
        handleFocusAssetClick={handleFocusAssetClick}
        latestAssetClickParams={latestAssetClickParams}
        selectedWord={selectedWord}
        setSelectedWord={setSelectedWord}
        lastFocusedRef={lastFocusedRef}
        myToaster={myToaster}
      />
    </>
  );

  // Conditional rendering based on hideAssetInventory prop
  if (hideAssetInventory) {
    return (
      <div className="app-container">
        {isValidPropertyBeingChanged && (
          <div className="loading-overlay">
            <Spinner></Spinner>
          </div>
        )}
        {isProgressBarLoading && (
          <div id="datacollection" className="loading-overlay">
            <Spinner></Spinner>
          </div>
        )}
        <OverlayToaster ref={myToaster} />

        {/* Content without AssetInventory and SplitPane */}
        <div style={{ height: "100%", width: "100%" }}>
          <Card
            className="no-padding-card"
            style={{
              backgroundColor: isDarkTheme ? "#252A31" : "",
              padding: "0px",
              height: "50px",
            }}
          >
            <ButtonGroup
              fill={true}
              style={{ justifyContent: "center", height: "50px" }}
            >
              <Tooltip
                content="Asset Properties"
                position={Position.BOTTOM}
                intent="primary"
                className={
                  activeTab === "generalProperties" ? "activemodule" : ""
                }
              >
                <Button
                  height="50px"
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<MdOutlineList size={24} />}
                  active={activeTab === "generalProperties"}
                  onClick={() => dispatch(setActiveTab("generalProperties"))}
                  intent={
                    activeTab === "generalProperties"
                      ? Intent.PRIMARY
                      : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                intent="primary"
                content="Previous Inspections"
                position={Position.BOTTOM}
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<RxCalendar size={24} />}
                  active={activeTab === "previousInspections"}
                  onClick={() => dispatch(setActiveTab("previousInspections"))}
                  intent={
                    activeTab === "previousInspections"
                      ? Intent.PRIMARY
                      : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="NCRs"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<HiOutlineWrench size={24} />}
                  active={activeTab === "ncrsData"}
                  onClick={() => dispatch(setActiveTab("ncrsData"))}
                  intent={
                    activeTab === "ncrsData" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="General Files"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<ImFilesEmpty size={24} />}
                  active={activeTab === "assetFiles"}
                  onClick={() => dispatch(setActiveTab("assetFiles"))}
                  intent={
                    activeTab === "assetFiles" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>

              <Tooltip
                content={
                  assetClassification === "Piping" ? "Spools" : "Nozzles"
                }
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={
                    <NozzleIcon
                      isActive={activeTab === "Nozzles"}
                      isDarkTheme={isDarkTheme}
                      size={33}
                    />
                  }
                  active={activeTab === "Nozzles"}
                  onClick={() => dispatch(setActiveTab("Nozzles"))}
                  intent={
                    activeTab === "Nozzles" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="CML Readings"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<SlGraph size={24} />}
                  active={activeTab === "cmlReadings"}
                  onClick={() => dispatch(setActiveTab("cmlReadings"))}
                  intent={
                    activeTab === "cmlReadings" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="Comments"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<PiChats size={30} />}
                  active={activeTab === "Posts"}
                  onClick={() => dispatch(setActiveTab("Posts"))}
                  intent={activeTab === "Posts" ? Intent.PRIMARY : Intent.NONE}
                />
              </Tooltip>
            </ButtonGroup>
          </Card>

          <Card
            className="no-padding-card"
            style={{
              height: "calc(100vh - 150px)", // Full height minus navbar and tab bar
              overflowY: "auto",
            }}
          >
            {activeTab === "generalProperties" && (
              <GeneralProperties
                myToaster={myToaster}
                selectKeys={selectKeys}
                lookupValues={lookupValues}
                handlePropertyChange={handlePropertyChange}
                propertyValues={propertyValues}
                inputIntents={inputIntents}
                handlePropertyInput={handlePropertyInput}
                updateCell={updateCell}
                selectedSheet={selectedSheet}
                selectedWord={selectedWord}
                setSelectedWord={setSelectedWord}
                lastFocusedRef={lastFocusedRef}
              />
            )}

            {activeTab === "previousInspections" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>
                    Previous Inspections
                  </h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>

                <PreviousInspections
                  currentAssetComponents={currentAssetComponents}
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                  lookupDict={lookupValues}
                />
              </Card>
            )}
            {activeTab === "assetFiles" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>
                    General Files
                  </h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>

                <GeneralFiles
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  generalFilesData={generalFilesData}
                  setGeneralFilesData={setGeneralFilesData}
                  generalFilesDataRows={generalFilesDataRows}
                  setGeneralFilesDataRows={setGeneralFilesDataRows}
                  lookupDict={lookupValues}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                  myToaster={myToaster}
                />
              </Card>
            )}
            {activeTab === "ncrsData" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>NCRs</h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>
                <NcrData
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  ncrsData={ncrsData}
                  setNcrsData={setNcrsData}
                  lookupDict={lookupValues}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                />
              </Card>
            )}
            {activeTab === "Nozzles" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>
                    {assetClassification === "Piping" ? "Spools" : "Nozzles"}
                  </h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>

                <Nozzles
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  nozzlesData={nozzlesData}
                  setNozzleData={setNozzlesData}
                  lookupDict={lookupValues}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                  myToaster={myToaster}
                />
              </Card>
            )}
            {activeTab === "Posts" && (
              <Posts latestAssetClickParams={latestAssetClickParams} />
            )}

            {activeTab === "cmlReadings" && <CMLs />}
          </Card>
          {activeTab === "generalProperties" &&
            (selectedSheet === "Storage Tank" ||
              selectedSheet === "Pressure Vessel") &&
            (isExpanded ? (
              <Card
                style={{ height: "100vh-300px", width: "100%" }}
                className={`no-padding-card ${
                  isExpanded ? "expanded-card-middle" : ""
                }`}
              >
                {CardContent}
              </Card>
            ) : (
              <Card
                className="no-padding-card"
                style={{ height: "100vh", width: "100%" }}
              >
                {CardContent}
              </Card>
            ))}
        </div>
      </div>
    );
  }

  // Original return with SplitPane and AssetInventory
  return (
    <div className="app-container">
      {isValidPropertyBeingChanged && (
        <div className="loading-overlay">
          <Spinner></Spinner>
        </div>
      )}
      {isProgressBarLoading && (
        <div id="datacollection" className="loading-overlay">
          <Spinner></Spinner>
        </div>
      )}
      <OverlayToaster ref={myToaster} />
      <SplitPane
        split="horizontal"
        defaultSize={paneSize}
        maxSize="300px"
        minSize="1px"
      >
        <div style={{ width: "100%", height: "100%" }}>
          <AssetInventory
            placeholderData={placeholderData}
            headers={headers}
            isLoading={isLoading}
            searchTerm={searchTerm}
            isSearchFocused={isSearchFocused}
            data={data}
            setData={setData}
            setFocusedCell={setFocusedCell}
            setIsSearchFocused={setIsSearchFocused}
            handleFocusAssetClick={handleFocusAssetClick}
            focusedCell={focusedCell}
            setIsloading={setIsLoading}
            updateCell={updateCell}
            assetData={assetData}
            selectedSheet={selectedSheet}
            myToaster={myToaster}
            validPropertiesChange={validPropertiesChange}
            setValidPropertiesChange={setValidPropertiesChange}
            gridContainerRef={gridContainerRef}
          />
        </div>
        <div>
          <Card
            className="no-padding-card"
            style={{
              backgroundColor: isDarkTheme ? "#252A31" : "",
              padding: "0px",
              height: "50px",
            }}
          >
            <ButtonGroup
              fill={true}
              style={{ justifyContent: "center", height: "50px" }}
            >
              <Tooltip
                content="Asset Properties"
                position={Position.BOTTOM}
                intent="primary"
                className={
                  activeTab === "generalProperties" ? "activemodule" : ""
                }
              >
                <Button
                  height="50px"
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<MdOutlineList size={24} />}
                  active={activeTab === "generalProperties"}
                  onClick={() => dispatch(setActiveTab("generalProperties"))}
                  intent={
                    activeTab === "generalProperties"
                      ? Intent.PRIMARY
                      : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                intent="primary"
                content="Previous Inspections"
                position={Position.BOTTOM}
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<RxCalendar size={24} />}
                  active={activeTab === "previousInspections"}
                  onClick={() => dispatch(setActiveTab("previousInspections"))}
                  intent={
                    activeTab === "previousInspections"
                      ? Intent.PRIMARY
                      : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="NCRs"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<HiOutlineWrench size={24} />}
                  active={activeTab === "ncrsData"}
                  onClick={() => dispatch(setActiveTab("ncrsData"))}
                  intent={
                    activeTab === "ncrsData" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="General Files"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<ImFilesEmpty size={24} />}
                  active={activeTab === "assetFiles"}
                  onClick={() => dispatch(setActiveTab("assetFiles"))}
                  intent={
                    activeTab === "assetFiles" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>

              <Tooltip
                content={
                  assetClassification === "Piping" ? "Spools" : "Nozzles"
                }
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={
                    <NozzleIcon
                      isActive={activeTab === "Nozzles"}
                      isDarkTheme={isDarkTheme}
                      size={33}
                    />
                  }
                  active={activeTab === "Nozzles"}
                  onClick={() => dispatch(setActiveTab("Nozzles"))}
                  intent={
                    activeTab === "Nozzles" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="CML Readings"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<SlGraph size={24} />}
                  active={activeTab === "cmlReadings"}
                  onClick={() => dispatch(setActiveTab("cmlReadings"))}
                  intent={
                    activeTab === "cmlReadings" ? Intent.PRIMARY : Intent.NONE
                  }
                />
              </Tooltip>
              <Tooltip
                content="Comments"
                position={Position.BOTTOM}
                intent="primary"
              >
                <Button
                  className="full-size-button"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  icon={<PiChats size={30} />}
                  active={activeTab === "Posts"}
                  onClick={() => dispatch(setActiveTab("Posts"))}
                  intent={activeTab === "Posts" ? Intent.PRIMARY : Intent.NONE}
                />
              </Tooltip>
            </ButtonGroup>
          </Card>

          <Card
            className="no-padding-card"
            style={{
              height:
                selectedSheet === "Piping" ? "calc(100vh - 380px)" : "45vh",
              overflowY: "auto",
            }}
          >
            {activeTab === "generalProperties" && (
              <GeneralProperties
                myToaster={myToaster}
                selectKeys={selectKeys}
                lookupValues={lookupValues}
                handlePropertyChange={handlePropertyChange}
                propertyValues={propertyValues}
                inputIntents={inputIntents}
                handlePropertyInput={handlePropertyInput}
                updateCell={updateCell}
                selectedSheet={selectedSheet}
                selectedWord={selectedWord}
                setSelectedWord={setSelectedWord}
                lastFocusedRef={lastFocusedRef}
              />
            )}

            {activeTab === "previousInspections" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>
                    Previous Inspections
                  </h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>

                <PreviousInspections
                  currentAssetComponents={currentAssetComponents}
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                  lookupDict={lookupValues}
                />
              </Card>
            )}
            {activeTab === "assetFiles" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>
                    General Files
                  </h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>

                <GeneralFiles
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  generalFilesData={generalFilesData}
                  setGeneralFilesData={setGeneralFilesData}
                  generalFilesDataRows={generalFilesDataRows}
                  setGeneralFilesDataRows={setGeneralFilesDataRows}
                  lookupDict={lookupValues}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                  myToaster={myToaster}
                />
              </Card>
            )}
            {activeTab === "ncrsData" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>NCRs</h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>
                <NcrData
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  ncrsData={ncrsData}
                  setNcrsData={setNcrsData}
                  lookupDict={lookupValues}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                />
              </Card>
            )}
            {activeTab === "Nozzles" && (
              <Card
                className={`no-padding-card ${
                  isPreviousInspectionsExpanded ? "expanded-card" : ""
                }`}
                style={{
                  transform: `translate(${previousInspectionsPosition.x}px, ${previousInspectionsPosition.y}px)`,
                }}
              >
                <div className="card-header">
                  <h4 style={{ margin: "2px", fontWeight: "bold" }}>
                    {assetClassification === "Piping" ? "Spools" : "Nozzles"}
                  </h4>
                  <Button onClick={togglePreviousInspectionsExpand}>
                    <FontAwesomeIcon
                      icon={
                        isPreviousInspectionsExpanded ? faCompress : faExpand
                      }
                    />
                  </Button>
                </div>

                <Nozzles
                  assetData={assetData}
                  key={assetData.length}
                  selectKeys={selectKeys}
                  nozzlesData={nozzlesData}
                  setNozzleData={setNozzlesData}
                  lookupDict={lookupValues}
                  updateCell={updateCell}
                  selectedSheet={selectedSheet}
                  handleFocusAssetClick={handleFocusAssetClick}
                  latestAssetClickParams={latestAssetClickParams}
                  selectedWord={selectedWord}
                  setSelectedWord={setSelectedWord}
                  lastFocusedRef={lastFocusedRef}
                  setCurrentInspectionFiles={setCurrentInspectionFiles}
                  isDarkTheme={isDarkTheme}
                  myToaster={myToaster}
                />
              </Card>
            )}
            {activeTab === "Posts" && (
              <Posts latestAssetClickParams={latestAssetClickParams} />
            )}

            {activeTab === "cmlReadings" && <CMLs />}
          </Card>
          {activeTab === "generalProperties" &&
            (selectedSheet === "Storage Tank" ||
              selectedSheet === "Pressure Vessel") &&
            (isExpanded ? (
              <Card
                style={{ height: "100vh-300px", width: "100%" }}
                className={`no-padding-card ${
                  isExpanded ? "expanded-card-middle" : ""
                }`}
              >
                {CardContent}
              </Card>
            ) : (
              <Card
                className="no-padding-card"
                style={{ height: "100vh", width: "100%" }}
              >
                {CardContent}
              </Card>
            ))}
        </div>
      </SplitPane>
    </div>
  );
}

export default DataCollectionApp;
