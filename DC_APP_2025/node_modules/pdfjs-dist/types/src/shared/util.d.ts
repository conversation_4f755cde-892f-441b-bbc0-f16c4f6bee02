export function _isValidExplicitDest(validRef: any, validName: any, dest: any): boolean;
declare const AbortException_base: any;
/**
 * Error used to indicate task cancellation.
 */
export class AbortException extends AbortException_base {
    [x: string]: any;
    constructor(msg: any);
}
export namespace AnnotationActionEventType {
    let E: string;
    let X: string;
    let D: string;
    let U: string;
    let Fo: string;
    let Bl: string;
    let PO: string;
    let PC: string;
    let PV: string;
    let PI: string;
    let K: string;
    let F: string;
    let V: string;
    let C: string;
}
export namespace AnnotationBorderStyleType {
    let SOLID: number;
    let DASHED: number;
    let BEVELED: number;
    let INSET: number;
    let UNDERLINE: number;
}
export namespace AnnotationEditorParamsType {
    let RESIZE: number;
    let CREATE: number;
    let FREETEXT_SIZE: number;
    let FREETEXT_COLOR: number;
    let FREETEXT_OPACITY: number;
    let INK_COLOR: number;
    let INK_THICKNESS: number;
    let INK_OPACITY: number;
    let HIGHLIGHT_COLOR: number;
    let HIGHLIGHT_DEFAULT_COLOR: number;
    let HIGHLIGHT_THICKNESS: number;
    let HIGHLIGHT_FREE: number;
    let HIGHLIGHT_SHOW_ALL: number;
    let DRAW_STEP: number;
}
export const AnnotationEditorPrefix: "pdfjs_internal_editor_";
export namespace AnnotationEditorType {
    let DISABLE: number;
    let NONE: number;
    let FREETEXT: number;
    let HIGHLIGHT: number;
    let STAMP: number;
    let INK: number;
    let SIGNATURE: number;
}
export namespace AnnotationFieldFlag {
    let READONLY: number;
    let REQUIRED: number;
    let NOEXPORT: number;
    let MULTILINE: number;
    let PASSWORD: number;
    let NOTOGGLETOOFF: number;
    let RADIO: number;
    let PUSHBUTTON: number;
    let COMBO: number;
    let EDIT: number;
    let SORT: number;
    let FILESELECT: number;
    let MULTISELECT: number;
    let DONOTSPELLCHECK: number;
    let DONOTSCROLL: number;
    let COMB: number;
    let RICHTEXT: number;
    let RADIOSINUNISON: number;
    let COMMITONSELCHANGE: number;
}
export namespace AnnotationFlag {
    export let INVISIBLE: number;
    export let HIDDEN: number;
    export let PRINT: number;
    export let NOZOOM: number;
    export let NOROTATE: number;
    export let NOVIEW: number;
    let READONLY_1: number;
    export { READONLY_1 as READONLY };
    export let LOCKED: number;
    export let TOGGLENOVIEW: number;
    export let LOCKEDCONTENTS: number;
}
export namespace AnnotationMode {
    let DISABLE_1: number;
    export { DISABLE_1 as DISABLE };
    export let ENABLE: number;
    export let ENABLE_FORMS: number;
    export let ENABLE_STORAGE: number;
}
export const AnnotationPrefix: "pdfjs_internal_id_";
export namespace AnnotationReplyType {
    let GROUP: string;
    let REPLY: string;
}
export namespace AnnotationType {
    export let TEXT: number;
    export let LINK: number;
    let FREETEXT_1: number;
    export { FREETEXT_1 as FREETEXT };
    export let LINE: number;
    export let SQUARE: number;
    export let CIRCLE: number;
    export let POLYGON: number;
    export let POLYLINE: number;
    let HIGHLIGHT_1: number;
    export { HIGHLIGHT_1 as HIGHLIGHT };
    let UNDERLINE_1: number;
    export { UNDERLINE_1 as UNDERLINE };
    export let SQUIGGLY: number;
    export let STRIKEOUT: number;
    let STAMP_1: number;
    export { STAMP_1 as STAMP };
    export let CARET: number;
    let INK_1: number;
    export { INK_1 as INK };
    export let POPUP: number;
    export let FILEATTACHMENT: number;
    export let SOUND: number;
    export let MOVIE: number;
    export let WIDGET: number;
    export let SCREEN: number;
    export let PRINTERMARK: number;
    export let TRAPNET: number;
    export let WATERMARK: number;
    export let THREED: number;
    export let REDACT: number;
}
export function assert(cond: any, msg: any): void;
/**
 * @type {any}
 */
export const BaseException: any;
export const BASELINE_FACTOR: number;
export function bytesToString(bytes: any): string;
/**
 * Attempts to create a valid absolute URL.
 *
 * @param {URL|string} url - An absolute, or relative, URL.
 * @param {URL|string} [baseUrl] - An absolute URL.
 * @param {Object} [options]
 * @returns Either a valid {URL}, or `null` otherwise.
 */
export function createValidAbsoluteUrl(url: URL | string, baseUrl?: URL | string, options?: Object): URL | null;
export namespace DocumentActionEventType {
    let WC: string;
    let WS: string;
    let DS: string;
    let WP: string;
    let DP: string;
}
export namespace DrawOPS {
    let moveTo: number;
    let lineTo: number;
    let curveTo: number;
    let closePath: number;
}
export class FeatureTest {
    static get isLittleEndian(): any;
    static get isEvalSupported(): any;
    static get isOffscreenCanvasSupported(): any;
    static get isImageDecoderSupported(): any;
    static get platform(): any;
    static get isCSSRoundSupported(): any;
}
export const FONT_IDENTITY_MATRIX: number[];
declare const FormatError_base: any;
/**
 * Error caused during parsing PDF data.
 */
export class FormatError extends FormatError_base {
    [x: string]: any;
    constructor(msg: any);
}
export function fromBase64Util(str: any): any;
export function getModificationDate(date?: Date): string;
export function getUuid(): string;
export function getVerbosityLevel(): number;
export const hexNumbers: string[];
export namespace ImageKind {
    let GRAYSCALE_1BPP: number;
    let RGB_24BPP: number;
    let RGBA_32BPP: number;
}
export function info(msg: any): void;
declare const InvalidPDFException_base: any;
export class InvalidPDFException extends InvalidPDFException_base {
    [x: string]: any;
    constructor(msg: any);
}
export function isArrayEqual(arr1: any, arr2: any): boolean;
export const isNodeJS: any;
export const LINE_DESCENT_FACTOR: 0.35;
export const LINE_FACTOR: 1.35;
export function MathClamp(v: any, min: any, max: any): number;
export function normalizeUnicode(str: any): any;
export function objectSize(obj: any): number;
export namespace OPS {
    export let dependency: number;
    export let setLineWidth: number;
    export let setLineCap: number;
    export let setLineJoin: number;
    export let setMiterLimit: number;
    export let setDash: number;
    export let setRenderingIntent: number;
    export let setFlatness: number;
    export let setGState: number;
    export let save: number;
    export let restore: number;
    export let transform: number;
    let moveTo_1: number;
    export { moveTo_1 as moveTo };
    let lineTo_1: number;
    export { lineTo_1 as lineTo };
    let curveTo_1: number;
    export { curveTo_1 as curveTo };
    export let curveTo2: number;
    export let curveTo3: number;
    let closePath_1: number;
    export { closePath_1 as closePath };
    export let rectangle: number;
    export let stroke: number;
    export let closeStroke: number;
    export let fill: number;
    export let eoFill: number;
    export let fillStroke: number;
    export let eoFillStroke: number;
    export let closeFillStroke: number;
    export let closeEOFillStroke: number;
    export let endPath: number;
    export let clip: number;
    export let eoClip: number;
    export let beginText: number;
    export let endText: number;
    export let setCharSpacing: number;
    export let setWordSpacing: number;
    export let setHScale: number;
    export let setLeading: number;
    export let setFont: number;
    export let setTextRenderingMode: number;
    export let setTextRise: number;
    export let moveText: number;
    export let setLeadingMoveText: number;
    export let setTextMatrix: number;
    export let nextLine: number;
    export let showText: number;
    export let showSpacedText: number;
    export let nextLineShowText: number;
    export let nextLineSetSpacingShowText: number;
    export let setCharWidth: number;
    export let setCharWidthAndBounds: number;
    export let setStrokeColorSpace: number;
    export let setFillColorSpace: number;
    export let setStrokeColor: number;
    export let setStrokeColorN: number;
    export let setFillColor: number;
    export let setFillColorN: number;
    export let setStrokeGray: number;
    export let setFillGray: number;
    export let setStrokeRGBColor: number;
    export let setFillRGBColor: number;
    export let setStrokeCMYKColor: number;
    export let setFillCMYKColor: number;
    export let shadingFill: number;
    export let beginInlineImage: number;
    export let beginImageData: number;
    export let endInlineImage: number;
    export let paintXObject: number;
    export let markPoint: number;
    export let markPointProps: number;
    export let beginMarkedContent: number;
    export let beginMarkedContentProps: number;
    export let endMarkedContent: number;
    export let beginCompat: number;
    export let endCompat: number;
    export let paintFormXObjectBegin: number;
    export let paintFormXObjectEnd: number;
    export let beginGroup: number;
    export let endGroup: number;
    export let beginAnnotation: number;
    export let endAnnotation: number;
    export let paintImageMaskXObject: number;
    export let paintImageMaskXObjectGroup: number;
    export let paintImageXObject: number;
    export let paintInlineImageXObject: number;
    export let paintInlineImageXObjectGroup: number;
    export let paintImageXObjectRepeat: number;
    export let paintImageMaskXObjectRepeat: number;
    export let paintSolidColorImageMask: number;
    export let constructPath: number;
    export let setStrokeTransparent: number;
    export let setFillTransparent: number;
    export let rawFillPath: number;
}
export namespace PageActionEventType {
    export let O: string;
    let C_1: string;
    export { C_1 as C };
}
declare const PasswordException_base: any;
export class PasswordException extends PasswordException_base {
    [x: string]: any;
    constructor(msg: any, code: any);
    code: any;
}
export namespace PasswordResponses {
    let NEED_PASSWORD: number;
    let INCORRECT_PASSWORD: number;
}
export namespace PermissionFlag {
    let PRINT_1: number;
    export { PRINT_1 as PRINT };
    export let MODIFY_CONTENTS: number;
    export let COPY: number;
    export let MODIFY_ANNOTATIONS: number;
    export let FILL_INTERACTIVE_FORMS: number;
    export let COPY_FOR_ACCESSIBILITY: number;
    export let ASSEMBLE: number;
    export let PRINT_HIGH_QUALITY: number;
}
export namespace RenderingIntentFlag {
    export let ANY: number;
    export let DISPLAY: number;
    let PRINT_2: number;
    export { PRINT_2 as PRINT };
    export let SAVE: number;
    export let ANNOTATIONS_FORMS: number;
    export let ANNOTATIONS_STORAGE: number;
    export let ANNOTATIONS_DISABLE: number;
    export let IS_EDITING: number;
    export let OPLIST: number;
}
declare const ResponseException_base: any;
export class ResponseException extends ResponseException_base {
    [x: string]: any;
    constructor(msg: any, status: any, missing: any);
    status: any;
    missing: any;
}
export function setVerbosityLevel(level: any): void;
export function shadow(obj: any, prop: any, value: any, nonSerializable?: boolean): any;
export function string32(value: any): string;
export function stringToBytes(str: any): Uint8Array<any>;
export function stringToPDFString(str: any): string;
export function stringToUTF8String(str: any): string;
export namespace TextRenderingMode {
    export let FILL: number;
    export let STROKE: number;
    export let FILL_STROKE: number;
    let INVISIBLE_1: number;
    export { INVISIBLE_1 as INVISIBLE };
    export let FILL_ADD_TO_PATH: number;
    export let STROKE_ADD_TO_PATH: number;
    export let FILL_STROKE_ADD_TO_PATH: number;
    export let ADD_TO_PATH: number;
    export let FILL_STROKE_MASK: number;
    export let ADD_TO_PATH_FLAG: number;
}
export function toBase64Util(arr: any): any;
export function toHexUtil(arr: any): any;
declare const UnknownErrorException_base: any;
export class UnknownErrorException extends UnknownErrorException_base {
    [x: string]: any;
    constructor(msg: any, details: any);
    details: any;
}
export function unreachable(msg: any): void;
/**
 * Remove, or replace, the hash property of the URL.
 *
 * @param {URL|string} url - The absolute, or relative, URL.
 * @param {string} hash - The hash property (use an empty string to remove it).
 * @param {boolean} [allowRel] - Allow relative URLs.
 * @returns {string} The resulting URL string.
 */
export function updateUrlHash(url: URL | string, hash: string, allowRel?: boolean): string;
export function utf8StringToString(str: any): string;
export class Util {
    static makeHexColor(r: any, g: any, b: any): string;
    static scaleMinMax(transform: any, minMax: any): void;
    static transform(m1: any, m2: any): any[];
    static applyTransform(p: any, m: any, pos?: number): void;
    static applyTransformToBezier(p: any, transform: any, pos?: number): void;
    static applyInverseTransform(p: any, m: any): void;
    static axialAlignedBoundingBox(rect: any, transform: any, output: any): void;
    static inverseTransform(m: any): number[];
    static singularValueDecompose2dScale(matrix: any, output: any): void;
    static normalizeRect(rect: any): any;
    static intersect(rect1: any, rect2: any): number[] | null;
    static pointBoundingBox(x: any, y: any, minMax: any): void;
    static rectBoundingBox(x0: any, y0: any, x1: any, y1: any, minMax: any): void;
    static "__#1@#getExtremumOnCurve"(x0: any, x1: any, x2: any, x3: any, y0: any, y1: any, y2: any, y3: any, t: any, minMax: any): void;
    static "__#1@#getExtremum"(x0: any, x1: any, x2: any, x3: any, y0: any, y1: any, y2: any, y3: any, a: any, b: any, c: any, minMax: any): void;
    static bezierBoundingBox(x0: any, y0: any, x1: any, y1: any, x2: any, y2: any, x3: any, y3: any, minMax: any): void;
}
export namespace VerbosityLevel {
    let ERRORS: number;
    let WARNINGS: number;
    let INFOS: number;
}
export function warn(msg: any): void;
export {};
