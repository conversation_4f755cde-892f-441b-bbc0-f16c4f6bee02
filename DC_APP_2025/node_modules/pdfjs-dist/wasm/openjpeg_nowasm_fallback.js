/* THIS FILE IS GENERATED - DO NOT EDIT */
var OpenJPEG = (() => {
  var _scriptName = import.meta.url;
  
  return (
function(moduleArg = {}) {
  var moduleRtn;

var Module=moduleArg;var readyPromiseResolve,readyPromiseReject;var readyPromise=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptName){scriptDirectory=_scriptName}if(scriptDirectory.startsWith("blob:")){scriptDirectory=""}else{scriptDirectory=scriptDirectory.slice(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}{readAsync=async url=>{var response=await fetch(url,{credentials:"same-origin"});if(response.ok){return response.arrayBuffer()}throw new Error(response.status+" : "+response.url)}}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];var wasmBinary=Module["wasmBinary"];var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536)},Module:function(binary){},Instance:function(module,info){this.exports=(
// EMSCRIPTEN_START_ASM
function instantiate(Ea){function c(d){d.set=function(a,b){this[a]=b};d.get=function(a){return this[a]};return d}var e;var f=new Uint8Array(123);for(var a=25;a>=0;--a){f[48+a]=52+a;f[65+a]=a;f[97+a]=26+a}f[43]=62;f[47]=63;function l(m,n,o){var g,h,a=0,i=n,j=o.length,k=n+(j*3>>2)-(o[j-2]=="=")-(o[j-1]=="=");for(;a<j;a+=4){g=f[o.charCodeAt(a+1)];h=f[o.charCodeAt(a+2)];m[i++]=f[o.charCodeAt(a)]<<2|g>>4;if(i<k)m[i++]=g<<4|h>>2;if(i<k)m[i++]=h<<6|f[o.charCodeAt(a+3)]}return m}function p(q){l(e,1024,"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");l(e,20716,"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");l(e,24601,"AQABAAEAAQAAAQEAAAEBAAEAAQABAAEAAAAAAQEBAQAAAAAAAQABAAAAAAEBAQEAAAABAAEBAQ==");l(e,24665,"AQABAAEAAQAAAQEAAAEBAAEAAQABAAEAAAAAAQEBAQAAAAAAAQABAAAAAAEBAQEAAAABAAEBAQ==");l(e,24729,"AQABAAEAAQ==");l(e,24745,"AQABAAEAAQAAAAABAQEBAAAAAAABAAEAAAAAAQEBAQAAAAAAAQABAQEAAAEBAAAAAQABAAEAAQEBAQEBAQEBAAEAAQABAAEAAAAAAQEBAQABAAABAQABAAAAAAEBAQEAAQABAQEBAQIAAAAEAAAABAAAAAgAAACQ/wAADAAAABkAAABS/wAAFAAAABoAAABT/wAAFAAAABsAAABe/wAAFAAAABwAAABc/wAAFAAAAB0AAABd/wAAFAAAAB4AAABf/wAAFAAAAB8AAABR/wAAAgAAACAAAABV/wAABAAAACEAAABX/wAABAAAACIAAABY/wAAEAAAACMAAABg/wAABAAAACQAAABh/wAAEAAAACUAAACR/w==");l(e,25032,"Y/8AAAQAAAAmAAAAZP8AABQAAAAnAAAAdP8AABQAAAAoAAAAeP8AAAQAAAApAAAAUP8AAAQAAAAqAAAAWf8AAAQAAAArAAAAdf8AABQAAAAsAAAAd/8AABQAAAAtAAAAAAAAABQ=");l(e,25152,"LgAAAC8AAAAwAAAAMQAAADIAAAAzAAAANAAAADUAAAAgIFBqNwAAAHB5dGY4AAAAaDJwajk=");l(e,25216,"cmRoaToAAABybG9jOwAAAGNjcGI8AAAAcmxjcD0AAABwYW1jPgAAAGZlZGM/AAAAQGY=");l(e,25280,"GQALABkZGQAAAAAFAAAAAAAACQAAAAALAAAAAAAAAAAZAAoKGRkZAwoHAAEACQsYAAAJBgsAAAsABhkAAAAZGRk=");l(e,25361,"DgAAAAAAAAAAGQALDRkZGQANAAACAAkOAAAACQAOAAAO");l(e,25419,"DA==");l(e,25431,"EwAAAAATAAAAAAkMAAAAAAAMAAAM");l(e,25477,"EA==");l(e,25489,"DwAAAAQPAAAAAAkQAAAAAAAQAAAQ");l(e,25535,"Eg==");l(e,25547,"EQAAAAARAAAAAAkSAAAAAAASAAASAAAaAAAAGhoa");l(e,25602,"GgAAABoaGgAAAAAAAAk=");l(e,25651,"FA==");l(e,25663,"FwAAAAAXAAAAAAkUAAAAAAAUAAAU");l(e,25709,"Fg==");l(e,25721,"FQAAAAAVAAAAAAkWAAAAAAAWAAAWAAAwMTIzNDU2Nzg5QUJDREVGAAAAAHAAAABwAAAAcQAAAHEAAABxAAAAcQAAAHEAAABxAAAAcAAAAHAAAABxAAAAcAAAAHAAAABwAAAAcA==");l(e,25856,"cQAAAHEAAABwAAAAcAAAAAAAAABwAAAAAAAAAHE=");l(e,26024,"YHABAAAAAAAF");l(e,26044,"aw==");l(e,26068,"bAAAAG0AAADIaw==");l(e,26092,"Ag==");l(e,26108,"//////////8=");l(e,26176,"BQ==");l(e,26188,"bg==");l(e,26212,"bAAAAG8AAADYawAAAAQ=");l(e,26236,"AQ==");l(e,26252,"/////wo=")}var r=new ArrayBuffer(16);var s=new Int32Array(r);var t=new Float32Array(r);var u=new Float64Array(r);function v(w){return s[w]}function x(w,y){s[w]=y}function z(){return u[0]}function A(y){u[0]=y}function B(C,y,D){C=C>>>0;D=D>>>0;if(C+D>e.length)throw"trap: invalid memory.fill";e.fill(y,C,C+D)}function E(C,F,D){e.copyWithin(C,F,F+D)}function G(){throw new Error("abort")}function Da(q){var H=new ArrayBuffer(16908288);var I=new Int8Array(H);var J=new Int16Array(H);var K=new Int32Array(H);var L=new Uint8Array(H);var M=new Uint16Array(H);var N=new Uint32Array(H);var O=new Float32Array(H);var P=new Float64Array(H);var Q=Math.imul;var R=Math.fround;var S=Math.abs;var T=Math.clz32;var U=Math.min;var V=Math.max;var W=Math.floor;var X=Math.ceil;var Y=Math.trunc;var Z=Math.sqrt;var _=q.a;var $=_.a;var aa=_.b;var ba=_.c;var ca=_.d;var da=_.e;var ea=_.f;var fa=_.g;var ga=_.h;var ha=_.i;var ia=_.j;var ja=_.k;var ka=_.l;var la=_.m;var ma=_.n;var na=_.o;var oa=_.p;var pa=_.q;var qa=_.r;var ra=94304;var sa=0;var ta=0;var ua=0;
// EMSCRIPTEN_START_FUNCS
function jd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,C=0,D=0,F=0,G=0,H=0,P=0,S=0,U=0,V=0,W=0,X=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=R(0),ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,sa=0,ta=0,ua=0,wa=0;aa=ra-96|0;ra=aa;D=K[a+8>>2];a:{b:{c:{if(!K[a>>2]){g=Q(K[D+16>>2]-K[D+8>>2]|0,K[D+20>>2]-K[D+12>>2]|0)<<2;c=Ma(g);K[D+60>>2]=c;if(!c){Fa(K[a+32>>2],1,7986,0);d=a+28|0;break b}if(!g){break c}B(c,0,g);break c}c=K[D+60>>2];if(!c){break c}Ga(c);K[D+60>>2]=0}if(!K[K[a+28>>2]>>2]){break a}pa=K[a+16>>2];c=K[pa+28>>2]+Q(K[pa+24>>2],152)|0;ua=K[c-152>>2];wa=K[c-144>>2];qa=K[a+20>>2];sa=K[a+12>>2];ta=K[a+4>>2];d=a+28|0;d:{q=K[b+4>>2];e=0;e:{if((q|0)<=0){break e}l=K[b>>2];c=0;f:{while(1){g=l+Q(c,12)|0;if(!K[g>>2]){break f}c=c+1|0;if((q|0)!=(c|0)){continue}break}e=0;break e}e=K[g+4>>2]}if(e){break d}e=Ia(1,156);if(!e){Fa(K[a+32>>2],1,6276,0);break b}K[e+140>>2]=0;c=0;l=K[b+4>>2];g:{if((l|0)==2147483647){break g}g=K[b>>2];if((l|0)>0){while(1){q=g+Q(c,12)|0;if(!K[q>>2]){l=K[q+8>>2];if(l){va[l|0](K[q+4>>2]);g=K[b>>2]}b=g+Q(c,12)|0;K[b+8>>2]=15;K[b+4>>2]=e;c=1;break g}c=c+1|0;if((l|0)!=(c|0)){continue}break}}g=La(g,Q(l,12)+12|0);c=0;if(!g){break g}K[b>>2]=g;c=K[b+4>>2];g=g+Q(c,12)|0;K[g+8>>2]=15;K[g+4>>2]=e;K[g>>2]=0;K[b+4>>2]=c+1;c=1}if(c){break d}Fa(K[a+32>>2],1,8301,0);b=K[e+116>>2];if(b){Ga(b);K[e+116>>2]=0}b=K[e+120>>2];if(b){Ga(b);K[e+120>>2]=0}Ga(K[e+148>>2]);Ga(e);break b}K[e+144>>2]=K[a+24>>2];_=K[a+40>>2];ba=K[a+36>>2];S=K[a+32>>2];h=K[qa+808>>2];b=K[sa+16>>2];h:{Z=K[qa+16>>2];i:{if(Z&64){l=ra-304|0;ra=l;j:{if(h){if(ba){Fa(S,1,3182,0);break j}Fa(S,1,3182,0);break j}j=K[e+116>>2];c=K[D+20>>2]-K[D+12>>2]|0;b=K[D+16>>2]-K[D+8>>2]|0;g=Q(c,b);k:{l:{if(g>>>0>N[e+132>>2]){Ga(j);f=g<<2;j=Ma(f);K[e+116>>2]=j;if(!j){j=0;break j}K[e+132>>2]=g;break l}if(!j){break k}f=g<<2}if(!f){break k}B(j,0,f)}j=K[e+120>>2];m:{if(N[e+136>>2]>2639){break m}Ga(j);j=Ma(10560);K[e+120>>2]=j;if(j){break m}j=0;break j}K[e+136>>2]=2640;B(j,0,10560);K[e+128>>2]=c;K[e+124>>2]=b;n=K[D+24>>2];if(!n){j=1;break j}q=K[D+28>>2];j=1;n:{o:{p:{q:{f=K[D+52>>2];r:{if(f){c=K[D+4>>2];j=0;if(f>>>0>=4){b=f&-4;while(1){g=c+(m<<3)|0;j=K[g+28>>2]+(K[g+20>>2]+(K[g+12>>2]+(K[g+4>>2]+j|0)|0)|0)|0;m=m+4|0;x=x+4|0;if((b|0)!=(x|0)){continue}break}}b=f&3;if(b){while(1){j=K[(c+(m<<3)|0)+4>>2]+j|0;m=m+1|0;k=k+1|0;if((b|0)!=(k|0)){continue}break}}if(!K[e+144>>2]&(f|0)==1){break o}if(N[e+152>>2]>=j>>>0){break r}x=La(K[e+148>>2],j);if(x){break q}j=0;break j}if(!K[e+144>>2]){break j}}x=K[e+148>>2];if(x){break p}j=0;break j}K[e+152>>2]=j;K[e+148>>2]=x}if(!K[D+52>>2]){j=0;break n}f=K[D+4>>2];j=0;m=0;while(1){g=m<<3;c=g+f|0;b=K[c+4>>2];if(b){E(j+x|0,K[c>>2],b)}f=K[D+4>>2];j=K[(g+f|0)+4>>2]+j|0;m=m+1|0;if(m>>>0<N[D+52>>2]){continue}break}break n}x=K[K[D+4>>2]>>2]}m=0;f=0;c=K[D+40>>2];g=0;s:{if(!c){break s}b=K[D>>2];f=K[b+8>>2];g=0;if((c|0)==1){break s}g=K[b+32>>2]}c=n-q|0;f=f+g|0;t:{if(!f){k=0;break t}m=1;b=K[D>>2];s=K[b>>2];k=0;if((f|0)==1){m=0;break t}k=K[b+24>>2]}G=c+1|0;ia=K[e+116>>2];_=K[e+120>>2];A=K[D+12>>2];t=K[D+20>>2];F=K[D+8>>2];ja=K[D+16>>2];u:{v:{w:{x:{y:{z:{A:{B:{if(!(!m|k)){if(!ba){break B}Fa(S,2,10769,0);f=1;break A}if(f>>>0<4){break A}if(ba){K[l+112>>2]=f;Fa(S,1,9553,l+112|0);break u}K[l+96>>2]=f;Fa(S,1,9553,l+96|0);j=0;break j}Fa(S,2,10769,0);m=K[D+24>>2];if(m>>>0>30){break z}H=1;if(m>>>0>=G>>>0){break x}break v}m=K[D+24>>2];if(m>>>0<=30){break y}if(!ba){break z}K[l+32>>2]=K[D+24>>2];Fa(S,1,12265,l+32|0);break u}K[l>>2]=m;Fa(S,1,12265,l);j=0;break j}if(m>>>0<G>>>0){break w}if(f>>>0<2){H=f;break x}if((m|0)!=(G|0)){H=f;break x}H=1;if(L[26336]){break x}if(!ba){I[26336]=1;K[l+64>>2]=f;Fa(S,2,10262,l- -64|0);break x}if(!L[26336]){I[26336]=1;K[l+80>>2]=f;Fa(S,2,10262,l+80|0)}}if(!(!(s>>>0<2|j>>>0<s>>>0)&k+s>>>0<=j>>>0)){if(ba){j=0;Fa(S,1,9495,0);break j}j=0;Fa(S,1,9495,0);break j}U=s+x|0;b=L[U-1|0];j=b<<4|L[U-2|0]&15;if(!(!(j>>>0<2|(b|0)==255)&(j|0)<=(s|0))){if(ba){j=0;Fa(S,1,15268,0);break j}j=0;Fa(S,1,15268,0);break j}W=K[D+28>>2];K[l+272>>2]=0;K[l+280>>2]=0;K[l+264>>2]=0;K[l+268>>2]=0;K[l+296>>2]=0;K[l+300>>2]=0;K[l+284>>2]=0;K[l+288>>2]=0;c=j-1|0;K[l+276>>2]=c;f=(s+x|0)-j|0;K[l+256>>2]=f;q=L[f|0];b=8;K[l+272>>2]=8;i=f+1|0;K[l+256>>2]=i;g=j-2|0;K[l+276>>2]=g;n=(c|0)==1?q|15:q;c=0;q=c;K[l+264>>2]=n;K[l+268>>2]=c;K[l+280>>2]=!c&(n|0)==255;h=f&3;C:{D:{if((h|0)==3){break D}v=0;if(!((n|0)!=255|(c|0)!=0|L[i|0]<=143)){break C}c=255;c=j>>>0>=3?L[i|0]:c;m=j-3|0;K[l+276>>2]=m;f=!q&(n|0)==255;b=f?15:16;K[l+272>>2]=b;V=i+(j>>>0>2)|0;K[l+256>>2]=V;c=(g|0)==1?c|15:c;g=0;K[l+280>>2]=!g&(c|0)==255;g=c;i=n;c=f?7:8;f=c&31;if((c&63)>>>0>=32){P=i<<f;c=0}else{P=(1<<f)-1&i>>>32-f|q<<f;c=i<<f}n=c|g;c=w|P;q=c;K[l+264>>2]=n;K[l+268>>2]=c;if((h|0)==2){break D}f=255;v=0;if(!((g|0)!=255|(w|0)!=0|L[V|0]<=143)){break C}f=j>>>0>=4?L[V|0]:f;i=j-4|0;K[l+276>>2]=i;p=V+(j>>>0>3)|0;K[l+256>>2]=p;c=(m|0)==1?f|15:f;f=0;V=f;K[l+280>>2]=!f&(c|0)==255;f=!w&(g|0)==255;b=(f?7:8)+b|0;K[l+272>>2]=b;g=c;m=n;c=f?7:8;f=c&31;if((c&63)>>>0>=32){w=m<<f;c=0}else{w=(1<<f)-1&m>>>32-f|q<<f;c=m<<f}n=c|g;c=w|V;q=c;K[l+264>>2]=n;K[l+268>>2]=c;if((h|0)==1){break D}v=0;if(!((g|0)!=255|(V|0)!=0|L[p|0]<=143)){break C}c=255;c=j>>>0>=5?L[p|0]:c;K[l+276>>2]=j-5;K[l+256>>2]=p+(j>>>0>4);f=0;c=(i|0)==1?c|15:c;K[l+280>>2]=!f&(c|0)==255;g=!V&(g|0)==255;b=(g?7:8)+b|0;K[l+272>>2]=b;i=n;g=g?7:8;m=g&31;if((g&63)>>>0>=32){w=i<<m;g=0}else{w=(1<<m)-1&i>>>32-m|q<<m;g=i<<m}n=g|c;c=f|w;q=c;K[l+264>>2]=n;K[l+268>>2]=c}c=64-b|0;b=n;g=c&31;if((c&63)>>>0>=32){i=b<<g;b=0}else{i=(1<<g)-1&b>>>32-g|q<<g;b=b<<g}K[l+264>>2]=b;K[l+268>>2]=i;v=1}if(!v){if(ba){j=0;Fa(S,1,11433,0);break j}j=0;Fa(S,1,11433,0);break j}z=ja-F|0;i=j;p=i-2|0;K[l+244>>2]=p;V=s+x|0;c=V-3|0;K[l+224>>2]=c;b=L[V-2|0];f=b>>>0>143;K[l+248>>2]=f;q=0;n=b>>>4|0;K[l+232>>2]=n;K[l+236>>2]=0;v=(n&7)==7?3:4;K[l+240>>2]=v;b=(c&3)+1|0;r=b>>>0<p>>>0?b:p;E:{F:{if(!p){j=0;K[l+244>>2]=p-r;break F}b=V-4|0;K[l+224>>2]=b;g=L[c|0];j=g>>>0>143;K[l+248>>2]=j;q=v&31;if((v&63)>>>0>=32){w=g<<q;q=0}else{w=(1<<q)-1&g>>>32-q;q=g<<q}n=q|n;K[l+232>>2]=n;q=w;K[l+236>>2]=q;v=(f?(g&127)==127?7:8:8)+v|0;K[l+240>>2]=v;G:{if(r>>>0<2){f=j;break G}j=V-5|0;K[l+224>>2]=j;c=L[b|0];f=c>>>0>143;K[l+248>>2]=f;m=v&31;if((v&63)>>>0>=32){P=c<<m;u=0}else{P=(1<<m)-1&c>>>32-m;u=c<<m}n=u|n;K[l+232>>2]=n;q=q|P;K[l+236>>2]=q;v=(g>>>0<=143?8:(c&127)==127?7:8)+v|0;K[l+240>>2]=v;if((r|0)==2){c=b;b=j;break G}g=V-6|0;K[l+224>>2]=g;b=L[j|0];m=b;f=b>>>0>143;K[l+248>>2]=f;h=v&31;if((v&63)>>>0>=32){w=b<<h;u=0}else{w=(1<<h)-1&b>>>32-h;u=b<<h}n=u|n;K[l+232>>2]=n;q=q|w;K[l+236>>2]=q;v=(c>>>0<=143?8:(b&127)==127?7:8)+v|0;K[l+240>>2]=v;if((r|0)==3){c=j;b=g;break G}b=V-7|0;K[l+224>>2]=b;c=L[g|0];f=c>>>0>143;K[l+248>>2]=f;j=v&31;if((v&63)>>>0>=32){P=c<<j;j=0}else{P=(1<<j)-1&c>>>32-j;j=c<<j}n=j|n;j=q|P;q=j;K[l+232>>2]=n;K[l+236>>2]=j;v=(m>>>0<=143?8:(c&127)==127?7:8)+v|0;K[l+240>>2]=v;c=g}g=p-r|0;K[l+244>>2]=g;if(v>>>0>32){break E}if((g|0)>=4){j=K[c-4>>2];K[l+224>>2]=c-5;K[l+244>>2]=g-4;break F}if((g|0)<=0){j=0;break F}p=g&1;H:{if((r|0)==(i-3|0)){h=24;j=0;break H}V=g&2147483646;h=24;j=0;c=b;r=0;while(1){w=c-1|0;K[l+224>>2]=w;m=L[c|0];b=c-2|0;K[l+224>>2]=b;K[l+244>>2]=g-1;c=L[w|0];g=g-2|0;K[l+244>>2]=g;j=m<<h|j|c<<h-8;h=h-16|0;c=b;r=r+2|0;if((V|0)!=(r|0)){continue}break}}if(!p){break F}K[l+224>>2]=b-1;b=L[b|0];K[l+244>>2]=g-1;j=b<<h|j}w=j&255;K[l+248>>2]=w>>>0>143;g=f?(j&2130706432)==2130706432?7:8:8;c=g+(j>>>0<=2415919103?8:(j&8323072)==8323072?7:8)|0;m=j>>>16&255;b=c+(m>>>0<=143?8:(j&32512)==32512?7:8)|0;h=j>>>8&255;K[l+240>>2]=b+((h>>>0<=143?8:(j&127)==127?7:8)+v|0);b=m<<g|j>>>24|h<<c|w<<b;c=v&31;if((v&63)>>>0>=32){w=b<<c;b=0}else{w=(1<<c)-1&b>>>32-c;b=b<<c}K[l+232>>2]=b|n;K[l+236>>2]=q|w}nc(l+192|0,x,s-i|0,255);V=0;I:{if(H>>>0<2){break I}nc(l+160|0,U,k,0);V=0;if((H|0)==2){break I}n=0;q=0;f=0;K[l+152>>2]=1;K[l+144>>2]=0;K[l+136>>2]=0;K[l+140>>2]=0;b=k-1|0;K[l+148>>2]=b;c=(s+x|0)+k|0;g=c-1|0;K[l+128>>2]=g;m=g&3;J:{if((k|0)<=0){c=g;break J}c=c-2|0;K[l+128>>2]=c;n=L[g|0]}K[l+136>>2]=n;K[l+140>>2]=0;h=n>>>0>143;K[l+152>>2]=h;v=(n&127)==127?7:8;K[l+144>>2]=v;K:{if(!m){break K}s=k-2|0;K[l+148>>2]=s;L:{if((k|0)<2){j=c;break L}j=c-1|0;K[l+128>>2]=j;f=L[c|0]}h=f>>>0>143;K[l+152>>2]=h;c=v&31;if((v&63)>>>0>=32){i=f<<c;c=0}else{i=(1<<c)-1&f>>>32-c;c=f<<c}q=c|n;K[l+136>>2]=q;c=i;K[l+140>>2]=c;v=(n>>>0<=143?8:(f&127)==127?7:8)+v|0;K[l+144>>2]=v;if((m|0)==1){c=j;n=q;q=i;k=b;b=s;break K}i=k-3|0;K[l+148>>2]=i;M:{if((k|0)<3){g=j;break M}g=j-1|0;K[l+128>>2]=g;X=L[j|0]}h=X>>>0>143;K[l+152>>2]=h;b=v&31;if((v&63)>>>0>=32){P=X<<b;b=0}else{P=(1<<b)-1&X>>>32-b;b=X<<b}n=b|q;b=c|P;q=b;K[l+136>>2]=n;K[l+140>>2]=b;v=(f>>>0<=143?8:(X&127)==127?7:8)+v|0;K[l+144>>2]=v;if((m|0)==2){c=g;k=s;b=i;break K}b=k-4|0;K[l+148>>2]=b;f=0;N:{if((k|0)<4){c=g;break N}c=g-1|0;K[l+128>>2]=c;f=L[g|0]}h=f>>>0>143;K[l+152>>2]=h;g=v&31;if((v&63)>>>0>=32){w=f<<g;g=0}else{w=(1<<g)-1&f>>>32-g;g=f<<g}n=g|n;g=q|w;q=g;K[l+136>>2]=n;K[l+140>>2]=g;v=(X>>>0<=143?8:(f&127)==127?7:8)+v|0;K[l+144>>2]=v;k=i}if(v>>>0<=32){O:{if((k|0)>=5){j=K[c-3>>2];K[l+148>>2]=k-5;K[l+128>>2]=c-4;break O}j=0;if((k|0)<2){break O}k=24;while(1){f=c-1|0;K[l+128>>2]=f;c=L[c|0];g=b-1|0;K[l+148>>2]=g;j=c<<k|j;i=b>>>0>1;c=f;k=k-8|0;b=g;if(i){continue}break}}i=j&255;K[l+152>>2]=i>>>0>143;g=h?(j&2130706432)==2130706432?7:8:8;c=g+(j>>>0<=2415919103?8:(j&8323072)==8323072?7:8)|0;k=j>>>16&255;b=c+(k>>>0<=143?8:(j&32512)==32512?7:8)|0;f=j>>>8&255;K[l+144>>2]=b+((f>>>0<=143?8:(j&127)==127?7:8)+v|0);b=k<<g|j>>>24|f<<c|i<<b;c=v&31;if((v&63)>>>0>=32){i=b<<c;b=0}else{i=(1<<c)-1&b>>>32-c;b=b<<c}K[l+136>>2]=b|n;K[l+140>>2]=i|q}V=1}ca=t-A|0;y=G+1|0;I[_+2112|0]=0;X=_+2112|0;g=cb(l+256|0);if((z|0)>0){U=W-1|0;c=_;f=X;b=ia;x=0;while(1){s=x;m=M[(o<<8|(pb(l+224|0)&127)<<1)+16608>>1];P:{if(o){break P}j=g-2|0;m=(j|0)==-1?m:0;if((g|0)>1){g=j;break P}g=cb(l+256|0)}q=K[l+236>>2];n=K[l+232>>2];j=K[l+240>>2];r=m>>>4|0;h=K[c>>2]|(r&3|m>>>2&48)<<Y;K[c>>2]=h;p=m&16;o=m>>>5&7|p>>>4;k=j;j=m&7;x=k-j|0;n=((1<<j)-1&q)<<32-j|n>>>j;q=q>>>j|0;k=n;j=0;if((z|0)>(s|2)){j=M[(o<<8|(k&127)<<1)+16608>>1];Q:{if(o){break Q}k=g-2|0;j=(k|0)==-1?j:0;if((g|0)>1){g=k;break Q}g=cb(l+256|0)}k=j&7;x=x-k|0;o=j>>>4&1|j>>>5&7;n=((1<<k)-1&q)<<32-k|n>>>k;q=q>>>k|0;k=n}K[c>>2]=h|(j<<2&768|j&48)<<Y+4;v=j>>>2&2|m>>>3&1;R:{if((v|0)!=3){break R}i=g-2|0;v=(i|0)==-1?4:3;if((g|0)>1){g=i;break R}g=cb(l+256|0)}S:{if(!v){K[l+120>>2]=1;K[l+124>>2]=1;k=0;break S}if(v>>>0<=2){i=L[(k&7)+20756|0];w=i>>>2&7;h=i&3;i=(((-1<<w^-1)&k>>>h)+(i>>>5|0)|0)+1|0;k=(v|0)==1;K[l+124>>2]=k?1:i;K[l+120>>2]=k?i:1;k=h+w|0;break S}i=k;k=L[(k&7)+20756|0];A=k&3;i=i>>>A|0;if((v|0)==3){v=(k>>>5|0)+1|0;if((A|0)==3){K[l+124>>2]=i&1|2;k=k>>>2&7;K[l+120>>2]=v+((-1<<k^-1)&i>>>1);k=k+4|0;break S}w=L[(i&7)+20756|0];h=w&3;i=i>>>h|0;t=k>>>2&7;K[l+120>>2]=v+(i&(-1<<t^-1));k=w>>>2&7;K[l+124>>2]=(((-1<<k^-1)&i>>>t)+(w>>>5|0)|0)+1;k=k+(h+(t+A|0)|0)|0;break S}w=L[(i&7)+20756|0];h=w&3;i=i>>>h|0;t=k>>>2&7;K[l+120>>2]=((i&(-1<<t^-1))+(k>>>5|0)|0)+3;k=w>>>2&7;K[l+124>>2]=(((-1<<k^-1)&i>>>t)+(w>>>5|0)|0)+3;k=k+(t+(h+A|0)|0)|0}T:{A=K[l+120>>2];if(A>>>0<=y>>>0){t=K[l+124>>2];if(t>>>0<=y>>>0){break T}}if(ba){j=0;Fa(S,1,15719,0);break j}j=0;Fa(S,1,15719,0);break j}K[l+240>>2]=x-k;i=k&31;if((k&63)>>>0>=32){w=0;q=q>>>i|0}else{w=q>>>i|0;q=((1<<i)-1&q)<<32-i|n>>>i}K[l+232>>2]=q;K[l+236>>2]=w;k=j&240|r&15;x=s+4|0;q=(x|0)<=(z|0)?255:255>>>(x-z<<1)|0;r=(ca|0)>1?q:q&85;if(k&(r^-1)){if(ba){j=0;Fa(S,1,12157,0);break j}j=0;Fa(S,1,12157,0);break j}U:{V:{if(p){n=Qa(l+192|0);i=A+(m<<19>>31)|0;K[l+208>>2]=K[l+208>>2]-i;k=K[l+204>>2];q=K[l+200>>2];h=i&31;if((i&63)>>>0>=32){w=0;q=k>>>h|0}else{w=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}K[l+200>>2]=q;K[l+204>>2]=w;v=(n&(-1<<i^-1)|(m>>>8&1)<<i|1)+2<<U|n<<31;break V}v=0;if(!(r&1)){break U}}K[b>>2]=v}W:{if(m&32){n=Qa(l+192|0);i=A+(m<<18>>31)|0;K[l+208>>2]=K[l+208>>2]-i;k=K[l+204>>2];q=K[l+200>>2];h=i&31;if((i&63)>>>0>=32){w=0;q=k>>>h|0}else{w=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}K[l+200>>2]=q;K[l+204>>2]=w;q=n&(-1<<i^-1)|(m>>>9&1)<<i|1;K[(z<<2)+b>>2]=q+2<<U|n<<31;n=32-T(q)|0;q=L[f|0]&127;I[f|0]=(n>>>0>q>>>0?n:q)|128;break W}if(!(r&2)){break W}K[(z<<2)+b>>2]=0}i=b+4|0;X:{Y:{if(m&64){n=Qa(l+192|0);h=A+(m<<17>>31)|0;K[l+208>>2]=K[l+208>>2]-h;k=K[l+204>>2];q=K[l+200>>2];p=h&31;if((h&63)>>>0>=32){w=0;q=k>>>p|0}else{w=k>>>p|0;q=((1<<p)-1&k)<<32-p|q>>>p}K[l+200>>2]=q;K[l+204>>2]=w;v=(n&(-1<<h^-1)|(m>>>10&1)<<h|1)+2<<U|n<<31;break Y}v=0;if(!(r&4)){break X}}K[i>>2]=v}I[f+1|0]=0;Z:{if(m&128){n=Qa(l+192|0);h=A+(m<<16>>31)|0;K[l+208>>2]=K[l+208>>2]-h;k=K[l+204>>2];q=K[l+200>>2];p=h&31;if((h&63)>>>0>=32){w=0;q=k>>>p|0}else{w=k>>>p|0;q=((1<<p)-1&k)<<32-p|q>>>p}K[l+200>>2]=q;K[l+204>>2]=w;q=n&(-1<<h^-1)|(m>>>11&1)<<h|1;K[i+(z<<2)>>2]=q+2<<U|n<<31;I[f+1|0]=-96-T(q);break Z}if(!(r&8)){break Z}K[i+(z<<2)>>2]=0}i=b+8|0;_:{$:{if(j&16){n=Qa(l+192|0);m=t+(j<<19>>31)|0;K[l+208>>2]=K[l+208>>2]-m;k=K[l+204>>2];q=K[l+200>>2];h=m&31;if((m&63)>>>0>=32){w=0;q=k>>>h|0}else{w=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}K[l+200>>2]=q;K[l+204>>2]=w;v=(n&(-1<<m^-1)|(j>>>8&1)<<m|1)+2<<U|n<<31;break $}v=0;if(!(r&16)){break _}}K[i>>2]=v}aa:{if(j&32){n=Qa(l+192|0);m=t+(j<<18>>31)|0;K[l+208>>2]=K[l+208>>2]-m;k=K[l+204>>2];q=K[l+200>>2];h=m&31;if((m&63)>>>0>=32){w=0;q=k>>>h|0}else{w=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}K[l+200>>2]=q;K[l+204>>2]=w;q=n&(-1<<m^-1)|(j>>>9&1)<<m|1;K[i+(z<<2)>>2]=q+2<<U|n<<31;n=32-T(q)|0;q=L[f+1|0]&127;I[f+1|0]=(n>>>0>q>>>0?n:q)|128;break aa}if(!(r&32)){break aa}K[i+(z<<2)>>2]=0}i=b+12|0;ba:{ca:{if(j&64){n=Qa(l+192|0);m=t+(j<<17>>31)|0;K[l+208>>2]=K[l+208>>2]-m;k=K[l+204>>2];q=K[l+200>>2];h=m&31;if((m&63)>>>0>=32){w=0;q=k>>>h|0}else{w=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}K[l+200>>2]=q;K[l+204>>2]=w;v=(n&(-1<<m^-1)|(j>>>10&1)<<m|1)+2<<U|n<<31;break ca}v=0;if(!(r&64)){break ba}}K[i>>2]=v}f=f+2|0;I[f|0]=0;da:{if(j&128){n=Qa(l+192|0);m=t+(j<<16>>31)|0;K[l+208>>2]=K[l+208>>2]-m;k=K[l+204>>2];q=K[l+200>>2];h=m&31;if((m&63)>>>0>=32){w=0;q=k>>>h|0}else{w=k>>>h|0;q=((1<<h)-1&k)<<32-h|q>>>h}K[l+200>>2]=q;K[l+204>>2]=w;j=n&(-1<<m^-1)|(j>>>11&1)<<m|1;K[i+(z<<2)>>2]=j+2<<U|n<<31;I[f|0]=-96-T(j);break da}if(r>>>0<128){break da}K[i+(z<<2)>>2]=0}Y=Y^16;c=(s&4)+c|0;b=b+16|0;if((x|0)<(z|0)){continue}break}}ma=Z&8;ka=_+1584|0;la=_+1056|0;ga=_+528|0;if((ca|0)>=3){na=Q(z,12);oa=z<<3;fa=W-1|0;b=W-2|0;C=3<<b;da=1<<b;u=(z+7>>>1&2147483644)+4|0;p=2;while(1){Z=p;v=L[X|0];I[X|0]=0;Y=Y&-17^2;ea:{if((z|0)<=0){p=p+2|0;break ea}o=Z&4?ga:_;p=Z+2|0;f=ia+(Q(z,Z)<<2)|0;t=0;b=X;s=0;while(1){h=s;v=v&255;c=L[b+1|0]>>>5&4|(v>>>7|t);m=M[(c<<8|(pb(l+224|0)&127)<<1)+18656>>1];fa:{if(c){break fa}c=g-2|0;m=(c|0)==-1?m:0;if((g|0)>1){g=c;break fa}g=cb(l+256|0)}j=K[l+236>>2];q=K[l+232>>2];c=K[l+240>>2];k=K[o>>2]|(m>>>4&3|m>>>2&48)<<Y;K[o>>2]=k;U=m&64;A=m&128;t=U>>>5|A>>>6;n=c;c=m&7;r=n-c|0;n=((1<<c)-1&j)<<32-c|q>>>c;q=j>>>c|0;s=n;j=0;if((z|0)>(h|2)){c=L[b+2|0]>>>5&4|L[b+1|0]>>>7|t;j=M[(c<<8|(n&127)<<1)+18656>>1];ga:{if(c){break ga}c=g-2|0;j=(c|0)==-1?j:0;if((g|0)>1){g=c;break ga}g=cb(l+256|0)}c=j&7;r=r-c|0;t=(j>>>5|j>>>6)&2;n=((1<<c)-1&q)<<32-c|n>>>c;s=n;q=q>>>c|0}K[o>>2]=k|(j<<2&768|j&48)<<Y+4;k=1;c=1;ha:{ia:{ja:{x=j>>>2&2|m>>>3&1;switch(x|0){case 0:break ha;case 3:break ia;default:break ja}}c=L[(s&7)+20756|0];w=c>>>2&7;k=s;s=c&3;i=(((-1<<w^-1)&k>>>s)+(c>>>5|0)|0)+1|0;c=(x|0)==1;k=c?1:i;c=c?i:1;x=s+w|0;break ha}P=L[(s&7)+20756|0];k=P&3;c=s>>>k|0;G=L[(c&7)+20756|0];w=G&3;i=G>>>2&7;s=P>>>2&7;x=i+(s+(k+w|0)|0)|0;k=c>>>w|0;c=((k&(-1<<s^-1))+(P>>>5|0)|0)+1|0;k=(((-1<<i^-1)&k>>>s)+(G>>>5|0)|0)+1|0}K[l+240>>2]=r-x;i=x&31;if((x&63)>>>0>=32){w=0;q=q>>>i|0}else{w=q>>>i|0;q=((1<<i)-1&q)<<32-i|n>>>i}K[l+232>>2]=q;K[l+236>>2]=w;s=m&240;if(s-1&s){n=c;q=v&127;c=L[b+1|0]&127;q=c>>>0<q>>>0?q:c;c=q-2|0;c=n+(c>>>0<=q>>>0?c:0)|0}i=j&240;if(i-1&i){n=L[b+1|0]&127;q=L[b+2|0]&127;q=n>>>0>q>>>0?n:q;k=(q>>>0>2?q-2|0:0)+k|0}if(!(c>>>0<=y>>>0&k>>>0<=y>>>0)){if(ba){j=0;Fa(S,1,15819,0);break j}j=0;Fa(S,1,15819,0);break j}v=L[b+2|0];I[b+1|0]=0;I[b+2|0]=0;n=i|s>>>4;s=h+4|0;q=(s|0)<=(z|0)?255:255>>>(s-z<<1)|0;G=(p|0)>(ca|0)?q&85:q;if(n&(G^-1)){if(ba){j=0;Fa(S,1,12157,0);break j}j=0;Fa(S,1,12157,0);break j}ka:{la:{if(m&16){n=Qa(l+192|0);r=(m<<19>>31)+c|0;K[l+208>>2]=K[l+208>>2]-r;i=K[l+204>>2];q=K[l+200>>2];x=r&31;if((r&63)>>>0>=32){w=0;q=i>>>x|0}else{w=i>>>x|0;q=((1<<x)-1&i)<<32-x|q>>>x}K[l+200>>2]=q;K[l+204>>2]=w;r=(n&(-1<<r^-1)|(m>>>8&1)<<r|1)+2<<fa|n<<31;break la}r=0;if(!(G&1)){break ka}}K[f>>2]=r}ma:{if(m&32){n=Qa(l+192|0);r=(m<<18>>31)+c|0;K[l+208>>2]=K[l+208>>2]-r;i=K[l+204>>2];q=K[l+200>>2];x=r&31;if((r&63)>>>0>=32){w=0;q=i>>>x|0}else{w=i>>>x|0;q=((1<<x)-1&i)<<32-x|q>>>x}K[l+200>>2]=q;K[l+204>>2]=w;q=n&(-1<<r^-1)|(m>>>9&1)<<r|1;K[(z<<2)+f>>2]=q+2<<fa|n<<31;n=32-T(q)|0;q=L[b|0]&127;I[b|0]=(n>>>0>q>>>0?n:q)|128;break ma}if(!(G&2)){break ma}K[(z<<2)+f>>2]=0}r=f+4|0;na:{oa:{if(U){n=Qa(l+192|0);x=(m<<17>>31)+c|0;K[l+208>>2]=K[l+208>>2]-x;i=K[l+204>>2];q=K[l+200>>2];U=x&31;if((x&63)>>>0>=32){w=0;q=i>>>U|0}else{w=i>>>U|0;q=((1<<U)-1&i)<<32-U|q>>>U}K[l+200>>2]=q;K[l+204>>2]=w;$=(n&(-1<<x^-1)|(m>>>10&1)<<x|1)+2<<fa|n<<31;break oa}$=0;if(!(G&4)){break na}}K[r>>2]=$}pa:{if(A){q=Qa(l+192|0);i=(m<<16>>31)+c|0;K[l+208>>2]=K[l+208>>2]-i;n=K[l+204>>2];c=K[l+200>>2];x=i&31;if((i&63)>>>0>=32){w=0;c=n>>>x|0}else{w=n>>>x|0;c=((1<<x)-1&n)<<32-x|c>>>x}K[l+200>>2]=c;K[l+204>>2]=w;c=q&(-1<<i^-1)|(m>>>11&1)<<i|1;K[r+(z<<2)>>2]=c+2<<fa|q<<31;I[b+1|0]=-96-T(c);break pa}if(!(G&8)){break pa}K[r+(z<<2)>>2]=0}i=f+8|0;qa:{ra:{if(j&16){q=Qa(l+192|0);m=(j<<19>>31)+k|0;K[l+208>>2]=K[l+208>>2]-m;n=K[l+204>>2];c=K[l+200>>2];r=m&31;if((m&63)>>>0>=32){w=0;c=n>>>r|0}else{w=n>>>r|0;c=((1<<r)-1&n)<<32-r|c>>>r}K[l+200>>2]=c;K[l+204>>2]=w;c=(q&(-1<<m^-1)|(j>>>8&1)<<m|1)+2<<fa|q<<31;break ra}c=0;if(!(G&16)){break qa}}K[i>>2]=c}sa:{if(j&32){q=Qa(l+192|0);m=(j<<18>>31)+k|0;K[l+208>>2]=K[l+208>>2]-m;n=K[l+204>>2];c=K[l+200>>2];r=m&31;if((m&63)>>>0>=32){w=0;c=n>>>r|0}else{w=n>>>r|0;c=((1<<r)-1&n)<<32-r|c>>>r}K[l+200>>2]=c;K[l+204>>2]=w;c=q&(-1<<m^-1)|(j>>>9&1)<<m|1;K[i+(z<<2)>>2]=c+2<<fa|q<<31;q=32-T(c)|0;c=L[b+1|0]&127;I[b+1|0]=(c>>>0<q>>>0?q:c)|128;break sa}if(!(G&32)){break sa}K[i+(z<<2)>>2]=0}i=f+12|0;ta:{ua:{if(j&64){q=Qa(l+192|0);m=(j<<17>>31)+k|0;K[l+208>>2]=K[l+208>>2]-m;n=K[l+204>>2];c=K[l+200>>2];r=m&31;if((m&63)>>>0>=32){w=0;c=n>>>r|0}else{w=n>>>r|0;c=((1<<r)-1&n)<<32-r|c>>>r}K[l+200>>2]=c;K[l+204>>2]=w;c=(q&(-1<<m^-1)|(j>>>10&1)<<m|1)+2<<fa|q<<31;break ua}c=0;if(!(G&64)){break ta}}K[i>>2]=c}b=b+2|0;va:{if(j&128){q=Qa(l+192|0);k=(j<<16>>31)+k|0;K[l+208>>2]=K[l+208>>2]-k;n=K[l+204>>2];c=K[l+200>>2];m=k&31;if((k&63)>>>0>=32){w=0;c=n>>>m|0}else{w=n>>>m|0;c=((1<<m)-1&n)<<32-m|c>>>m}K[l+200>>2]=c;K[l+204>>2]=w;c=q&(-1<<k^-1)|(j>>>11&1)<<k|1;K[i+(z<<2)>>2]=c+2<<fa|q<<31;I[b|0]=-96-T(c);break va}if(G>>>0<128){break va}K[i+(z<<2)>>2]=0}Y=Y^16;o=(h&4)+o|0;f=f+16|0;if((s|0)<(z|0)){continue}break}}wa:{if(!(Z&2)|H>>>0<2){break wa}o=p&4;xa:{ya:{za:{Aa:{Ba:{if(V){r=o?_:ga;x=0;if((z|0)<=0){break Ba}q=ia+(Q(z,Z-2|0)<<2)|0;while(1){j=pb(l+128|0);m=0;f=K[r>>2];if(f){m=q+(x<<2)|0;k=0;b=15;while(1){Ca:{if(!(b&f)){break Ca}n=b&286331153;if(n&f){K[m>>2]=da|K[m>>2]^((j^-1)&1)<<fa;j=j>>>1|0}if(f&n<<1){c=(z<<2)+m|0;K[c>>2]=da|K[c>>2]^((j^-1)&1)<<fa;j=j>>>1|0}if(f&n<<2){c=m+oa|0;K[c>>2]=da|K[c>>2]^((j^-1)&1)<<fa;j=j>>>1|0}if(!(f&n<<3)){break Ca}c=m+na|0;K[c>>2]=da|K[c>>2]^((j^-1)&1)<<fa;j=j>>>1|0}m=m+4|0;b=b<<4;k=k+1|0;if((k|0)!=8){continue}break}m=Pe(f)}r=r+4|0;K[l+144>>2]=K[l+144>>2]-m;c=K[l+140>>2];b=K[l+136>>2];j=m&31;if((m&63)>>>0>=32){w=0;b=c>>>j|0}else{w=c>>>j|0;b=((1<<j)-1&c)<<32-j|b>>>j}K[l+136>>2]=b;K[l+140>>2]=w;x=x+8|0;if((z|0)>(x|0)){continue}break}}c=0;j=0;ea=o?la:ka;m=ea;r=o?_:ga;b=r;if((z|0)>0){break za}b=!o;break Aa}ea=o?la:ka;b=!o}if(Z>>>0<=5){break wa}h=b?_:ga;if((z|0)<=0){break xa}b=b?la:ka;break ya}while(1){q=j>>>28|0;j=K[b>>2];q=j|(q|j<<4|j>>>4);K[m>>2]=q;q=q|K[b+4>>2]<<28;K[m>>2]=(q>>>1&2004318071|q<<1&-286331154|q)&(j^-1);m=m+4|0;b=b+4|0;c=c+8|0;if((z|0)>(c|0)){continue}break}if(Z>>>0<6){break wa}h=o?ga:_;b=o?ka:la}k=0;o=0;m=r;v=b;j=b;b=h;while(1){q=m+4|0;c=K[j>>2];n=K[m>>2];if(!ma){c=c|(n|(n<<4|o>>>28|n>>>4|K[q>>2]<<28))<<3&-2004318072}K[j>>2]=(K[b>>2]^-1)&c;b=b+4|0;j=j+4|0;o=n;m=q;k=k+8|0;if((z|0)>(k|0)){continue}break}if((z|0)<=0){break xa}U=ia+(Q(z,Z-6|0)<<2)|0;$=0;o=h;while(1){f=0;b=K[v>>2];if(b){A=$|4;Z=z-$|0;j=0;t=0;while(1){q=j;j=Qa(l+160|0);w=(z|0)>(t+A|0)?t+4|0:Z;Da:{if((w|0)<=(t|0)){m=0;break Da}P=K[o>>2]^-1;x=((t|$)<<2)+U|0;m=0;k=t;i=k<<2;s=15<<i;c=s;while(1){Ea:{if(!(b&c)){break Ea}G=c&286331153;if(G&b){if(j&1){f=f|G;b=P&50<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=G<<1;if(n&b){if(j&1){f=f|n;b=P&116<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=G<<2;if(n&b){if(j&1){f=f|n;b=P&232<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=G<<3;if(!(n&b)){break Ea}if(j&1){f=f|n;b=P&192<<(k<<2)|b}m=m+1|0;j=j>>>1|0}c=c<<4;k=k+1|0;if((w|0)>(k|0)){continue}break}if(!(f>>>i&65535)){break Da}while(1){Fa:{if(!(f&s)){break Fa}n=s&286331153;if(n&f){K[x>>2]=C|(K[x>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<1&f){c=(z<<2)+x|0;K[c>>2]=C|(K[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<2&f){c=x+oa|0;K[c>>2]=C|(K[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(!(n<<3&f)){break Fa}c=x+na|0;K[c>>2]=C|(K[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}s=s<<4;x=x+4|0;t=t+1|0;if((w|0)>(t|0)){continue}break}}K[l+176>>2]=K[l+176>>2]-m;j=K[l+172>>2];c=K[l+168>>2];n=m&31;if((m&63)>>>0>=32){w=0;c=j>>>n|0}else{w=j>>>n|0;c=((1<<n)-1&j)<<32-n|c>>>n}K[l+168>>2]=c;K[l+172>>2]=w;j=1;t=4;if(!(q&1)){continue}break}K[v+4>>2]=K[v+4>>2]|(f>>>27&14|f>>>29|f>>>28)&(K[o+4>>2]^-1)}j=K[o>>2]|f;q=j>>>3&286331153;c=q>>>4|q<<4|q;if($){b=ea-4|0;K[b>>2]=K[b>>2]|(K[r-4>>2]^-1)&q<<28}K[ea>>2]=K[ea>>2]|c&(K[r>>2]^-1);K[ea+4>>2]=K[ea+4>>2]|(K[r+4>>2]^-1)&j>>>31;v=v+4|0;o=o+4|0;ea=ea+4|0;r=r+4|0;$=$+8|0;if((z|0)>($|0)){continue}break}}if(!u){break wa}B(h,0,u)}if((p|0)<(ca|0)){continue}break}}Ga:{if(H>>>0<2){break Ga}f=(ca&3)-1|0;Ha:{if(V&f>>>0<2){if((z|0)<=0){break Ha}s=1<<W-2;n=ia+(Q(z,ca&16777212)<<2)|0;P=ca&4?ga:_;q=Q(z,12);g=z<<3;i=W-1|0;t=0;while(1){j=pb(l+128|0);m=0;h=K[P>>2];if(h){m=n+(t<<2)|0;b=15;k=0;while(1){Ia:{if(!(b&h)){break Ia}o=b&286331153;if(o&h){K[m>>2]=s|K[m>>2]^((j^-1)&1)<<i;j=j>>>1|0}if(h&o<<1){c=(z<<2)+m|0;K[c>>2]=s|K[c>>2]^((j^-1)&1)<<i;j=j>>>1|0}if(h&o<<2){c=g+m|0;K[c>>2]=s|K[c>>2]^((j^-1)&1)<<i;j=j>>>1|0}if(!(h&o<<3)){break Ia}c=m+q|0;K[c>>2]=s|K[c>>2]^((j^-1)&1)<<i;j=j>>>1|0}m=m+4|0;b=b<<4;k=k+1|0;if((k|0)!=8){continue}break}m=Pe(h)}P=P+4|0;K[l+144>>2]=K[l+144>>2]-m;c=K[l+140>>2];b=K[l+136>>2];j=m&31;if((m&63)>>>0>=32){w=0;b=c>>>j|0}else{w=c>>>j|0;b=((1<<j)-1&c)<<32-j|b>>>j}K[l+136>>2]=b;K[l+140>>2]=w;t=t+8|0;if((z|0)>(t|0)){continue}break}}if((z|0)<=0|f>>>0>1){break Ha}b=ca&4;m=b?ga:_;b=b?ka:la;c=0;j=0;while(1){g=j>>>28|0;j=K[m>>2];g=j|(g|j<<4|j>>>4);K[b>>2]=g;g=g|K[m+4>>2]<<28;K[b>>2]=(g>>>1&2004318071|g<<1&-286331154|g)&(j^-1);b=b+4|0;m=m+4|0;c=c+8|0;if((z|0)>(c|0)){continue}break}}$=(ca|0)>6?(ca-(ca+1&3)|0)-3|0:0;if((ca|0)<=($|0)){break Ga}t=Q(z,12);r=z<<3;G=3<<W-2;v=(z|0)<=0;while(1){c=ca-$|0;b=c-1|0;Ja:{Ka:{La:{if(b>>>0>=3){h=-1;if((c|0)<5){break La}if(v){break Ja}c=$&4;m=c?ga:_;j=c?ka:la;b=0;if(!ma){b=c?_:ga;c=0;f=0;while(1){g=f>>>28|0;h=-1;f=K[b>>2];K[j>>2]=(K[j>>2]|(f|(g|f<<4|f>>>4|K[b+4>>2]<<28))<<3&-2004318072)&(K[m>>2]^-1);m=m+4|0;j=j+4|0;b=b+4|0;c=c+8|0;if((z|0)>(c|0)){continue}break}break Ka}while(1){h=-1;K[j>>2]=K[j>>2]&(K[m>>2]^-1);m=m+4|0;j=j+4|0;b=b+8|0;if((z|0)>(b|0)){continue}break}break Ka}h=K[(b<<2)+20764>>2]}if(v){break Ja}}b=$&4;Y=b?ga:_;s=b?ka:la;g=b?_:ga;H=b?la:ka;p=ia+(Q(z,$)<<2)|0;o=0;while(1){f=0;b=K[s>>2]&h;if(b){V=o|4;Z=z-o|0;j=0;i=0;while(1){q=j;j=Qa(l+160|0);w=(z|0)>(i+V|0)?i+4|0:Z;Ma:{if((w|0)<=(i|0)){m=0;break Ma}U=(K[Y>>2]^-1)&h;x=p+((i|o)<<2)|0;m=0;k=i;X=i<<2;P=15<<X;c=P;while(1){Na:{if(!(b&c)){break Na}A=c&286331153;if(A&b){if(j&1){f=f|A;b=U&50<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=A<<1;if(n&b){if(j&1){f=f|n;b=U&116<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=A<<2;if(n&b){if(j&1){f=f|n;b=U&232<<(k<<2)|b}m=m+1|0;j=j>>>1|0}n=A<<3;if(!(n&b)){break Na}if(j&1){f=f|n;b=U&192<<(k<<2)|b}m=m+1|0;j=j>>>1|0}c=c<<4;k=k+1|0;if((w|0)>(k|0)){continue}break}if(!(f>>>X&65535)){break Ma}while(1){Oa:{if(!(f&P)){break Oa}n=P&286331153;if(n&f){K[x>>2]=G|(K[x>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<1&f){c=(z<<2)+x|0;K[c>>2]=G|(K[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(n<<2&f){c=r+x|0;K[c>>2]=G|(K[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}if(!(n<<3&f)){break Oa}c=t+x|0;K[c>>2]=G|(K[c>>2]|j<<31);m=m+1|0;j=j>>>1|0}P=P<<4;x=x+4|0;i=i+1|0;if((w|0)>(i|0)){continue}break}}K[l+176>>2]=K[l+176>>2]-m;j=K[l+172>>2];c=K[l+168>>2];n=m&31;if((m&63)>>>0>=32){w=0;c=j>>>n|0}else{w=j>>>n|0;c=((1<<n)-1&j)<<32-n|c>>>n}K[l+168>>2]=c;K[l+172>>2]=w;j=1;i=4;if(!(q&1)){continue}break}K[s+4>>2]=K[s+4>>2]|(f>>>27&14|f>>>29|f>>>28)&(K[Y+4>>2]^-1)}j=K[Y>>2]|f;q=j>>>3&286331153;c=q>>>4|q<<4|q;if(o){b=H-4|0;K[b>>2]=K[b>>2]|(K[g-4>>2]^-1)&q<<28}K[H>>2]=K[H>>2]|c&(K[g>>2]^-1);K[H+4>>2]=K[H+4>>2]|(K[g+4>>2]^-1)&j>>>31;s=s+4|0;Y=Y+4|0;H=H+4|0;g=g+4|0;o=o+8|0;if((z|0)>(o|0)){continue}break}}$=$+4|0;if((ca|0)>($|0)){continue}break}}j=1;if((ca|0)<=0|(z|0)<=0){break j}q=z&2147483644;n=z&3;g=F-ja>>>0>4294967292;o=0;while(1){j=ia+(Q(o,z)<<2)|0;m=0;if(!g){while(1){c=K[j>>2];b=c&2147483647;K[j>>2]=(c|0)<0?0-b|0:b;c=K[j+4>>2];b=c&2147483647;K[j+4>>2]=(c|0)<0?0-b|0:b;c=K[j+8>>2];b=c&2147483647;K[j+8>>2]=(c|0)<0?0-b|0:b;c=K[j+12>>2];b=c&2147483647;K[j+12>>2]=(c|0)<0?0-b|0:b;j=j+16|0;m=m+4|0;if((q|0)!=(m|0)){continue}break}}m=0;if(n){while(1){c=K[j>>2];b=c&2147483647;K[j>>2]=(c|0)<0?0-b|0:b;j=j+4|0;m=m+1|0;if((n|0)!=(m|0)){continue}break}}j=1;o=o+1|0;if((ca|0)!=(o|0)){continue}break}break j}if(!ba){break v}K[l+52>>2]=K[D+24>>2];K[l+48>>2]=G;Fa(S,1,9649,l+48|0);break u}K[l+20>>2]=m;K[l+16>>2]=G;Fa(S,1,9649,l+16|0);j=0;break j}j=0}ra=l+304|0;if(j){break i}break b}K[e+108>>2]=(b<<9)+22288;c=0;b=K[e+116>>2];Pa:{Qa:{o=K[D+16>>2]-K[D+8>>2]|0;k=K[D+20>>2]-K[D+12>>2]|0;g=Q(o,k);Ra:{Sa:{Ta:{if(g>>>0>N[e+132>>2]){Ga(b);b=Ma(g<<2);K[e+116>>2]=b;if(!b){break Ra}K[e+132>>2]=g;break Ta}if(!b){break Sa}}g=g<<2;if(!g){break Sa}B(b,0,g)}b=K[e+120>>2];s=o+2|0;n=k+3>>>2|0;g=Q(s,n+2|0);if(g>>>0<=N[e+136>>2]){x=g<<2;break Qa}Ga(b);x=g<<2;b=Ma(x);K[e+120>>2]=b;if(b){break Qa}}b=0;break Pa}K[e+136>>2]=g;if(x){B(b,0,x)}Ua:{if(!s){break Ua}q=K[e+120>>2];b=q;l=o+1|0;if(l>>>0>=7){g=s&-8;while(1){K[b+24>>2]=1226833920;K[b+28>>2]=1226833920;K[b+16>>2]=1226833920;K[b+20>>2]=1226833920;K[b+8>>2]=1226833920;K[b+12>>2]=1226833920;K[b>>2]=1226833920;K[b+4>>2]=1226833920;b=b+32|0;c=c+8|0;if((g|0)!=(c|0)){continue}break}}g=s&7;if(g){c=0;while(1){K[b>>2]=1226833920;b=b+4|0;c=c+1|0;if((g|0)!=(c|0)){continue}break}}b=q+(Q(s,n+1|0)<<2)|0;if(l>>>0>=7){g=s&-8;c=0;while(1){K[b+24>>2]=1226833920;K[b+28>>2]=1226833920;K[b+16>>2]=1226833920;K[b+20>>2]=1226833920;K[b+8>>2]=1226833920;K[b+12>>2]=1226833920;K[b>>2]=1226833920;K[b+4>>2]=1226833920;b=b+32|0;c=c+8|0;if((g|0)!=(c|0)){continue}break}}g=s&7;if(g){c=0;while(1){K[b>>2]=1226833920;b=b+4|0;c=c+1|0;if((g|0)!=(c|0)){continue}break}}b=k&3;if(!b){break Ua}g=(b|0)==1?1224736768:(b|0)==2?1207959552:1073741824;b=q+(Q(n,s)<<2)|0;if(l>>>0>=7){c=s&-8;x=0;while(1){K[b+28>>2]=g;K[b+24>>2]=g;K[b+20>>2]=g;K[b+16>>2]=g;K[b+12>>2]=g;K[b+8>>2]=g;K[b+4>>2]=g;K[b>>2]=g;b=b+32|0;x=x+8|0;if((c|0)!=(x|0)){continue}break}}c=s&7;if(!c){break Ua}x=0;while(1){K[b>>2]=g;b=b+4|0;x=x+1|0;if((c|0)!=(x|0)){continue}break}}K[e+128>>2]=k;K[e+124>>2]=o;b=1}if(!b){break b}x=h+K[D+28>>2]|0;if((x|0)>=31){if(!ba){break h}K[aa+16>>2]=x;Fa(S,2,8679,aa+16|0);break b}mc(e);bb(e,18,46);bb(e,17,3);bb(e,0,4);if(K[D+64>>2]){break i}q=K[D+52>>2];Va:{if(!(q>>>0<=1&(!K[e+144>>2]|(q|0)!=1))){b=K[D+4>>2];g=0;if(q-1>>>0>=3){c=q&-4;while(1){l=(i<<3)+b|0;g=K[l+28>>2]+(K[l+20>>2]+(K[l+12>>2]+(K[l+4>>2]+g|0)|0)|0)|0;i=i+4|0;f=f+4|0;if((c|0)!=(f|0)){continue}break}}c=q&3;if(c){while(1){g=K[((i<<3)+b|0)+4>>2]+g|0;i=i+1|0;j=j+1|0;if((c|0)!=(j|0)){continue}break}}ja=K[e+148>>2];c=g+2|0;if(c>>>0>N[e+152>>2]){b=La(ja,c);if(!b){break b}K[e+148>>2]=b;b=b+g|0;I[b|0]=0;I[b+1|0]=0;K[e+152>>2]=c;ja=K[e+148>>2];if(!K[D+52>>2]){break Va}b=K[D+4>>2]}g=0;i=0;while(1){l=i<<3;c=l+b|0;b=K[c+4>>2];if(b){E(g+ja|0,K[c>>2],b)}b=K[D+4>>2];g=K[(l+b|0)+4>>2]+g|0;i=i+1|0;if(i>>>0<N[D+52>>2]){continue}break}break Va}if((q|0)!=1){break i}ja=K[K[D+4>>2]>>2]}b=K[D+60>>2];if(b){ia=K[e+116>>2];K[e+116>>2]=b}if(K[D+44>>2]){V=Z&2;P=Z&8;da=e+28|0;w=!(Z&1);ma=2;while(1){l=G+ja|0;na=K[D>>2]+Q(U,24)|0;c=K[na>>2];oa=w|((K[D+28>>2]-4|0)<(x|0)|ma>>>0>1);Wa:{if(!oa){K[e+20>>2]=l;b=c+l|0;K[e+24>>2]=b;J[e+112>>1]=L[b|0]|L[b+1|0]<<8;I[b|0]=255;I[K[e+24>>2]+1|0]=255;K[e+8>>2]=0;K[e>>2]=0;K[e+16>>2]=l;break Wa}K[e+20>>2]=l;b=c+l|0;K[e+24>>2]=b;J[e+112>>1]=L[b|0]|L[b+1|0]<<8;I[b|0]=255;I[K[e+24>>2]+1|0]=255;K[e+104>>2]=e+28;K[e+16>>2]=l;K[e+12>>2]=0;b=c?L[l|0]<<16:16711680;K[e>>2]=b;j=1;c=l+1|0;g=L[l+1|0];Xa:{if(L[l|0]==255){if(g>>>0>=144){K[e+12>>2]=1;b=b|65280;break Xa}K[e+16>>2]=c;j=0;b=b+(g<<9)|0;break Xa}K[e+16>>2]=c;b=b|g<<8}K[e+8>>2]=j;K[e+4>>2]=32768;K[e>>2]=b<<7}H=K[na>>2];Ya:{if(!K[na+8>>2]|(x|0)<=0){break Ya}ea=0;X=oa&(V|0)!=0;while(1){Za:{_a:{$a:{switch(ma-1|0){default:if(!oa){b=1<<x;h=b>>>1|b;s=K[e+124>>2];n=s<<2;b=(n+K[e+120>>2]|0)+12|0;g=K[e+116>>2];m=0;c=K[e+128>>2];if(c>>>0>=4){if(!s){break Za}d=Q(s,12);q=s<<3;f=0-h|0;while(1){c=0;while(1){l=b;b=K[b>>2];ab:{if(!b){break ab}if(!(!(b&495)|b&2097168)){b=K[e>>2];j=K[e+8>>2];bb:{if(j){break bb}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];cb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break cb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break bb}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;db:{if(!(b>>>j&1)){break db}eb:{if(j){break eb}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];fb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break fb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break eb}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;k=b>>>j&1;K[g>>2]=k?f:h;j=K[e+124>>2];b=l-4|0;K[b>>2]=K[b>>2]|32;K[l+4>>2]=K[l+4>>2]|8;K[l>>2]=K[l>>2]|k<<19|16;if(P){break db}b=l+(-2-j<<2)|0;K[b+4>>2]=K[b+4>>2]|32768;K[b>>2]=K[b>>2]|k<<31|65536;b=b-4|0;K[b>>2]=K[b>>2]|131072}b=K[l>>2]|2097152;K[l>>2]=b}if(!(!(b&3960)|b&16777344)){b=K[e>>2];j=K[e+8>>2];gb:{if(j){break gb}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];hb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break hb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break gb}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;if(b>>>j&1){ib:{if(j){break ib}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];jb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break jb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break ib}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;j=b>>>j&1;K[g+n>>2]=j?f:h;b=l-4|0;K[b>>2]=K[b>>2]|256;K[l+4>>2]=K[l+4>>2]|64;b=K[l>>2]|j<<22|128}else{b=K[l>>2]}b=b|16777216;K[l>>2]=b}if(!(!(b&31680)|b&134218752)){b=K[e>>2];j=K[e+8>>2];kb:{if(j){break kb}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];lb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break lb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break kb}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;if(b>>>j&1){mb:{if(j){break mb}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];nb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break nb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break mb}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;j=b>>>j&1;K[g+q>>2]=j?f:h;b=l-4|0;K[b>>2]=K[b>>2]|2048;K[l+4>>2]=K[l+4>>2]|512;b=K[l>>2]|j<<25|1024}else{b=K[l>>2]}b=b|134217728;K[l>>2]=b}if(!(b&253440)|b&1073750016){break ab}b=K[e>>2];j=K[e+8>>2];ob:{if(j){break ob}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];pb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break pb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break ob}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;if(b>>>j&1){qb:{if(j){break qb}j=(b|0)==255;k=K[e+16>>2];b=L[k|0];rb:{if(!j){K[e>>2]=b;K[e+16>>2]=k+1;break rb}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=k+1;j=7;break qb}b=255;K[e>>2]=255}j=8}j=j-1|0;K[e+8>>2]=j;k=b>>>j&1;K[d+g>>2]=k?f:h;j=K[e+124>>2];b=l-4|0;K[b>>2]=K[b>>2]|16384;K[l+4>>2]=K[l+4>>2]|4096;K[l>>2]=K[l>>2]|k<<28|8192;b=l+(j<<2)|0;K[b+4>>2]=K[b+4>>2]|4;K[b+12>>2]=K[b+12>>2]|1;K[b+8>>2]=K[b+8>>2]|k<<18|2}K[l>>2]=K[l>>2]|1073741824}g=g+4|0;b=l+4|0;c=c+1|0;if((s|0)!=(c|0)){continue}break}g=d+g|0;b=l+12|0;m=m+4|0;c=K[e+128>>2];if(m>>>0<(c&-4)>>>0){continue}break}}if(!s|c>>>0<=m>>>0){break _a}v=0;q=0-h|0;j=c;while(1){sb:{if((j|0)==(m|0)){j=m;break sb}d=b-4|0;k=K[b>>2];i=0;while(1){o=Q(i,3);l=k>>>o|0;if(!(l&2097168|!(l&495))){c=K[e>>2];f=K[e+8>>2];tb:{if(f){break tb}l=(c|0)!=255;j=K[e+16>>2];c=L[j|0];ub:{if(!l){if(c>>>0>=144){c=255;K[e>>2]=255;break ub}K[e>>2]=c;K[e+16>>2]=j+1;f=7;break tb}K[e>>2]=c;K[e+16>>2]=j+1}f=8}f=f-1|0;K[e+8>>2]=f;vb:{if(!(c>>>f&1)){break vb}j=(Q(i,s)<<2)+g|0;wb:{if(f){break wb}l=(c|0)!=255;n=K[e+16>>2];c=L[n|0];xb:{if(!l){if(c>>>0>=144){c=255;K[e>>2]=255;break xb}K[e>>2]=c;K[e+16>>2]=n+1;f=7;break wb}K[e>>2]=c;K[e+16>>2]=n+1}f=8}l=f-1|0;K[e+8>>2]=l;k=j;j=c>>>l&1;K[k>>2]=j?q:h;l=K[e+124>>2];K[d>>2]=K[d>>2]|32<<o;K[b>>2]=K[b>>2]|(j<<19|16)<<o;K[b+4>>2]=K[b+4>>2]|8<<o;if(!(i|P)){c=(-2-l<<2)+b|0;K[c+4>>2]=K[c+4>>2]|32768;K[c>>2]=K[c>>2]|j<<31|65536;c=c-4|0;K[c>>2]=K[c>>2]|131072}if((i|0)!=3){break vb}c=(l<<2)+b|0;K[c+4>>2]=K[c+4>>2]|4;K[c+12>>2]=K[c+12>>2]|1;K[c+8>>2]=K[c+8>>2]|j<<18|2}k=K[b>>2]|2097152<<o;K[b>>2]=k;c=K[e+128>>2]}j=c;i=i+1|0;if(i>>>0<c-m>>>0){continue}break}}g=g+4|0;b=b+4|0;v=v+1|0;if((s|0)!=(v|0)){continue}break}break _a}j=0;s=0;v=0;yb:{zb:{Ab:{C=K[e+124>>2];if(!((C|0)!=64|K[e+128>>2]!=64)){b=1<<x;j=b>>>1|b;l=0-j|0;r=e+28|0;g=K[e+120>>2]+268|0;f=K[e+8>>2];c=K[e+4>>2];k=K[e>>2];m=K[e+104>>2];b=K[e+116>>2];if(Z&8){break Ab}while(1){v=0;while(1){q=b;n=g;g=K[g>>2];if(g){Bb:{if(g&2097168){break Bb}b=g&495;if(!b){break Bb}m=r+(L[b+K[e+108>>2]|0]<<2)|0;i=K[m>>2];b=K[i>>2];c=c-b|0;Cb:{if(k>>>16>>>0<b>>>0){o=K[i+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[i+(d?8:12)>>2];while(1){Db:{if(f){break Db}f=K[e+16>>2];c=f+1|0;i=L[f+1|0];if(L[f|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Db}K[e+16>>2]=c;k=(i<<9)+k|0;f=7;break Db}K[e+16>>2]=c;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?o:!o;break Cb}k=k-(b<<16)|0;if(!(c&32768)){o=K[i+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[i+(b?12:8)>>2];while(1){Eb:{if(f){break Eb}f=K[e+16>>2];d=f+1|0;i=L[f+1|0];if(L[f|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Eb}K[e+16>>2]=d;k=(i<<9)+k|0;f=7;break Eb}K[e+16>>2]=d;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!o:o;break Cb}b=K[i+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>17&4|(K[h>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];Fb:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Gb:{if(f){break Gb}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Gb}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Gb}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;d=d?i:!i;break Fb}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Hb:{if(f){break Hb}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Hb}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Hb}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}d=b?!i:i;break Fb}d=K[p+4>>2]}K[q>>2]=(o|0)==(d|0)?j:l;K[h>>2]=K[h>>2]|32;K[n+4>>2]=K[n+4>>2]|8;b=n-268|0;K[b>>2]=K[b>>2]|131072;b=n-260|0;K[b>>2]=K[b>>2]|32768;b=n-264|0;i=b;u=K[b>>2];b=d^o;K[i>>2]=u|b<<31|65536;g=b<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){o=g>>>3|0;m=r+(L[K[e+108>>2]+(o&495)|0]<<2)|0;h=K[m>>2];b=K[h>>2];c=c-b|0;Ib:{if(k>>>16>>>0<b>>>0){i=K[h+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[h+(d?8:12)>>2];while(1){Jb:{if(f){break Jb}f=K[e+16>>2];c=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Jb}K[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Jb}K[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Ib}k=k-(b<<16)|0;if(!(c&32768)){i=K[h+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[h+(b?12:8)>>2];while(1){Kb:{if(f){break Kb}f=K[e+16>>2];d=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Kb}K[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Kb}K[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Ib}b=K[h+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>20&4|(K[h>>2]>>>22&1|(g>>>15&16|(g>>>19&64|o&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];Lb:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Mb:{if(f){break Mb}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Mb}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Mb}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Lb}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Nb:{if(f){break Nb}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Nb}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Nb}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Lb}b=K[p+4>>2]}K[q+256>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|256;K[n+4>>2]=K[n+4>>2]|64;g=(b^o)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){o=g>>>6|0;m=r+(L[K[e+108>>2]+(o&495)|0]<<2)|0;h=K[m>>2];b=K[h>>2];c=c-b|0;Ob:{if(k>>>16>>>0<b>>>0){i=K[h+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[h+(d?8:12)>>2];while(1){Pb:{if(f){break Pb}f=K[e+16>>2];c=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Pb}K[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Pb}K[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Ob}k=k-(b<<16)|0;if(!(c&32768)){i=K[h+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[h+(b?12:8)>>2];while(1){Qb:{if(f){break Qb}f=K[e+16>>2];d=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Qb}K[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Qb}K[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Ob}b=K[h+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>23&4|(K[h>>2]>>>25&1|(g>>>18&16|(g>>>22&64|o&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];Rb:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Sb:{if(f){break Sb}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Sb}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Sb}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Rb}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Tb:{if(f){break Tb}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Tb}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Tb}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Rb}b=K[p+4>>2]}K[q+512>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|2048;K[n+4>>2]=K[n+4>>2]|512;g=(b^o)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){o=g>>>9|0;m=r+(L[K[e+108>>2]+(o&495)|0]<<2)|0;h=K[m>>2];b=K[h>>2];c=c-b|0;Ub:{if(k>>>16>>>0<b>>>0){i=K[h+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[h+(d?8:12)>>2];while(1){Vb:{if(f){break Vb}f=K[e+16>>2];c=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Vb}K[e+16>>2]=c;k=(h<<9)+k|0;f=7;break Vb}K[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Ub}k=k-(b<<16)|0;if(!(c&32768)){i=K[h+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[h+(b?12:8)>>2];while(1){Wb:{if(f){break Wb}f=K[e+16>>2];d=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Wb}K[e+16>>2]=d;k=(h<<9)+k|0;f=7;break Wb}K[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Ub}b=K[h+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>26&4|(K[h>>2]>>>28&1|(g>>>21&16|(g>>>25&64|o&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];Xb:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Yb:{if(f){break Yb}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Yb}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Yb}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break Xb}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Zb:{if(f){break Zb}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break Zb}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Zb}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break Xb}b=K[p+4>>2]}K[q+768>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|16384;K[n+4>>2]=K[n+4>>2]|4096;K[n+260>>2]=K[n+260>>2]|4;K[n+268>>2]=K[n+268>>2]|1;b=b^o;K[n+264>>2]=K[n+264>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}K[n>>2]=g}g=n+4|0;b=q+4|0;v=v+1|0;if((v|0)!=64){continue}break}g=n+12|0;b=q+772|0;q=s>>>0<60;s=s+4|0;if(q){continue}break}break zb}b=1<<x;l=b>>>1|b;q=K[e+120>>2];g=(q+(C<<2)|0)+12|0;b=K[e+128>>2];f=K[e+8>>2];c=K[e+4>>2];k=K[e>>2];m=K[e+104>>2];o=K[e+116>>2];_b:{if(Z&8){$b:{if(b>>>0<4){break $b}if(C){A=Q(C,12);h=C<<3;q=0-l|0;F=e+28|0;while(1){r=0;while(1){n=g;g=K[g>>2];if(g){ac:{if(g&2097168){break ac}b=g&495;if(!b){break ac}m=F+(L[b+K[e+108>>2]|0]<<2)|0;s=K[m>>2];b=K[s>>2];c=c-b|0;bc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;i=K[s+4>>2];if(c&32768){break bc}i=K[s+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[s+(b?12:8)>>2];while(1){cc:{if(f){break cc}f=K[e+16>>2];d=f+1|0;s=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(s<<8)+k|0;break cc}if(s>>>0<=143){K[e+16>>2]=d;k=(s<<9)+k|0;f=7;break cc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}i=b?!i:i;break bc}i=K[s+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[s+(d?8:12)>>2];while(1){dc:{if(f){break dc}f=K[e+16>>2];c=f+1|0;s=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(s<<8)+k|0;break dc}if(s>>>0<=143){K[e+16>>2]=c;k=(s<<9)+k|0;f=7;break dc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;i=d?i:!i}if(i){p=n-4|0;d=K[n+4>>2]>>>17&4|(K[p>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=F+(L[d+24336|0]<<2)|0;t=K[m>>2];b=K[t>>2];c=c-b|0;i=L[d+24592|0];ec:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[t+4>>2];if(c&32768){break ec}s=K[t+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[t+(b?12:8)>>2];while(1){fc:{if(f){break fc}f=K[e+16>>2];d=f+1|0;t=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(t<<8)+k|0;break fc}if(t>>>0<=143){K[e+16>>2]=d;k=(t<<9)+k|0;f=7;break fc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break ec}s=K[t+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[t+(d?8:12)>>2];while(1){gc:{if(f){break gc}f=K[e+16>>2];c=f+1|0;t=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(t<<8)+k|0;break gc}if(t>>>0<=143){K[e+16>>2]=c;k=(t<<9)+k|0;f=7;break gc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}b=u;K[o>>2]=(i|0)==(b|0)?l:q;K[p>>2]=K[p>>2]|32;K[n+4>>2]=K[n+4>>2]|8;g=(b^i)<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){i=g>>>3|0;m=F+(L[K[e+108>>2]+(i&495)|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;hc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[p+4>>2];if(c&32768){break hc}s=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){ic:{if(f){break ic}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(p<<8)+k|0;break ic}if(p>>>0<=143){K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break ic}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break hc}s=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){jc:{if(f){break jc}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(p<<8)+k|0;break jc}if(p>>>0<=143){K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break jc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}if(u){t=n-4|0;d=K[n+4>>2]>>>20&4|(K[t>>2]>>>22&1|(g>>>15&16|(g>>>19&64|i&170)));m=F+(L[d+24336|0]<<2)|0;u=K[m>>2];b=K[u>>2];c=c-b|0;s=(C<<2)+o|0;i=L[d+24592|0];kc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;y=K[u+4>>2];if(c&32768){break kc}p=K[u+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[u+(b?12:8)>>2];while(1){lc:{if(f){break lc}f=K[e+16>>2];d=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(u<<8)+k|0;break lc}if(u>>>0<=143){K[e+16>>2]=d;k=(u<<9)+k|0;f=7;break lc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}y=b?!p:p;break kc}p=K[u+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[u+(d?8:12)>>2];while(1){mc:{if(f){break mc}f=K[e+16>>2];c=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(u<<8)+k|0;break mc}if(u>>>0<=143){K[e+16>>2]=c;k=(u<<9)+k|0;f=7;break mc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;y=d?p:!p}b=y;K[s>>2]=(i|0)==(b|0)?l:q;K[t>>2]=K[t>>2]|256;K[n+4>>2]=K[n+4>>2]|64;g=(b^i)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){i=g>>>6|0;m=F+(L[K[e+108>>2]+(i&495)|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;nc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[p+4>>2];if(c&32768){break nc}s=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){oc:{if(f){break oc}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(p<<8)+k|0;break oc}if(p>>>0<=143){K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break oc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break nc}s=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){pc:{if(f){break pc}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(p<<8)+k|0;break pc}if(p>>>0<=143){K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break pc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}if(u){t=n-4|0;d=K[n+4>>2]>>>23&4|(K[t>>2]>>>25&1|(g>>>18&16|(g>>>22&64|i&170)));m=F+(L[d+24336|0]<<2)|0;u=K[m>>2];b=K[u>>2];c=c-b|0;s=h+o|0;i=L[d+24592|0];qc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;y=K[u+4>>2];if(c&32768){break qc}p=K[u+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[u+(b?12:8)>>2];while(1){rc:{if(f){break rc}f=K[e+16>>2];d=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(u<<8)+k|0;break rc}if(u>>>0<=143){K[e+16>>2]=d;k=(u<<9)+k|0;f=7;break rc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}y=b?!p:p;break qc}p=K[u+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[u+(d?8:12)>>2];while(1){sc:{if(f){break sc}f=K[e+16>>2];c=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(u<<8)+k|0;break sc}if(u>>>0<=143){K[e+16>>2]=c;k=(u<<9)+k|0;f=7;break sc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;y=d?p:!p}b=y;K[s>>2]=(i|0)==(b|0)?l:q;K[t>>2]=K[t>>2]|2048;K[n+4>>2]=K[n+4>>2]|512;g=(b^i)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){i=g>>>9|0;m=F+(L[K[e+108>>2]+(i&495)|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;tc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[p+4>>2];if(c&32768){break tc}s=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){uc:{if(f){break uc}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(p<<8)+k|0;break uc}if(p>>>0<=143){K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break uc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break tc}s=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){vc:{if(f){break vc}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(p<<8)+k|0;break vc}if(p>>>0<=143){K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break vc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}if(u){t=n-4|0;d=K[n+4>>2]>>>26&4|(K[t>>2]>>>28&1|(g>>>21&16|(g>>>25&64|i&170)));m=F+(L[d+24336|0]<<2)|0;u=K[m>>2];b=K[u>>2];c=c-b|0;s=o+A|0;i=L[d+24592|0];wc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;y=K[u+4>>2];if(c&32768){break wc}p=K[u+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[u+(b?12:8)>>2];while(1){xc:{if(f){break xc}f=K[e+16>>2];d=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(u<<8)+k|0;break xc}if(u>>>0<=143){K[e+16>>2]=d;k=(u<<9)+k|0;f=7;break xc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}y=b?!p:p;break wc}p=K[u+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[u+(d?8:12)>>2];while(1){yc:{if(f){break yc}f=K[e+16>>2];c=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(u<<8)+k|0;break yc}if(u>>>0<=143){K[e+16>>2]=c;k=(u<<9)+k|0;f=7;break yc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;y=d?p:!p}b=y;K[s>>2]=(i|0)==(b|0)?l:q;K[t>>2]=K[t>>2]|16384;K[n+4>>2]=K[n+4>>2]|4096;d=n+(K[e+124>>2]<<2)|0;K[d+4>>2]=K[d+4>>2]|4;K[d+12>>2]=K[d+12>>2]|1;b=b^i;K[d+8>>2]=K[d+8>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}K[n>>2]=g}g=n+4|0;o=o+4|0;r=r+1|0;if((C|0)!=(r|0)){continue}break}g=n+12|0;o=o+A|0;j=j+4|0;b=K[e+128>>2];if(j>>>0<(b&-4)>>>0){continue}break}break $b}g=(b&-4)-1|0;j=(g&-4)+4|0;g=(q+(g<<1&-8)|0)+20|0}K[e+8>>2]=f;K[e+4>>2]=c;K[e>>2]=k;K[e+104>>2]=m;if(!C|b>>>0<=j>>>0){break _b}while(1){c=(b|0)==(j|0);f=0;b=j;if(!c){while(1){lc(e,g,(Q(f,C)<<2)+o|0,l,f,K[e+124>>2]+2|0,1);f=f+1|0;b=K[e+128>>2];if(f>>>0<b-j>>>0){continue}break}}g=g+4|0;o=o+4|0;v=v+1|0;if((C|0)!=(v|0)){continue}break}break _b}zc:{if(b>>>0<4){break zc}if(C){A=Q(C,12);h=C<<3;q=0-l|0;F=e+28|0;while(1){r=0;while(1){n=g;g=K[g>>2];if(g){Ac:{if(g&2097168){break Ac}b=g&495;if(!b){break Ac}m=F+(L[b+K[e+108>>2]|0]<<2)|0;s=K[m>>2];b=K[s>>2];c=c-b|0;Bc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;i=K[s+4>>2];if(c&32768){break Bc}i=K[s+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[s+(b?12:8)>>2];while(1){Cc:{if(f){break Cc}f=K[e+16>>2];d=f+1|0;s=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(s<<8)+k|0;break Cc}if(s>>>0<=143){K[e+16>>2]=d;k=(s<<9)+k|0;f=7;break Cc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}i=b?!i:i;break Bc}i=K[s+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[s+(d?8:12)>>2];while(1){Dc:{if(f){break Dc}f=K[e+16>>2];c=f+1|0;s=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(s<<8)+k|0;break Dc}if(s>>>0<=143){K[e+16>>2]=c;k=(s<<9)+k|0;f=7;break Dc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;i=d?i:!i}if(i){p=n-4|0;d=K[n+4>>2]>>>17&4|(K[p>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=F+(L[d+24336|0]<<2)|0;t=K[m>>2];b=K[t>>2];c=c-b|0;i=L[d+24592|0];Ec:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;d=K[t+4>>2];if(c&32768){break Ec}s=K[t+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[t+(b?12:8)>>2];while(1){Fc:{if(f){break Fc}f=K[e+16>>2];d=f+1|0;t=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(t<<8)+k|0;break Fc}if(t>>>0<=143){K[e+16>>2]=d;k=(t<<9)+k|0;f=7;break Fc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}d=b?!s:s;break Ec}s=K[t+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[t+(d?8:12)>>2];while(1){Gc:{if(f){break Gc}f=K[e+16>>2];c=f+1|0;t=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(t<<8)+k|0;break Gc}if(t>>>0<=143){K[e+16>>2]=c;k=(t<<9)+k|0;f=7;break Gc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;d=d?s:!s}K[o>>2]=(i|0)==(d|0)?l:q;K[p>>2]=K[p>>2]|32;K[n+4>>2]=K[n+4>>2]|8;b=n+(-2-K[e+124>>2]<<2)|0;K[b+4>>2]=K[b+4>>2]|32768;d=d^i;K[b>>2]=K[b>>2]|d<<31|65536;b=b-4|0;K[b>>2]=K[b>>2]|131072;g=d<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){i=g>>>3|0;m=F+(L[K[e+108>>2]+(i&495)|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;Hc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[p+4>>2];if(c&32768){break Hc}s=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Ic:{if(f){break Ic}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(p<<8)+k|0;break Ic}if(p>>>0<=143){K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Ic}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break Hc}s=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Jc:{if(f){break Jc}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(p<<8)+k|0;break Jc}if(p>>>0<=143){K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Jc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}if(u){t=n-4|0;d=K[n+4>>2]>>>20&4|(K[t>>2]>>>22&1|(g>>>15&16|(g>>>19&64|i&170)));m=F+(L[d+24336|0]<<2)|0;u=K[m>>2];b=K[u>>2];c=c-b|0;s=(C<<2)+o|0;i=L[d+24592|0];Kc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;y=K[u+4>>2];if(c&32768){break Kc}p=K[u+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[u+(b?12:8)>>2];while(1){Lc:{if(f){break Lc}f=K[e+16>>2];d=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(u<<8)+k|0;break Lc}if(u>>>0<=143){K[e+16>>2]=d;k=(u<<9)+k|0;f=7;break Lc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}y=b?!p:p;break Kc}p=K[u+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[u+(d?8:12)>>2];while(1){Mc:{if(f){break Mc}f=K[e+16>>2];c=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(u<<8)+k|0;break Mc}if(u>>>0<=143){K[e+16>>2]=c;k=(u<<9)+k|0;f=7;break Mc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;y=d?p:!p}b=y;K[s>>2]=(i|0)==(b|0)?l:q;K[t>>2]=K[t>>2]|256;K[n+4>>2]=K[n+4>>2]|64;g=(b^i)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){i=g>>>6|0;m=F+(L[K[e+108>>2]+(i&495)|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;Nc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[p+4>>2];if(c&32768){break Nc}s=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Oc:{if(f){break Oc}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(p<<8)+k|0;break Oc}if(p>>>0<=143){K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Oc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break Nc}s=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Pc:{if(f){break Pc}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(p<<8)+k|0;break Pc}if(p>>>0<=143){K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Pc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}if(u){t=n-4|0;d=K[n+4>>2]>>>23&4|(K[t>>2]>>>25&1|(g>>>18&16|(g>>>22&64|i&170)));m=F+(L[d+24336|0]<<2)|0;u=K[m>>2];b=K[u>>2];c=c-b|0;s=h+o|0;i=L[d+24592|0];Qc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;y=K[u+4>>2];if(c&32768){break Qc}p=K[u+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[u+(b?12:8)>>2];while(1){Rc:{if(f){break Rc}f=K[e+16>>2];d=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(u<<8)+k|0;break Rc}if(u>>>0<=143){K[e+16>>2]=d;k=(u<<9)+k|0;f=7;break Rc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}y=b?!p:p;break Qc}p=K[u+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[u+(d?8:12)>>2];while(1){Sc:{if(f){break Sc}f=K[e+16>>2];c=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(u<<8)+k|0;break Sc}if(u>>>0<=143){K[e+16>>2]=c;k=(u<<9)+k|0;f=7;break Sc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;y=d?p:!p}b=y;K[s>>2]=(i|0)==(b|0)?l:q;K[t>>2]=K[t>>2]|2048;K[n+4>>2]=K[n+4>>2]|512;g=(b^i)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){i=g>>>9|0;m=F+(L[K[e+108>>2]+(i&495)|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;Tc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;u=K[p+4>>2];if(c&32768){break Tc}s=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){Uc:{if(f){break Uc}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(p<<8)+k|0;break Uc}if(p>>>0<=143){K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break Uc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}u=b?!s:s;break Tc}s=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){Vc:{if(f){break Vc}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(p<<8)+k|0;break Vc}if(p>>>0<=143){K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break Vc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;u=d?s:!s}if(u){t=n-4|0;d=K[n+4>>2]>>>26&4|(K[t>>2]>>>28&1|(g>>>21&16|(g>>>25&64|i&170)));m=F+(L[d+24336|0]<<2)|0;u=K[m>>2];b=K[u>>2];c=c-b|0;s=o+A|0;i=L[d+24592|0];Wc:{if(k>>>16>>>0>=b>>>0){k=k-(b<<16)|0;y=K[u+4>>2];if(c&32768){break Wc}p=K[u+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[u+(b?12:8)>>2];while(1){Xc:{if(f){break Xc}f=K[e+16>>2];d=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=d;f=8;k=(u<<8)+k|0;break Xc}if(u>>>0<=143){K[e+16>>2]=d;k=(u<<9)+k|0;f=7;break Xc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}y=b?!p:p;break Wc}p=K[u+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[u+(d?8:12)>>2];while(1){Yc:{if(f){break Yc}f=K[e+16>>2];c=f+1|0;u=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=c;f=8;k=(u<<8)+k|0;break Yc}if(u>>>0<=143){K[e+16>>2]=c;k=(u<<9)+k|0;f=7;break Yc}K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;y=d?p:!p}b=y;K[s>>2]=(i|0)==(b|0)?l:q;K[t>>2]=K[t>>2]|16384;K[n+4>>2]=K[n+4>>2]|4096;d=n+(K[e+124>>2]<<2)|0;K[d+4>>2]=K[d+4>>2]|4;K[d+12>>2]=K[d+12>>2]|1;b=b^i;K[d+8>>2]=K[d+8>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}K[n>>2]=g}g=n+4|0;o=o+4|0;r=r+1|0;if((C|0)!=(r|0)){continue}break}g=n+12|0;o=o+A|0;j=j+4|0;b=K[e+128>>2];if(j>>>0<(b&-4)>>>0){continue}break}break zc}g=(b&-4)-1|0;j=(g&-4)+4|0;g=(q+(g<<1&-8)|0)+20|0}K[e+8>>2]=f;K[e+4>>2]=c;K[e>>2]=k;K[e+104>>2]=m;if(!C|b>>>0<=j>>>0){break _b}while(1){c=(b|0)==(j|0);f=0;b=j;if(!c){while(1){lc(e,g,(Q(f,C)<<2)+o|0,l,f,K[e+124>>2]+2|0,0);f=f+1|0;b=K[e+128>>2];if(f>>>0<b-j>>>0){continue}break}}g=g+4|0;o=o+4|0;v=v+1|0;if((C|0)!=(v|0)){continue}break}}break yb}while(1){v=0;while(1){q=b;n=g;g=K[g>>2];if(g){Zc:{if(g&2097168){break Zc}b=g&495;if(!b){break Zc}m=r+(L[b+K[e+108>>2]|0]<<2)|0;i=K[m>>2];b=K[i>>2];c=c-b|0;_c:{if(k>>>16>>>0<b>>>0){o=K[i+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[i+(d?8:12)>>2];while(1){$c:{if(f){break $c}f=K[e+16>>2];c=f+1|0;i=L[f+1|0];if(L[f|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break $c}K[e+16>>2]=c;k=(i<<9)+k|0;f=7;break $c}K[e+16>>2]=c;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?o:!o;break _c}k=k-(b<<16)|0;if(!(c&32768)){o=K[i+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[i+(b?12:8)>>2];while(1){ad:{if(f){break ad}f=K[e+16>>2];d=f+1|0;i=L[f+1|0];if(L[f|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break ad}K[e+16>>2]=d;k=(i<<9)+k|0;f=7;break ad}K[e+16>>2]=d;f=8;k=(i<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!o:o;break _c}b=K[i+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>17&4|(K[h>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];bd:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){cd:{if(f){break cd}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break cd}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break cd}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break bd}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){dd:{if(f){break dd}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break dd}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break dd}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break bd}b=K[p+4>>2]}K[q>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|32;K[n+4>>2]=K[n+4>>2]|8;g=(b^o)<<19|g|16}g=g|2097152}if(!(!(g&3960)|g&16777344)){o=g>>>3|0;m=r+(L[K[e+108>>2]+(o&495)|0]<<2)|0;h=K[m>>2];b=K[h>>2];c=c-b|0;ed:{if(k>>>16>>>0<b>>>0){i=K[h+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[h+(d?8:12)>>2];while(1){fd:{if(f){break fd}f=K[e+16>>2];c=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break fd}K[e+16>>2]=c;k=(h<<9)+k|0;f=7;break fd}K[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break ed}k=k-(b<<16)|0;if(!(c&32768)){i=K[h+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[h+(b?12:8)>>2];while(1){gd:{if(f){break gd}f=K[e+16>>2];d=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break gd}K[e+16>>2]=d;k=(h<<9)+k|0;f=7;break gd}K[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break ed}b=K[h+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>20&4|(K[h>>2]>>>22&1|(g>>>15&16|(g>>>19&64|o&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];hd:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){id:{if(f){break id}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break id}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break id}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break hd}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){jd:{if(f){break jd}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break jd}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break jd}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break hd}b=K[p+4>>2]}K[q+256>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|256;K[n+4>>2]=K[n+4>>2]|64;g=(b^o)<<22|g|128}g=g|16777216}if(!(!(g&31680)|g&134218752)){o=g>>>6|0;m=r+(L[K[e+108>>2]+(o&495)|0]<<2)|0;h=K[m>>2];b=K[h>>2];c=c-b|0;kd:{if(k>>>16>>>0<b>>>0){i=K[h+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[h+(d?8:12)>>2];while(1){ld:{if(f){break ld}f=K[e+16>>2];c=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break ld}K[e+16>>2]=c;k=(h<<9)+k|0;f=7;break ld}K[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break kd}k=k-(b<<16)|0;if(!(c&32768)){i=K[h+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[h+(b?12:8)>>2];while(1){md:{if(f){break md}f=K[e+16>>2];d=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break md}K[e+16>>2]=d;k=(h<<9)+k|0;f=7;break md}K[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break kd}b=K[h+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>23&4|(K[h>>2]>>>25&1|(g>>>18&16|(g>>>22&64|o&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];nd:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){od:{if(f){break od}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break od}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break od}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break nd}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){pd:{if(f){break pd}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break pd}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break pd}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break nd}b=K[p+4>>2]}K[q+512>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|2048;K[n+4>>2]=K[n+4>>2]|512;g=(b^o)<<25|g|1024}g=g|134217728}if(!(!(g&253440)|g&1073750016)){o=g>>>9|0;m=r+(L[K[e+108>>2]+(o&495)|0]<<2)|0;h=K[m>>2];b=K[h>>2];c=c-b|0;qd:{if(k>>>16>>>0<b>>>0){i=K[h+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[h+(d?8:12)>>2];while(1){rd:{if(f){break rd}f=K[e+16>>2];c=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break rd}K[e+16>>2]=c;k=(h<<9)+k|0;f=7;break rd}K[e+16>>2]=c;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break qd}k=k-(b<<16)|0;if(!(c&32768)){i=K[h+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[h+(b?12:8)>>2];while(1){sd:{if(f){break sd}f=K[e+16>>2];d=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break sd}K[e+16>>2]=d;k=(h<<9)+k|0;f=7;break sd}K[e+16>>2]=d;f=8;k=(h<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break qd}b=K[h+4>>2]}if(b){h=n-4|0;d=K[n+4>>2]>>>26&4|(K[h>>2]>>>28&1|(g>>>21&16|(g>>>25&64|o&170)));m=r+(L[d+24336|0]<<2)|0;p=K[m>>2];b=K[p>>2];c=c-b|0;o=L[d+24592|0];td:{if(k>>>16>>>0<b>>>0){i=K[p+4>>2];d=b>>>0>c>>>0;K[m>>2]=K[p+(d?8:12)>>2];while(1){ud:{if(f){break ud}f=K[e+16>>2];c=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break ud}K[e+16>>2]=c;k=(p<<9)+k|0;f=7;break ud}K[e+16>>2]=c;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;b=b<<1;if(b>>>0<32768){continue}break}c=b;b=d?i:!i;break td}k=k-(b<<16)|0;if(!(c&32768)){i=K[p+4>>2];b=b>>>0>c>>>0;K[m>>2]=K[p+(b?12:8)>>2];while(1){vd:{if(f){break vd}f=K[e+16>>2];d=f+1|0;p=L[f+1|0];if(L[f|0]==255){if(p>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;k=k+65280|0;f=8;break vd}K[e+16>>2]=d;k=(p<<9)+k|0;f=7;break vd}K[e+16>>2]=d;f=8;k=(p<<8)+k|0}f=f-1|0;k=k<<1;c=c<<1;if(c>>>0<32768){continue}break}b=b?!i:i;break td}b=K[p+4>>2]}K[q+768>>2]=(o|0)==(b|0)?j:l;K[h>>2]=K[h>>2]|16384;K[n+4>>2]=K[n+4>>2]|4096;K[n+260>>2]=K[n+260>>2]|4;K[n+268>>2]=K[n+268>>2]|1;b=b^o;K[n+264>>2]=K[n+264>>2]|b<<18|2;g=b<<28|g|8192}g=g|1073741824}K[n>>2]=g}g=n+4|0;b=q+4|0;v=v+1|0;if((v|0)!=64){continue}break}g=n+12|0;b=q+772|0;q=s>>>0<60;s=s+4|0;if(q){continue}break}}K[e+8>>2]=f;K[e+4>>2]=c;K[e>>2]=k;K[e+104>>2]=m}break _a;case 0:if(!oa){p=1<<x>>>1|0;s=K[e+124>>2];d=s<<2;b=(d+K[e+120>>2]|0)+12|0;g=K[e+116>>2];k=0;c=K[e+128>>2];if(c>>>0>=4){if(!s){break Za}o=Q(s,12);n=s<<3;i=0-p|0;while(1){c=0;while(1){l=b;b=K[b>>2];wd:{if(!b){break wd}if((b&2097168)==16){b=K[e>>2];h=K[e+8>>2];xd:{if(h){break xd}j=(b|0)==255;q=K[e+16>>2];b=L[q|0];yd:{if(!j){K[e>>2]=b;K[e+16>>2]=q+1;break yd}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=q+1;h=7;break xd}b=255;K[e>>2]=255}h=8}j=h-1|0;K[e+8>>2]=j;j=b>>>j&1;b=K[g>>2];K[g>>2]=((j|0)==(b>>>31|0)?i:p)+b;b=K[l>>2]|1048576;K[l>>2]=b}if((b&16777344)==128){b=K[e>>2];h=K[e+8>>2];zd:{if(h){break zd}j=(b|0)==255;q=K[e+16>>2];b=L[q|0];Ad:{if(!j){K[e>>2]=b;K[e+16>>2]=q+1;break Ad}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=q+1;h=7;break zd}b=255;K[e>>2]=255}h=8}q=h-1|0;K[e+8>>2]=q;j=d+g|0;f=K[j>>2];K[j>>2]=f+((b>>>q&1)==(f>>>31|0)?i:p);b=K[l>>2]|8388608;K[l>>2]=b}if((b&134218752)==1024){b=K[e>>2];h=K[e+8>>2];Bd:{if(h){break Bd}j=(b|0)==255;q=K[e+16>>2];b=L[q|0];Cd:{if(!j){K[e>>2]=b;K[e+16>>2]=q+1;break Cd}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=q+1;h=7;break Bd}b=255;K[e>>2]=255}h=8}q=h-1|0;K[e+8>>2]=q;j=g+n|0;f=K[j>>2];K[j>>2]=f+((b>>>q&1)==(f>>>31|0)?i:p);b=K[l>>2]|67108864;K[l>>2]=b}if((b&1073750016)!=8192){break wd}b=K[e>>2];h=K[e+8>>2];Dd:{if(h){break Dd}j=(b|0)==255;q=K[e+16>>2];b=L[q|0];Ed:{if(!j){K[e>>2]=b;K[e+16>>2]=q+1;break Ed}if(b>>>0<=143){K[e>>2]=b;K[e+16>>2]=q+1;h=7;break Dd}b=255;K[e>>2]=255}h=8}q=h-1|0;K[e+8>>2]=q;j=g+o|0;f=K[j>>2];K[j>>2]=f+((b>>>q&1)==(f>>>31|0)?i:p);K[l>>2]=K[l>>2]|536870912}g=g+4|0;b=l+4|0;c=c+1|0;if((s|0)!=(c|0)){continue}break}g=g+o|0;b=l+12|0;k=k+4|0;c=K[e+128>>2];if(k>>>0<(c&-4)>>>0){continue}break}}if(!s|c>>>0<=k>>>0){break _a}v=0;j=0-p|0;d=c;while(1){Fd:{if((d|0)==(k|0)){d=k;break Fd}h=K[b>>2];i=0;while(1){d=Q(i,3);if((2097168<<d&h)==16<<d){n=(Q(i,s)<<2)+g|0;c=K[e>>2];m=K[e+8>>2];Gd:{if(m){break Gd}l=(c|0)!=255;q=K[e+16>>2];c=L[q|0];Hd:{if(!l){if(c>>>0>=144){c=255;K[e>>2]=255;break Hd}K[e>>2]=c;K[e+16>>2]=q+1;m=7;break Gd}K[e>>2]=c;K[e+16>>2]=q+1}m=8}l=m-1|0;K[e+8>>2]=l;l=c>>>l&1;c=K[n>>2];K[n>>2]=((l|0)==(c>>>31|0)?j:p)+c;h=K[b>>2]|1048576<<d;K[b>>2]=h;c=K[e+128>>2]}i=i+1|0;d=c;if(i>>>0<c-k>>>0){continue}break}}g=g+4|0;b=b+4|0;v=v+1|0;if((s|0)!=(v|0)){continue}break}break _a}j=K[e+120>>2];d=K[e+116>>2];A=K[e+124>>2];c=K[e+128>>2];if(!((A|0)!=64|(c|0)!=64)){c=j+268|0;s=0;r=1<<x>>>1|0;p=0-r|0;i=K[e+8>>2];g=K[e+4>>2];b=K[e>>2];k=K[e+104>>2];while(1){m=0;while(1){q=d;j=c;d=K[c>>2];if(d){l=c;if((d&2097168)==16){k=da+((d&1048576?16:d&495?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;Id:{if(b>>>16>>>0<c>>>0){o=K[f+4>>2];n=c>>>0>g>>>0;K[k>>2]=K[f+(n?8:12)>>2];while(1){Jd:{if(i){break Jd}f=K[e+16>>2];g=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Jd}K[e+16>>2]=g;b=(h<<9)+b|0;i=7;break Jd}K[e+16>>2]=g;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?o:!o;break Id}b=b-(c<<16)|0;if(!(g&32768)){o=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){Kd:{if(i){break Kd}f=K[e+16>>2];n=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Kd}K[e+16>>2]=n;b=(h<<9)+b|0;i=7;break Kd}K[e+16>>2]=n;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!o:o;break Id}n=K[f+4>>2]}c=K[q>>2];K[q>>2]=((n|0)==(c>>>31|0)?p:r)+c;d=d|1048576}if((d&16777344)==128){k=da+((d&8388608?16:d&3960?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;Ld:{if(b>>>16>>>0<c>>>0){o=K[f+4>>2];n=c>>>0>g>>>0;K[k>>2]=K[f+(n?8:12)>>2];while(1){Md:{if(i){break Md}f=K[e+16>>2];g=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Md}K[e+16>>2]=g;b=(h<<9)+b|0;i=7;break Md}K[e+16>>2]=g;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?o:!o;break Ld}b=b-(c<<16)|0;if(!(g&32768)){o=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){Nd:{if(i){break Nd}f=K[e+16>>2];n=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Nd}K[e+16>>2]=n;b=(h<<9)+b|0;i=7;break Nd}K[e+16>>2]=n;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!o:o;break Ld}n=K[f+4>>2]}c=K[q+256>>2];K[q+256>>2]=((n|0)==(c>>>31|0)?p:r)+c;d=d|8388608}if((d&134218752)==1024){k=da+((d&67108864?16:d&31680?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;Od:{if(b>>>16>>>0<c>>>0){o=K[f+4>>2];n=c>>>0>g>>>0;K[k>>2]=K[f+(n?8:12)>>2];while(1){Pd:{if(i){break Pd}f=K[e+16>>2];g=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Pd}K[e+16>>2]=g;b=(h<<9)+b|0;i=7;break Pd}K[e+16>>2]=g;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?o:!o;break Od}b=b-(c<<16)|0;if(!(g&32768)){o=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){Qd:{if(i){break Qd}f=K[e+16>>2];n=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Qd}K[e+16>>2]=n;b=(h<<9)+b|0;i=7;break Qd}K[e+16>>2]=n;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!o:o;break Od}n=K[f+4>>2]}c=K[q+512>>2];K[q+512>>2]=((n|0)==(c>>>31|0)?p:r)+c;d=d|67108864}if((d&1073750016)==8192){k=da+((d&536870912?16:d&253440?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;Rd:{if(b>>>16>>>0<c>>>0){o=K[f+4>>2];n=c>>>0>g>>>0;K[k>>2]=K[f+(n?8:12)>>2];while(1){Sd:{if(i){break Sd}f=K[e+16>>2];g=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Sd}K[e+16>>2]=g;b=(h<<9)+b|0;i=7;break Sd}K[e+16>>2]=g;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;n=n?o:!o;break Rd}b=b-(c<<16)|0;if(!(g&32768)){o=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){Td:{if(i){break Td}f=K[e+16>>2];n=f+1|0;h=L[f+1|0];if(L[f|0]==255){if(h>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8;break Td}K[e+16>>2]=n;b=(h<<9)+b|0;i=7;break Td}K[e+16>>2]=n;i=8;b=(h<<8)+b|0}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}n=c?!o:o;break Rd}n=K[f+4>>2]}c=K[q+768>>2];K[q+768>>2]=((n|0)==(c>>>31|0)?p:r)+c;d=d|536870912}K[l>>2]=d}c=j+4|0;d=q+4|0;m=m+1|0;if((m|0)!=64){continue}break}c=j+12|0;d=q+772|0;l=s>>>0<60;s=s+4|0;if(l){continue}break}K[e+8>>2]=i;K[e+4>>2]=g;K[e>>2]=b;K[e+104>>2]=k;break _a}v=1<<x>>>1|0;s=A<<2;f=(s+j|0)+12|0;i=K[e+8>>2];g=K[e+4>>2];b=K[e>>2];k=K[e+104>>2];o=0;Ud:{if(c>>>0<4){break Ud}if(A){p=Q(A,12);n=A<<3;t=0-v|0;while(1){m=0;while(1){l=f;j=K[f>>2];if(j){if((j&2097168)==16){k=da+((j&1048576?16:j&495?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;Vd:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=K[f+4>>2];if(g&32768){break Vd}h=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){Wd:{if(i){break Wd}f=K[e+16>>2];q=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=q;i=8;b=(r<<8)+b|0;break Wd}if(r>>>0<=143){K[e+16>>2]=q;b=(r<<9)+b|0;i=7;break Wd}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!h:h;break Vd}h=K[f+4>>2];q=c>>>0>g>>>0;K[k>>2]=K[f+(q?8:12)>>2];while(1){Xd:{if(i){break Xd}f=K[e+16>>2];g=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=g;i=8;b=(r<<8)+b|0;break Xd}if(r>>>0<=143){K[e+16>>2]=g;b=(r<<9)+b|0;i=7;break Xd}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?h:!h}c=K[d>>2];K[d>>2]=((q|0)==(c>>>31|0)?t:v)+c;j=j|1048576}if((j&16777344)==128){k=da+((j&8388608?16:j&3960?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;Yd:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=K[f+4>>2];if(g&32768){break Yd}h=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){Zd:{if(i){break Zd}f=K[e+16>>2];q=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=q;i=8;b=(r<<8)+b|0;break Zd}if(r>>>0<=143){K[e+16>>2]=q;b=(r<<9)+b|0;i=7;break Zd}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!h:h;break Yd}h=K[f+4>>2];q=c>>>0>g>>>0;K[k>>2]=K[f+(q?8:12)>>2];while(1){_d:{if(i){break _d}f=K[e+16>>2];g=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=g;i=8;b=(r<<8)+b|0;break _d}if(r>>>0<=143){K[e+16>>2]=g;b=(r<<9)+b|0;i=7;break _d}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?h:!h}f=q;c=d+s|0;q=K[c>>2];K[c>>2]=q+((f|0)==(q>>>31|0)?t:v);j=j|8388608}if((j&134218752)==1024){k=da+((j&67108864?16:j&31680?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;$d:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=K[f+4>>2];if(g&32768){break $d}h=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){ae:{if(i){break ae}f=K[e+16>>2];q=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=q;i=8;b=(r<<8)+b|0;break ae}if(r>>>0<=143){K[e+16>>2]=q;b=(r<<9)+b|0;i=7;break ae}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!h:h;break $d}h=K[f+4>>2];q=c>>>0>g>>>0;K[k>>2]=K[f+(q?8:12)>>2];while(1){be:{if(i){break be}f=K[e+16>>2];g=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=g;i=8;b=(r<<8)+b|0;break be}if(r>>>0<=143){K[e+16>>2]=g;b=(r<<9)+b|0;i=7;break be}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?h:!h}f=q;c=d+n|0;q=K[c>>2];K[c>>2]=q+((f|0)==(q>>>31|0)?t:v);j=j|67108864}if((j&1073750016)==8192){k=da+((j&536870912?16:j&253440?15:14)<<2)|0;f=K[k>>2];c=K[f>>2];g=g-c|0;ce:{if(b>>>16>>>0>=c>>>0){b=b-(c<<16)|0;q=K[f+4>>2];if(g&32768){break ce}h=K[f+4>>2];c=c>>>0>g>>>0;K[k>>2]=K[f+(c?12:8)>>2];while(1){de:{if(i){break de}f=K[e+16>>2];q=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=q;i=8;b=(r<<8)+b|0;break de}if(r>>>0<=143){K[e+16>>2]=q;b=(r<<9)+b|0;i=7;break de}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;g=g<<1;if(g>>>0<32768){continue}break}q=c?!h:h;break ce}h=K[f+4>>2];q=c>>>0>g>>>0;K[k>>2]=K[f+(q?8:12)>>2];while(1){ee:{if(i){break ee}f=K[e+16>>2];g=f+1|0;r=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=g;i=8;b=(r<<8)+b|0;break ee}if(r>>>0<=143){K[e+16>>2]=g;b=(r<<9)+b|0;i=7;break ee}K[e+12>>2]=K[e+12>>2]+1;b=b+65280|0;i=8}i=i-1|0;b=b<<1;c=c<<1;if(c>>>0<32768){continue}break}g=c;q=q?h:!h}f=q;c=d+p|0;q=K[c>>2];K[c>>2]=q+((f|0)==(q>>>31|0)?t:v);j=j|536870912}K[l>>2]=j}f=l+4|0;d=d+4|0;m=m+1|0;if((A|0)!=(m|0)){continue}break}f=l+12|0;d=d+p|0;o=o+4|0;c=K[e+128>>2];if(o>>>0<(c&-4)>>>0){continue}break}break Ud}l=(c&-4)-1|0;o=(l&-4)+4|0;f=(j+(l<<1&-8)|0)+20|0}K[e+8>>2]=i;K[e+4>>2]=g;K[e>>2]=b;K[e+104>>2]=k;if(!A|c>>>0<=o>>>0){break _a}r=0;l=0-v|0;b=c;while(1){fe:{if((b|0)==(o|0)){b=o;break fe}i=K[f>>2];h=0;while(1){m=Q(h,3);if((2097168<<m&i)==16<<m){k=(Q(h,A)<<2)+d|0;b=i>>>m|0;j=da+((b&1048576?16:b&495?15:14)<<2)|0;K[e+104>>2]=j;q=K[j>>2];b=K[q>>2];c=K[e+4>>2]-b|0;K[e+4>>2]=c;g=K[e>>2];ge:{if(g>>>16>>>0<b>>>0){n=K[q+4>>2];K[e+4>>2]=b;c=b>>>0>c>>>0;K[j>>2]=K[q+(c?8:12)>>2];i=K[e+8>>2];while(1){he:{if(i){break he}q=K[e+16>>2];j=q+1|0;s=L[q+1|0];if(L[q|0]==255){if(s>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;g=g+65280|0;i=8;break he}K[e+16>>2]=j;g=(s<<9)+g|0;i=7;break he}K[e+16>>2]=j;i=8;g=(s<<8)+g|0}i=i-1|0;K[e+8>>2]=i;g=g<<1;K[e>>2]=g;b=b<<1;K[e+4>>2]=b;if(b>>>0<32768){continue}break}c=c?n:!n;break ge}g=g-(b<<16)|0;K[e>>2]=g;if(!(c&32768)){n=K[q+4>>2];b=b>>>0>c>>>0;K[j>>2]=K[q+(b?12:8)>>2];i=K[e+8>>2];while(1){ie:{if(i){break ie}q=K[e+16>>2];j=q+1|0;s=L[q+1|0];if(L[q|0]==255){if(s>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;g=g+65280|0;i=8;break ie}K[e+16>>2]=j;g=(s<<9)+g|0;i=7;break ie}K[e+16>>2]=j;i=8;g=(s<<8)+g|0}i=i-1|0;K[e+8>>2]=i;g=g<<1;K[e>>2]=g;c=c<<1;K[e+4>>2]=c;if(c>>>0<32768){continue}break}c=b?!n:n;break ge}c=K[q+4>>2]}b=K[k>>2];K[k>>2]=((c|0)==(b>>>31|0)?l:v)+b;i=K[f>>2]|1048576<<m;K[f>>2]=i;c=K[e+128>>2]}h=h+1|0;b=c;if(h>>>0<b-o>>>0){continue}break}}f=f+4|0;d=d+4|0;r=r+1|0;if((A|0)!=(r|0)){continue}break};break _a;case 1:break $a}}F=0;s=0;je:{ke:{le:{W=K[e+124>>2];if(!((W|0)!=64|K[e+128>>2]!=64)){b=1<<x;A=b>>>1|b;v=0-A|0;q=e+100|0;l=e+96|0;u=e+28|0;g=K[e+120>>2]+268|0;h=K[e+8>>2];b=K[e+4>>2];d=K[e>>2];j=K[e+104>>2];c=K[e+116>>2];if(Z&8){break le}while(1){p=0;while(1){k=c;f=g;g=K[g>>2];me:{ne:{oe:{if(!g){j=K[l>>2];g=K[j>>2];b=b-g|0;pe:{if(d>>>16>>>0<g>>>0){n=K[j+4>>2];c=b>>>0<g>>>0;K[l>>2]=K[j+(c?8:12)>>2];while(1){qe:{if(h){break qe}j=K[e+16>>2];b=j+1|0;o=L[j+1|0];if(L[j|0]==255){if(o>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break qe}K[e+16>>2]=b;d=(o<<9)+d|0;h=7;break qe}K[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?n:!n;break pe}d=d-(g<<16)|0;if(!(b&32768)){n=K[j+4>>2];c=b>>>0<g>>>0;K[l>>2]=K[j+(c?12:8)>>2];while(1){re:{if(h){break re}j=K[e+16>>2];g=j+1|0;o=L[j+1|0];if(L[j|0]==255){if(o>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break re}K[e+16>>2]=g;d=(o<<9)+d|0;h=7;break re}K[e+16>>2]=g;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!n:n;break pe}c=K[j+4>>2]}if(!c){j=l;break me}c=K[q>>2];g=K[c>>2];b=b-g|0;se:{if(d>>>16>>>0<g>>>0){o=K[c+4>>2];j=b>>>0<g>>>0;c=K[(j?8:12)+c>>2];K[q>>2]=c;while(1){te:{if(h){break te}n=K[e+16>>2];b=n+1|0;i=L[n+1|0];if(L[n|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break te}K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break te}K[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;n=j?o:!o;break se}d=d-(g<<16)|0;if(!(b&32768)){o=K[c+4>>2];g=b>>>0<g>>>0;c=K[(g?12:8)+c>>2];K[q>>2]=c;while(1){ue:{if(h){break ue}n=K[e+16>>2];j=n+1|0;i=L[n+1|0];if(L[n|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ue}K[e+16>>2]=j;d=(i<<9)+d|0;h=7;break ue}K[e+16>>2]=j;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=g?!o:o;break se}n=K[c+4>>2]}g=K[c>>2];b=b-g|0;ve:{if(d>>>16>>>0<g>>>0){o=K[c+4>>2];j=c;c=b>>>0<g>>>0;K[q>>2]=K[j+(c?8:12)>>2];while(1){we:{if(h){break we}j=K[e+16>>2];b=j+1|0;i=L[j+1|0];if(L[j|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break we}K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break we}K[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?o:!o;break ve}d=d-(g<<16)|0;if(!(b&32768)){o=K[c+4>>2];j=c;c=b>>>0<g>>>0;K[q>>2]=K[j+(c?12:8)>>2];while(1){xe:{if(h){break xe}j=K[e+16>>2];g=j+1|0;i=L[j+1|0];if(L[j|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break xe}K[e+16>>2]=g;d=(i<<9)+d|0;h=7;break xe}K[e+16>>2]=g;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!o:o;break ve}c=K[c+4>>2]}g=0;j=q;ye:{ze:{Ae:{Be:{Ce:{switch(c|n<<1){case 0:i=f-4|0;j=K[f+4>>2]>>>17&4|K[i>>2]>>>19&1;c=u+(L[j+24336|0]<<2)|0;n=K[c>>2];g=K[n>>2];b=b-g|0;De:{if(d>>>16>>>0<g>>>0){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?8:12)>>2];while(1){Ee:{if(h){break Ee}n=K[e+16>>2];b=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ee}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Ee}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;n=c?o:!o;break De}d=d-(g<<16)|0;if(!(b&32768)){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?12:8)>>2];while(1){Fe:{if(h){break Fe}n=K[e+16>>2];g=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Fe}K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break Fe}K[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!o:o;break De}n=K[n+4>>2]}g=L[j+24592|0];K[k>>2]=(n|0)==(g|0)?A:v;K[i>>2]=K[i>>2]|32;K[f+4>>2]=K[f+4>>2]|8;c=f-268|0;K[c>>2]=K[c>>2]|131072;c=f-260|0;K[c>>2]=K[c>>2]|32768;c=f-264|0;j=c;i=K[c>>2];c=g^n;K[j>>2]=i|c<<31|65536;j=c<<19;i=K[e+108>>2];c=u+(L[i+2|0]<<2)|0;n=K[c>>2];g=K[n>>2];b=b-g|0;Ge:{if(d>>>16>>>0<g>>>0){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?8:12)>>2];while(1){He:{if(h){break He}n=K[e+16>>2];b=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break He}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break He}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?o:!o;break Ge}d=d-(g<<16)|0;if(!(b&32768)){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?12:8)>>2];while(1){Ie:{if(h){break Ie}n=K[e+16>>2];g=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ie}K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break Ie}K[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!o:o;break Ge}c=K[n+4>>2]}g=j|16;if(!c){break Be}break;case 1:break Ce;case 2:break Ae;case 3:break ye;default:break ne}}m=f-4|0;n=K[f+4>>2]>>>20&4|(K[m>>2]>>>22&1|(g>>>15&16|(g>>>19&64|g>>>3&170)));j=u+(L[n+24336|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;Je:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];y=j;j=b>>>0<c>>>0;K[y>>2]=K[o+(j?8:12)>>2];while(1){Ke:{if(h){break Ke}o=K[e+16>>2];b=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ke}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break Ke}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break Je}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){Le:{if(h){break Le}o=K[e+16>>2];j=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Le}K[e+16>>2]=j;d=(r<<9)+d|0;h=7;break Le}K[e+16>>2]=j;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break Je}j=K[o+4>>2]}c=L[n+24592|0];K[k+256>>2]=(j|0)==(c|0)?A:v;K[m>>2]=K[m>>2]|256;K[f+4>>2]=K[f+4>>2]|64;i=K[e+108>>2];g=(c^j)<<22|g|128}j=u+(L[(g>>>6&495)+i|0]<<2)|0;n=K[j>>2];c=K[n>>2];b=b-c|0;Me:{if(d>>>16>>>0<c>>>0){o=K[n+4>>2];y=j;j=b>>>0<c>>>0;K[y>>2]=K[n+(j?8:12)>>2];while(1){Ne:{if(h){break Ne}n=K[e+16>>2];b=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ne}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Ne}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=j?o:!o;break Me}d=d-(c<<16)|0;if(!(b&32768)){o=K[n+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[n+(c?12:8)>>2];while(1){Oe:{if(h){break Oe}n=K[e+16>>2];j=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Oe}K[e+16>>2]=j;d=(m<<9)+d|0;h=7;break Oe}K[e+16>>2]=j;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!o:o;break Me}c=K[n+4>>2]}if(!c){break ze}}m=f-4|0;n=K[f+4>>2]>>>23&4|(K[m>>2]>>>25&1|(g>>>18&16|(g>>>22&64|g>>>6&170)));j=u+(L[n+24336|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;Pe:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];y=j;j=b>>>0<c>>>0;K[y>>2]=K[o+(j?8:12)>>2];while(1){Qe:{if(h){break Qe}o=K[e+16>>2];b=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Qe}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break Qe}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break Pe}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){Re:{if(h){break Re}o=K[e+16>>2];j=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Re}K[e+16>>2]=j;d=(r<<9)+d|0;h=7;break Re}K[e+16>>2]=j;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break Pe}j=K[o+4>>2]}c=L[n+24592|0];K[k+512>>2]=(j|0)==(c|0)?A:v;K[m>>2]=K[m>>2]|2048;K[f+4>>2]=K[f+4>>2]|512;g=(c^j)<<25|g|1024;i=K[e+108>>2]}j=u+(L[(g>>>9&495)+i|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;Se:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[o+(n?8:12)>>2];while(1){Te:{if(h){break Te}o=K[e+16>>2];b=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Te}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Te}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break Se}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){Ue:{if(h){break Ue}o=K[e+16>>2];n=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ue}K[e+16>>2]=n;d=(m<<9)+d|0;h=7;break Ue}K[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break Se}c=K[o+4>>2]}if(!c){break ne}}F=f-4|0;t=K[f+4>>2]>>>26&4|(K[F>>2]>>>28&1|(g>>>21&16|(g>>>25&64|g>>>9&170)));j=u+(L[t+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;break oe}Ve:{if(g&2097168){break Ve}j=u+(L[K[e+108>>2]+(g&495)|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;We:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[o+(n?8:12)>>2];while(1){Xe:{if(h){break Xe}o=K[e+16>>2];b=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Xe}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Xe}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break We}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){Ye:{if(h){break Ye}o=K[e+16>>2];n=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ye}K[e+16>>2]=n;d=(m<<9)+d|0;h=7;break Ye}K[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break We}c=K[o+4>>2]}if(!c){break Ve}r=f-4|0;o=K[f+4>>2]>>>17&4|(K[r>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));j=u+(L[o+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;Ze:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){_e:{if(h){break _e}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break _e}K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break _e}K[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;i=n?m:!m;break Ze}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){$e:{if(h){break $e}i=K[e+16>>2];n=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break $e}K[e+16>>2]=n;d=(t<<9)+d|0;h=7;break $e}K[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=c?!m:m;break Ze}i=K[i+4>>2]}n=L[o+24592|0];K[k>>2]=(i|0)==(n|0)?A:v;K[r>>2]=K[r>>2]|32;K[f+4>>2]=K[f+4>>2]|8;c=f-268|0;K[c>>2]=K[c>>2]|131072;c=f-260|0;K[c>>2]=K[c>>2]|32768;c=f-264|0;o=c;y=K[c>>2];c=i^n;K[o>>2]=y|c<<31|65536;g=c<<19|g|16}af:{if(g&16777344){break af}o=g>>>3|0;j=u+(L[K[e+108>>2]+(o&495)|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;bf:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){cf:{if(h){break cf}i=K[e+16>>2];b=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break cf}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break cf}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break bf}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){df:{if(h){break df}i=K[e+16>>2];n=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break df}K[e+16>>2]=n;d=(r<<9)+d|0;h=7;break df}K[e+16>>2]=n;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break bf}c=K[i+4>>2]}if(!c){break af}r=f-4|0;o=K[f+4>>2]>>>20&4|(K[r>>2]>>>22&1|(g>>>15&16|(g>>>19&64|o&170)));j=u+(L[o+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;ef:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){ff:{if(h){break ff}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ff}K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break ff}K[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break ef}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){gf:{if(h){break gf}i=K[e+16>>2];n=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break gf}K[e+16>>2]=n;d=(t<<9)+d|0;h=7;break gf}K[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break ef}n=K[i+4>>2]}c=L[o+24592|0];K[k+256>>2]=(n|0)==(c|0)?A:v;K[r>>2]=K[r>>2]|256;K[f+4>>2]=K[f+4>>2]|64;g=(c^n)<<22|g|128}hf:{if(g&134218752){break hf}o=g>>>6|0;j=u+(L[K[e+108>>2]+(o&495)|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;jf:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){kf:{if(h){break kf}i=K[e+16>>2];b=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break kf}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break kf}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break jf}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){lf:{if(h){break lf}i=K[e+16>>2];n=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break lf}K[e+16>>2]=n;d=(r<<9)+d|0;h=7;break lf}K[e+16>>2]=n;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break jf}c=K[i+4>>2]}if(!c){break hf}r=f-4|0;o=K[f+4>>2]>>>23&4|(K[r>>2]>>>25&1|(g>>>18&16|(g>>>22&64|o&170)));j=u+(L[o+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;mf:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){nf:{if(h){break nf}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break nf}K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break nf}K[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break mf}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){of:{if(h){break of}i=K[e+16>>2];n=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break of}K[e+16>>2]=n;d=(t<<9)+d|0;h=7;break of}K[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break mf}n=K[i+4>>2]}c=L[o+24592|0];K[k+512>>2]=(n|0)==(c|0)?A:v;K[r>>2]=K[r>>2]|2048;K[f+4>>2]=K[f+4>>2]|512;g=(c^n)<<25|g|1024}if(g&1073750016){break ne}o=g>>>9|0;j=u+(L[K[e+108>>2]+(o&495)|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;pf:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){qf:{if(h){break qf}i=K[e+16>>2];b=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break qf}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break qf}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break pf}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){rf:{if(h){break rf}i=K[e+16>>2];n=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break rf}K[e+16>>2]=n;d=(r<<9)+d|0;h=7;break rf}K[e+16>>2]=n;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break pf}c=K[i+4>>2]}if(!c){break ne}F=f-4|0;t=K[f+4>>2]>>>26&4|(K[F>>2]>>>28&1|(g>>>21&16|(g>>>25&64|o&170)));j=u+(L[t+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0}sf:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[(n?8:12)+i>>2];while(1){tf:{if(h){break tf}o=K[e+16>>2];b=o+1|0;i=L[o+1|0];if(L[o|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break tf}K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break tf}K[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break sf}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[(c?12:8)+i>>2];while(1){uf:{if(h){break uf}o=K[e+16>>2];n=o+1|0;i=L[o+1|0];if(L[o|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break uf}K[e+16>>2]=n;d=(i<<9)+d|0;h=7;break uf}K[e+16>>2]=n;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break sf}n=K[i+4>>2]}c=L[t+24592|0];K[k+768>>2]=(n|0)==(c|0)?A:v;K[F>>2]=K[F>>2]|16384;K[f+4>>2]=K[f+4>>2]|4096;K[f+260>>2]=K[f+260>>2]|4;K[f+268>>2]=K[f+268>>2]|1;c=c^n;K[f+264>>2]=K[f+264>>2]|c<<18|2;g=c<<28|g|8192}K[f>>2]=g&-1226833921}g=f+4|0;c=k+4|0;p=p+1|0;if((p|0)!=64){continue}break}g=f+12|0;c=k+772|0;n=s>>>0<60;s=s+4|0;if(n){continue}break}break ke}b=1<<x;y=b>>>1|b;l=K[e+120>>2];c=(l+(W<<2)|0)+12|0;g=K[e+128>>2];h=K[e+8>>2];b=K[e+4>>2];d=K[e>>2];j=K[e+104>>2];o=K[e+116>>2];if(Z&8){vf:{if(g>>>0<4){break vf}if(W){n=e+100|0;q=e+96|0;v=Q(W,12);r=W<<3;u=0-y|0;C=e+28|0;while(1){A=0;while(1){k=c;c=K[c>>2];wf:{xf:{yf:{if(c){zf:{if(c&2097168){break zf}j=C+(L[K[e+108>>2]+(c&495)|0]<<2)|0;f=K[j>>2];g=K[f>>2];b=b-g|0;Af:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[f+4>>2];if(b&32768){break Af}i=K[f+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[f+(g?12:8)>>2];while(1){Bf:{if(h){break Bf}f=K[e+16>>2];l=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Bf}if(m>>>0<=143){K[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Bf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break Af}i=K[f+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[f+(l?8:12)>>2];while(1){Cf:{if(h){break Cf}f=K[e+16>>2];b=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Cf}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Cf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break zf}p=k-4|0;f=K[k+4>>2]>>>17&4|(K[p>>2]>>>19&1|(c>>>14&16|(c>>>16&64|c&170)));j=C+(L[f+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Df:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Df}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Ef:{if(h){break Ef}i=K[e+16>>2];l=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Ef}if(t>>>0<=143){K[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Ef}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Df}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Ff:{if(h){break Ff}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Ff}if(t>>>0<=143){K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Ff}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[f+24592|0];K[o>>2]=(l|0)==(g|0)?y:u;K[p>>2]=K[p>>2]|32;K[k+4>>2]=K[k+4>>2]|8;c=(g^l)<<19|c|16}Gf:{if(c&16777344){break Gf}f=c>>>3|0;j=C+(L[K[e+108>>2]+(f&495)|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Hf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Hf}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){If:{if(h){break If}i=K[e+16>>2];l=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break If}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break If}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Hf}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Jf:{if(h){break Jf}i=K[e+16>>2];b=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Jf}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Jf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Gf}p=k-4|0;f=K[k+4>>2]>>>20&4|(K[p>>2]>>>22&1|(c>>>15&16|(c>>>19&64|f&170)));j=C+(L[f+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;z=(W<<2)+o|0;Kf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Kf}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Lf:{if(h){break Lf}i=K[e+16>>2];l=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Lf}if(t>>>0<=143){K[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Lf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Kf}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Mf:{if(h){break Mf}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Mf}if(t>>>0<=143){K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Mf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[f+24592|0];K[z>>2]=(l|0)==(g|0)?y:u;K[p>>2]=K[p>>2]|256;K[k+4>>2]=K[k+4>>2]|64;c=(g^l)<<22|c|128}Nf:{if(c&134218752){break Nf}f=c>>>6|0;j=C+(L[K[e+108>>2]+(f&495)|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Of:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Of}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Pf:{if(h){break Pf}i=K[e+16>>2];l=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break Pf}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break Pf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Of}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Qf:{if(h){break Qf}i=K[e+16>>2];b=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Qf}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Qf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Nf}p=k-4|0;f=K[k+4>>2]>>>23&4|(K[p>>2]>>>25&1|(c>>>18&16|(c>>>22&64|f&170)));j=C+(L[f+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;z=o+r|0;Rf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Rf}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Sf:{if(h){break Sf}i=K[e+16>>2];l=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Sf}if(t>>>0<=143){K[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Sf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Rf}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Tf:{if(h){break Tf}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Tf}if(t>>>0<=143){K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Tf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[f+24592|0];K[z>>2]=(l|0)==(g|0)?y:u;K[p>>2]=K[p>>2]|2048;K[k+4>>2]=K[k+4>>2]|512;c=(g^l)<<25|c|1024}if(c&1073750016){break xf}f=c>>>9|0;j=C+(L[K[e+108>>2]+(f&495)|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Uf:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Uf}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Vf:{if(h){break Vf}i=K[e+16>>2];l=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break Vf}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break Vf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Uf}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Wf:{if(h){break Wf}i=K[e+16>>2];b=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Wf}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Wf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break xf}t=k-4|0;Y=K[k+4>>2]>>>26&4|(K[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|f&170)));j=C+(L[Y+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;break yf}l=K[q>>2];c=K[l>>2];b=b-c|0;Xf:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;g=K[l+4>>2];if(b&32768){break Xf}j=K[l+4>>2];c=b>>>0<c>>>0;K[q>>2]=K[l+(c?12:8)>>2];while(1){Yf:{if(h){break Yf}l=K[e+16>>2];g=l+1|0;f=L[l+1|0];if(L[l|0]!=255){K[e+16>>2]=g;h=8;d=(f<<8)+d|0;break Yf}if(f>>>0<=143){K[e+16>>2]=g;d=(f<<9)+d|0;h=7;break Yf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}g=c?!j:j;break Xf}j=K[l+4>>2];g=b>>>0<c>>>0;K[q>>2]=K[l+(g?8:12)>>2];while(1){Zf:{if(h){break Zf}l=K[e+16>>2];b=l+1|0;f=L[l+1|0];if(L[l|0]!=255){K[e+16>>2]=b;h=8;d=(f<<8)+d|0;break Zf}if(f>>>0<=143){K[e+16>>2]=b;d=(f<<9)+d|0;h=7;break Zf}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;g=g?j:!j}if(!g){j=q;break wf}g=K[n>>2];c=K[g>>2];b=b-c|0;_f:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;l=K[g+4>>2];if(b&32768){break _f}f=K[g+4>>2];c=b>>>0<c>>>0;g=K[(c?12:8)+g>>2];K[n>>2]=g;while(1){$f:{if(h){break $f}j=K[e+16>>2];l=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=l;h=8;d=(i<<8)+d|0;break $f}if(i>>>0<=143){K[e+16>>2]=l;d=(i<<9)+d|0;h=7;break $f}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=c?!f:f;break _f}f=K[g+4>>2];l=b>>>0<c>>>0;g=K[(l?8:12)+g>>2];K[n>>2]=g;while(1){ag:{if(h){break ag}j=K[e+16>>2];b=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(i<<8)+d|0;break ag}if(i>>>0<=143){K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break ag}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;l=l?f:!f}c=K[g>>2];b=b-c|0;bg:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;j=K[g+4>>2];if(b&32768){break bg}f=K[g+4>>2];c=b>>>0<c>>>0;K[n>>2]=K[(c?12:8)+g>>2];while(1){cg:{if(h){break cg}j=K[e+16>>2];g=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=g;h=8;d=(i<<8)+d|0;break cg}if(i>>>0<=143){K[e+16>>2]=g;d=(i<<9)+d|0;h=7;break cg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!f:f;break bg}f=K[g+4>>2];j=g;g=b>>>0<c>>>0;K[n>>2]=K[j+(g?8:12)>>2];while(1){dg:{if(h){break dg}j=K[e+16>>2];b=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(i<<8)+d|0;break dg}if(i>>>0<=143){K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break dg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=g?f:!f}g=j;c=0;j=n;eg:{fg:{gg:{hg:{ig:{switch(g|l<<1){case 0:i=k-4|0;l=K[k+4>>2]>>>17&4|K[i>>2]>>>19&1;g=C+(L[l+24336|0]<<2)|0;j=K[g>>2];c=K[j>>2];b=b-c|0;jg:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=K[j+4>>2];if(b&32768){break jg}f=K[j+4>>2];c=b>>>0<c>>>0;K[g>>2]=K[j+(c?12:8)>>2];while(1){kg:{if(h){break kg}j=K[e+16>>2];g=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=g;h=8;d=(m<<8)+d|0;break kg}if(m>>>0<=143){K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break kg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break jg}f=K[j+4>>2];m=g;g=b>>>0<c>>>0;K[m>>2]=K[j+(g?8:12)>>2];while(1){lg:{if(h){break lg}j=K[e+16>>2];b=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break lg}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break lg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=L[l+24592|0];K[o>>2]=(g|0)==(c|0)?y:u;K[i>>2]=K[i>>2]|32;K[k+4>>2]=K[k+4>>2]|8;l=(c^g)<<19;i=K[e+108>>2];g=C+(L[i+2|0]<<2)|0;j=K[g>>2];c=K[j>>2];b=b-c|0;mg:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=K[j+4>>2];if(b&32768){break mg}f=K[j+4>>2];c=b>>>0<c>>>0;K[g>>2]=K[j+(c?12:8)>>2];while(1){ng:{if(h){break ng}j=K[e+16>>2];g=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=g;h=8;d=(m<<8)+d|0;break ng}if(m>>>0<=143){K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break ng}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break mg}f=K[j+4>>2];m=g;g=b>>>0<c>>>0;K[m>>2]=K[j+(g?8:12)>>2];while(1){og:{if(h){break og}j=K[e+16>>2];b=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break og}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break og}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=l|16;if(!g){break hg}break;case 1:break ig;case 2:break gg;case 3:break eg;default:break xf}}m=k-4|0;j=K[k+4>>2]>>>20&4|(K[m>>2]>>>22&1|(c>>>15&16|(c>>>19&64|c>>>3&170)));l=C+(L[j+24336|0]<<2)|0;f=K[l>>2];g=K[f>>2];b=b-g|0;t=(W<<2)+o|0;pg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=K[f+4>>2];if(b&32768){break pg}i=K[f+4>>2];g=b>>>0<g>>>0;K[l>>2]=K[f+(g?12:8)>>2];while(1){qg:{if(h){break qg}f=K[e+16>>2];l=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break qg}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break qg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break pg}i=K[f+4>>2];p=l;l=b>>>0<g>>>0;K[p>>2]=K[f+(l?8:12)>>2];while(1){rg:{if(h){break rg}f=K[e+16>>2];b=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break rg}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break rg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=L[j+24592|0];K[t>>2]=(l|0)==(g|0)?y:u;K[m>>2]=K[m>>2]|256;K[k+4>>2]=K[k+4>>2]|64;i=K[e+108>>2];c=(g^l)<<22|c|128}l=C+(L[(c>>>6&495)+i|0]<<2)|0;j=K[l>>2];g=K[j>>2];b=b-g|0;sg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;f=K[j+4>>2];if(b&32768){break sg}f=K[j+4>>2];g=b>>>0<g>>>0;K[l>>2]=K[j+(g?12:8)>>2];while(1){tg:{if(h){break tg}j=K[e+16>>2];l=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=l;h=8;d=(m<<8)+d|0;break tg}if(m>>>0<=143){K[e+16>>2]=l;d=(m<<9)+d|0;h=7;break tg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=g?!f:f;break sg}f=K[j+4>>2];m=l;l=b>>>0<g>>>0;K[m>>2]=K[j+(l?8:12)>>2];while(1){ug:{if(h){break ug}j=K[e+16>>2];b=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break ug}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break ug}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;f=l?f:!f}if(!f){break fg}}m=k-4|0;j=K[k+4>>2]>>>23&4|(K[m>>2]>>>25&1|(c>>>18&16|(c>>>22&64|c>>>6&170)));l=C+(L[j+24336|0]<<2)|0;f=K[l>>2];g=K[f>>2];b=b-g|0;t=o+r|0;vg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=K[f+4>>2];if(b&32768){break vg}i=K[f+4>>2];g=b>>>0<g>>>0;K[l>>2]=K[f+(g?12:8)>>2];while(1){wg:{if(h){break wg}f=K[e+16>>2];l=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break wg}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break wg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break vg}i=K[f+4>>2];p=l;l=b>>>0<g>>>0;K[p>>2]=K[f+(l?8:12)>>2];while(1){xg:{if(h){break xg}f=K[e+16>>2];b=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break xg}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break xg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=L[j+24592|0];K[t>>2]=(l|0)==(g|0)?y:u;K[m>>2]=K[m>>2]|2048;K[k+4>>2]=K[k+4>>2]|512;c=(g^l)<<25|c|1024;i=K[e+108>>2]}j=C+(L[(c>>>9&495)+i|0]<<2)|0;f=K[j>>2];g=K[f>>2];b=b-g|0;yg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[f+4>>2];if(b&32768){break yg}i=K[f+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[f+(g?12:8)>>2];while(1){zg:{if(h){break zg}f=K[e+16>>2];l=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(m<<8)+d|0;break zg}if(m>>>0<=143){K[e+16>>2]=l;d=(m<<9)+d|0;h=7;break zg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break yg}i=K[f+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[f+(l?8:12)>>2];while(1){Ag:{if(h){break Ag}f=K[e+16>>2];b=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Ag}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Ag}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break xf}}t=k-4|0;Y=K[k+4>>2]>>>26&4|(K[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|c>>>9&170)));j=C+(L[Y+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0}p=o+v|0;Bg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Bg}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[(g?12:8)+i>>2];while(1){Cg:{if(h){break Cg}f=K[e+16>>2];l=f+1|0;i=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(i<<8)+d|0;break Cg}if(i>>>0<=143){K[e+16>>2]=l;d=(i<<9)+d|0;h=7;break Cg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Bg}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[(l?8:12)+i>>2];while(1){Dg:{if(h){break Dg}f=K[e+16>>2];b=f+1|0;i=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(i<<8)+d|0;break Dg}if(i>>>0<=143){K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Dg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[Y+24592|0];K[p>>2]=(l|0)==(g|0)?y:u;K[t>>2]=K[t>>2]|16384;K[k+4>>2]=K[k+4>>2]|4096;f=k+(K[e+124>>2]<<2)|0;K[f+4>>2]=K[f+4>>2]|4;K[f+12>>2]=K[f+12>>2]|1;g=g^l;K[f+8>>2]=K[f+8>>2]|g<<18|2;c=g<<28|c|8192}K[k>>2]=c&-1226833921}c=k+4|0;o=o+4|0;A=A+1|0;if((W|0)!=(A|0)){continue}break}c=k+12|0;o=o+v|0;s=s+4|0;g=K[e+128>>2];if(s>>>0<(g&-4)>>>0){continue}break}break vf}c=(g&-4)-1|0;s=(c&-4)+4|0;c=(l+(c<<1&-8)|0)+20|0}K[e+8>>2]=h;K[e+4>>2]=b;K[e>>2]=d;K[e+104>>2]=j;if(!W|g>>>0<=s>>>0){break je}while(1){h=0;if(K[e+128>>2]!=(s|0)){while(1){kc(e,c,(Q(h,W)<<2)+o|0,y,h,1);h=h+1|0;if(h>>>0<K[e+128>>2]-s>>>0){continue}break}}K[c>>2]=K[c>>2]&-1226833921;o=o+4|0;c=c+4|0;F=F+1|0;if((W|0)!=(F|0)){continue}break}break je}Eg:{if(g>>>0<4){break Eg}if(W){n=e+100|0;q=e+96|0;v=Q(W,12);r=W<<3;u=0-y|0;C=e+28|0;while(1){A=0;while(1){k=c;c=K[c>>2];Fg:{Gg:{Hg:{if(c){Ig:{if(c&2097168){break Ig}j=C+(L[K[e+108>>2]+(c&495)|0]<<2)|0;f=K[j>>2];g=K[f>>2];b=b-g|0;Jg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[f+4>>2];if(b&32768){break Jg}i=K[f+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[f+(g?12:8)>>2];while(1){Kg:{if(h){break Kg}f=K[e+16>>2];l=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Kg}if(m>>>0<=143){K[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Kg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break Jg}i=K[f+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[f+(l?8:12)>>2];while(1){Lg:{if(h){break Lg}f=K[e+16>>2];b=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Lg}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Lg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break Ig}p=k-4|0;f=K[k+4>>2]>>>17&4|(K[p>>2]>>>19&1|(c>>>14&16|(c>>>16&64|c&170)));j=C+(L[f+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Mg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Mg}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Ng:{if(h){break Ng}i=K[e+16>>2];l=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Ng}if(t>>>0<=143){K[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Ng}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Mg}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Og:{if(h){break Og}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Og}if(t>>>0<=143){K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Og}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[f+24592|0];K[o>>2]=(l|0)==(g|0)?y:u;K[p>>2]=K[p>>2]|32;K[k+4>>2]=K[k+4>>2]|8;f=k+(-2-K[e+124>>2]<<2)|0;K[f+4>>2]=K[f+4>>2]|32768;l=g^l;K[f>>2]=K[f>>2]|l<<31|65536;g=f-4|0;K[g>>2]=K[g>>2]|131072;c=l<<19|c|16}Pg:{if(c&16777344){break Pg}f=c>>>3|0;j=C+(L[K[e+108>>2]+(f&495)|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Qg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Qg}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Rg:{if(h){break Rg}i=K[e+16>>2];l=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break Rg}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break Rg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Qg}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Sg:{if(h){break Sg}i=K[e+16>>2];b=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Sg}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Sg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Pg}p=k-4|0;f=K[k+4>>2]>>>20&4|(K[p>>2]>>>22&1|(c>>>15&16|(c>>>19&64|f&170)));j=C+(L[f+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;z=(W<<2)+o|0;Tg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Tg}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Ug:{if(h){break Ug}i=K[e+16>>2];l=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(t<<8)+d|0;break Ug}if(t>>>0<=143){K[e+16>>2]=l;d=(t<<9)+d|0;h=7;break Ug}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Tg}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Vg:{if(h){break Vg}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(t<<8)+d|0;break Vg}if(t>>>0<=143){K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Vg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[f+24592|0];K[z>>2]=(l|0)==(g|0)?y:u;K[p>>2]=K[p>>2]|256;K[k+4>>2]=K[k+4>>2]|64;c=(g^l)<<22|c|128}Wg:{if(c&134218752){break Wg}f=c>>>6|0;j=C+(L[K[e+108>>2]+(f&495)|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;Xg:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Xg}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){Yg:{if(h){break Yg}i=K[e+16>>2];l=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break Yg}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break Yg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Xg}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){Zg:{if(h){break Zg}i=K[e+16>>2];b=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Zg}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Zg}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Wg}p=k-4|0;f=K[k+4>>2]>>>23&4|(K[p>>2]>>>25&1|(c>>>18&16|(c>>>22&64|f&170)));j=C+(L[f+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;z=o+r|0;_g:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break _g}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){$g:{if(h){break $g}i=K[e+16>>2];l=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(t<<8)+d|0;break $g}if(t>>>0<=143){K[e+16>>2]=l;d=(t<<9)+d|0;h=7;break $g}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break _g}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){ah:{if(h){break ah}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(t<<8)+d|0;break ah}if(t>>>0<=143){K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break ah}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[f+24592|0];K[z>>2]=(l|0)==(g|0)?y:u;K[p>>2]=K[p>>2]|2048;K[k+4>>2]=K[k+4>>2]|512;c=(g^l)<<25|c|1024}if(c&1073750016){break Gg}f=c>>>9|0;j=C+(L[K[e+108>>2]+(f&495)|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;bh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break bh}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[i+(g?12:8)>>2];while(1){ch:{if(h){break ch}i=K[e+16>>2];l=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break ch}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break ch}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break bh}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[i+(l?8:12)>>2];while(1){dh:{if(h){break dh}i=K[e+16>>2];b=i+1|0;p=L[i+1|0];if(L[i|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break dh}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break dh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}if(!l){break Gg}t=k-4|0;Y=K[k+4>>2]>>>26&4|(K[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|f&170)));j=C+(L[Y+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0;break Hg}l=K[q>>2];c=K[l>>2];b=b-c|0;eh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;g=K[l+4>>2];if(b&32768){break eh}j=K[l+4>>2];c=b>>>0<c>>>0;K[q>>2]=K[l+(c?12:8)>>2];while(1){fh:{if(h){break fh}l=K[e+16>>2];g=l+1|0;f=L[l+1|0];if(L[l|0]!=255){K[e+16>>2]=g;h=8;d=(f<<8)+d|0;break fh}if(f>>>0<=143){K[e+16>>2]=g;d=(f<<9)+d|0;h=7;break fh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}g=c?!j:j;break eh}j=K[l+4>>2];g=b>>>0<c>>>0;K[q>>2]=K[l+(g?8:12)>>2];while(1){gh:{if(h){break gh}l=K[e+16>>2];b=l+1|0;f=L[l+1|0];if(L[l|0]!=255){K[e+16>>2]=b;h=8;d=(f<<8)+d|0;break gh}if(f>>>0<=143){K[e+16>>2]=b;d=(f<<9)+d|0;h=7;break gh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;g=g?j:!j}if(!g){j=q;break Fg}g=K[n>>2];c=K[g>>2];b=b-c|0;hh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;l=K[g+4>>2];if(b&32768){break hh}f=K[g+4>>2];c=b>>>0<c>>>0;g=K[(c?12:8)+g>>2];K[n>>2]=g;while(1){ih:{if(h){break ih}j=K[e+16>>2];l=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=l;h=8;d=(i<<8)+d|0;break ih}if(i>>>0<=143){K[e+16>>2]=l;d=(i<<9)+d|0;h=7;break ih}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=c?!f:f;break hh}f=K[g+4>>2];l=b>>>0<c>>>0;g=K[(l?8:12)+g>>2];K[n>>2]=g;while(1){jh:{if(h){break jh}j=K[e+16>>2];b=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(i<<8)+d|0;break jh}if(i>>>0<=143){K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break jh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;l=l?f:!f}c=K[g>>2];b=b-c|0;kh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;j=K[g+4>>2];if(b&32768){break kh}f=K[g+4>>2];c=b>>>0<c>>>0;K[n>>2]=K[(c?12:8)+g>>2];while(1){lh:{if(h){break lh}j=K[e+16>>2];g=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=g;h=8;d=(i<<8)+d|0;break lh}if(i>>>0<=143){K[e+16>>2]=g;d=(i<<9)+d|0;h=7;break lh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!f:f;break kh}f=K[g+4>>2];j=g;g=b>>>0<c>>>0;K[n>>2]=K[j+(g?8:12)>>2];while(1){mh:{if(h){break mh}j=K[e+16>>2];b=j+1|0;i=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(i<<8)+d|0;break mh}if(i>>>0<=143){K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break mh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=g?f:!f}g=j;c=0;j=n;nh:{oh:{ph:{qh:{rh:{switch(g|l<<1){case 0:i=k-4|0;l=K[k+4>>2]>>>17&4|K[i>>2]>>>19&1;g=C+(L[l+24336|0]<<2)|0;j=K[g>>2];c=K[j>>2];b=b-c|0;sh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=K[j+4>>2];if(b&32768){break sh}f=K[j+4>>2];c=b>>>0<c>>>0;K[g>>2]=K[j+(c?12:8)>>2];while(1){th:{if(h){break th}j=K[e+16>>2];g=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=g;h=8;d=(m<<8)+d|0;break th}if(m>>>0<=143){K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break th}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break sh}f=K[j+4>>2];m=g;g=b>>>0<c>>>0;K[m>>2]=K[j+(g?8:12)>>2];while(1){uh:{if(h){break uh}j=K[e+16>>2];b=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break uh}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break uh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=L[l+24592|0];K[o>>2]=(g|0)==(c|0)?y:u;K[i>>2]=K[i>>2]|32;K[k+4>>2]=K[k+4>>2]|8;l=k+(-2-K[e+124>>2]<<2)|0;K[l+4>>2]=K[l+4>>2]|32768;g=c^g;K[l>>2]=K[l>>2]|g<<31|65536;c=l-4|0;K[c>>2]=K[c>>2]|131072;l=g<<19;i=K[e+108>>2];g=C+(L[i+2|0]<<2)|0;j=K[g>>2];c=K[j>>2];b=b-c|0;vh:{if(d>>>16>>>0>=c>>>0){d=d-(c<<16)|0;f=K[j+4>>2];if(b&32768){break vh}f=K[j+4>>2];c=b>>>0<c>>>0;K[g>>2]=K[j+(c?12:8)>>2];while(1){wh:{if(h){break wh}j=K[e+16>>2];g=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=g;h=8;d=(m<<8)+d|0;break wh}if(m>>>0<=143){K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break wh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=c?!f:f;break vh}f=K[j+4>>2];m=g;g=b>>>0<c>>>0;K[m>>2]=K[j+(g?8:12)>>2];while(1){xh:{if(h){break xh}j=K[e+16>>2];b=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break xh}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break xh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;f=g?f:!f}g=f;c=l|16;if(!g){break qh}break;case 1:break rh;case 2:break ph;case 3:break nh;default:break Gg}}m=k-4|0;j=K[k+4>>2]>>>20&4|(K[m>>2]>>>22&1|(c>>>15&16|(c>>>19&64|c>>>3&170)));l=C+(L[j+24336|0]<<2)|0;f=K[l>>2];g=K[f>>2];b=b-g|0;t=(W<<2)+o|0;yh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=K[f+4>>2];if(b&32768){break yh}i=K[f+4>>2];g=b>>>0<g>>>0;K[l>>2]=K[f+(g?12:8)>>2];while(1){zh:{if(h){break zh}f=K[e+16>>2];l=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break zh}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break zh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break yh}i=K[f+4>>2];p=l;l=b>>>0<g>>>0;K[p>>2]=K[f+(l?8:12)>>2];while(1){Ah:{if(h){break Ah}f=K[e+16>>2];b=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Ah}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Ah}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=L[j+24592|0];K[t>>2]=(l|0)==(g|0)?y:u;K[m>>2]=K[m>>2]|256;K[k+4>>2]=K[k+4>>2]|64;i=K[e+108>>2];c=(g^l)<<22|c|128}l=C+(L[(c>>>6&495)+i|0]<<2)|0;j=K[l>>2];g=K[j>>2];b=b-g|0;Bh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;f=K[j+4>>2];if(b&32768){break Bh}f=K[j+4>>2];g=b>>>0<g>>>0;K[l>>2]=K[j+(g?12:8)>>2];while(1){Ch:{if(h){break Ch}j=K[e+16>>2];l=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Ch}if(m>>>0<=143){K[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Ch}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}f=g?!f:f;break Bh}f=K[j+4>>2];m=l;l=b>>>0<g>>>0;K[m>>2]=K[j+(l?8:12)>>2];while(1){Dh:{if(h){break Dh}j=K[e+16>>2];b=j+1|0;m=L[j+1|0];if(L[j|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Dh}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Dh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;f=l?f:!f}if(!f){break oh}}m=k-4|0;j=K[k+4>>2]>>>23&4|(K[m>>2]>>>25&1|(c>>>18&16|(c>>>22&64|c>>>6&170)));l=C+(L[j+24336|0]<<2)|0;f=K[l>>2];g=K[f>>2];b=b-g|0;t=o+r|0;Eh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;i=K[f+4>>2];if(b&32768){break Eh}i=K[f+4>>2];g=b>>>0<g>>>0;K[l>>2]=K[f+(g?12:8)>>2];while(1){Fh:{if(h){break Fh}f=K[e+16>>2];l=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(p<<8)+d|0;break Fh}if(p>>>0<=143){K[e+16>>2]=l;d=(p<<9)+d|0;h=7;break Fh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}i=g?!i:i;break Eh}i=K[f+4>>2];p=l;l=b>>>0<g>>>0;K[p>>2]=K[f+(l?8:12)>>2];while(1){Gh:{if(h){break Gh}f=K[e+16>>2];b=f+1|0;p=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(p<<8)+d|0;break Gh}if(p>>>0<=143){K[e+16>>2]=b;d=(p<<9)+d|0;h=7;break Gh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;i=l?i:!i}l=i;g=L[j+24592|0];K[t>>2]=(l|0)==(g|0)?y:u;K[m>>2]=K[m>>2]|2048;K[k+4>>2]=K[k+4>>2]|512;c=(g^l)<<25|c|1024;i=K[e+108>>2]}j=C+(L[(c>>>9&495)+i|0]<<2)|0;f=K[j>>2];g=K[f>>2];b=b-g|0;Hh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[f+4>>2];if(b&32768){break Hh}i=K[f+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[f+(g?12:8)>>2];while(1){Ih:{if(h){break Ih}f=K[e+16>>2];l=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(m<<8)+d|0;break Ih}if(m>>>0<=143){K[e+16>>2]=l;d=(m<<9)+d|0;h=7;break Ih}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!i:i;break Hh}i=K[f+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[f+(l?8:12)>>2];while(1){Jh:{if(h){break Jh}f=K[e+16>>2];b=f+1|0;m=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(m<<8)+d|0;break Jh}if(m>>>0<=143){K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break Jh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?i:!i}if(!l){break Gg}}t=k-4|0;Y=K[k+4>>2]>>>26&4|(K[t>>2]>>>28&1|(c>>>21&16|(c>>>25&64|c>>>9&170)));j=C+(L[Y+24336|0]<<2)|0;i=K[j>>2];g=K[i>>2];b=b-g|0}p=o+v|0;Kh:{if(d>>>16>>>0>=g>>>0){d=d-(g<<16)|0;l=K[i+4>>2];if(b&32768){break Kh}m=K[i+4>>2];g=b>>>0<g>>>0;K[j>>2]=K[(g?12:8)+i>>2];while(1){Lh:{if(h){break Lh}f=K[e+16>>2];l=f+1|0;i=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=l;h=8;d=(i<<8)+d|0;break Lh}if(i>>>0<=143){K[e+16>>2]=l;d=(i<<9)+d|0;h=7;break Lh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}l=g?!m:m;break Kh}m=K[i+4>>2];l=b>>>0<g>>>0;K[j>>2]=K[(l?8:12)+i>>2];while(1){Mh:{if(h){break Mh}f=K[e+16>>2];b=f+1|0;i=L[f+1|0];if(L[f|0]!=255){K[e+16>>2]=b;h=8;d=(i<<8)+d|0;break Mh}if(i>>>0<=143){K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Mh}K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;l=l?m:!m}g=L[Y+24592|0];K[p>>2]=(l|0)==(g|0)?y:u;K[t>>2]=K[t>>2]|16384;K[k+4>>2]=K[k+4>>2]|4096;f=k+(K[e+124>>2]<<2)|0;K[f+4>>2]=K[f+4>>2]|4;K[f+12>>2]=K[f+12>>2]|1;g=g^l;K[f+8>>2]=K[f+8>>2]|g<<18|2;c=g<<28|c|8192}K[k>>2]=c&-1226833921}c=k+4|0;o=o+4|0;A=A+1|0;if((W|0)!=(A|0)){continue}break}c=k+12|0;o=o+v|0;s=s+4|0;g=K[e+128>>2];if(s>>>0<(g&-4)>>>0){continue}break}break Eg}c=(g&-4)-1|0;s=(c&-4)+4|0;c=(l+(c<<1&-8)|0)+20|0}K[e+8>>2]=h;K[e+4>>2]=b;K[e>>2]=d;K[e+104>>2]=j;if(!W|g>>>0<=s>>>0){break je}while(1){h=0;if(K[e+128>>2]!=(s|0)){while(1){kc(e,c,(Q(h,W)<<2)+o|0,y,h,0);h=h+1|0;if(h>>>0<K[e+128>>2]-s>>>0){continue}break}}K[c>>2]=K[c>>2]&-1226833921;o=o+4|0;c=c+4|0;F=F+1|0;if((W|0)!=(F|0)){continue}break}break je}while(1){p=0;while(1){k=c;f=g;g=K[g>>2];Nh:{Oh:{Ph:{if(!g){j=K[l>>2];g=K[j>>2];b=b-g|0;Qh:{if(d>>>16>>>0<g>>>0){n=K[j+4>>2];c=b>>>0<g>>>0;K[l>>2]=K[j+(c?8:12)>>2];while(1){Rh:{if(h){break Rh}j=K[e+16>>2];b=j+1|0;o=L[j+1|0];if(L[j|0]==255){if(o>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Rh}K[e+16>>2]=b;d=(o<<9)+d|0;h=7;break Rh}K[e+16>>2]=b;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?n:!n;break Qh}d=d-(g<<16)|0;if(!(b&32768)){n=K[j+4>>2];c=b>>>0<g>>>0;K[l>>2]=K[j+(c?12:8)>>2];while(1){Sh:{if(h){break Sh}j=K[e+16>>2];g=j+1|0;o=L[j+1|0];if(L[j|0]==255){if(o>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Sh}K[e+16>>2]=g;d=(o<<9)+d|0;h=7;break Sh}K[e+16>>2]=g;h=8;d=(o<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!n:n;break Qh}c=K[j+4>>2]}if(!c){j=l;break Nh}c=K[q>>2];g=K[c>>2];b=b-g|0;Th:{if(d>>>16>>>0<g>>>0){o=K[c+4>>2];j=b>>>0<g>>>0;c=K[(j?8:12)+c>>2];K[q>>2]=c;while(1){Uh:{if(h){break Uh}n=K[e+16>>2];b=n+1|0;i=L[n+1|0];if(L[n|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Uh}K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Uh}K[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;n=j?o:!o;break Th}d=d-(g<<16)|0;if(!(b&32768)){o=K[c+4>>2];g=b>>>0<g>>>0;c=K[(g?12:8)+c>>2];K[q>>2]=c;while(1){Vh:{if(h){break Vh}n=K[e+16>>2];j=n+1|0;i=L[n+1|0];if(L[n|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Vh}K[e+16>>2]=j;d=(i<<9)+d|0;h=7;break Vh}K[e+16>>2]=j;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=g?!o:o;break Th}n=K[c+4>>2]}g=K[c>>2];b=b-g|0;Wh:{if(d>>>16>>>0<g>>>0){o=K[c+4>>2];j=c;c=b>>>0<g>>>0;K[q>>2]=K[j+(c?8:12)>>2];while(1){Xh:{if(h){break Xh}j=K[e+16>>2];b=j+1|0;i=L[j+1|0];if(L[j|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Xh}K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Xh}K[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?o:!o;break Wh}d=d-(g<<16)|0;if(!(b&32768)){o=K[c+4>>2];j=c;c=b>>>0<g>>>0;K[q>>2]=K[j+(c?12:8)>>2];while(1){Yh:{if(h){break Yh}j=K[e+16>>2];g=j+1|0;i=L[j+1|0];if(L[j|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Yh}K[e+16>>2]=g;d=(i<<9)+d|0;h=7;break Yh}K[e+16>>2]=g;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!o:o;break Wh}c=K[c+4>>2]}g=0;j=q;Zh:{_h:{$h:{ai:{bi:{switch(c|n<<1){case 0:i=f-4|0;j=K[f+4>>2]>>>17&4|K[i>>2]>>>19&1;c=u+(L[j+24336|0]<<2)|0;n=K[c>>2];g=K[n>>2];b=b-g|0;ci:{if(d>>>16>>>0<g>>>0){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?8:12)>>2];while(1){di:{if(h){break di}n=K[e+16>>2];b=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break di}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break di}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;g=c?o:!o;break ci}d=d-(g<<16)|0;if(!(b&32768)){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?12:8)>>2];while(1){ei:{if(h){break ei}n=K[e+16>>2];g=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ei}K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break ei}K[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}g=c?!o:o;break ci}g=K[n+4>>2]}c=L[j+24592|0];K[k>>2]=(g|0)==(c|0)?A:v;K[i>>2]=K[i>>2]|32;K[f+4>>2]=K[f+4>>2]|8;j=(c^g)<<19;i=K[e+108>>2];c=u+(L[i+2|0]<<2)|0;n=K[c>>2];g=K[n>>2];b=b-g|0;fi:{if(d>>>16>>>0<g>>>0){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?8:12)>>2];while(1){gi:{if(h){break gi}n=K[e+16>>2];b=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break gi}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break gi}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;g=g<<1;if(g>>>0<32768){continue}break}b=g;c=c?o:!o;break fi}d=d-(g<<16)|0;if(!(b&32768)){o=K[n+4>>2];y=c;c=b>>>0<g>>>0;K[y>>2]=K[n+(c?12:8)>>2];while(1){hi:{if(h){break hi}n=K[e+16>>2];g=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break hi}K[e+16>>2]=g;d=(m<<9)+d|0;h=7;break hi}K[e+16>>2]=g;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!o:o;break fi}c=K[n+4>>2]}g=j|16;if(!c){break ai}break;case 1:break bi;case 2:break $h;case 3:break Zh;default:break Oh}}m=f-4|0;n=K[f+4>>2]>>>20&4|(K[m>>2]>>>22&1|(g>>>15&16|(g>>>19&64|g>>>3&170)));j=u+(L[n+24336|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;ii:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];y=j;j=b>>>0<c>>>0;K[y>>2]=K[o+(j?8:12)>>2];while(1){ji:{if(h){break ji}o=K[e+16>>2];b=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ji}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break ji}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break ii}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){ki:{if(h){break ki}o=K[e+16>>2];j=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ki}K[e+16>>2]=j;d=(r<<9)+d|0;h=7;break ki}K[e+16>>2]=j;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break ii}j=K[o+4>>2]}c=L[n+24592|0];K[k+256>>2]=(j|0)==(c|0)?A:v;K[m>>2]=K[m>>2]|256;K[f+4>>2]=K[f+4>>2]|64;i=K[e+108>>2];g=(c^j)<<22|g|128}j=u+(L[(g>>>6&495)+i|0]<<2)|0;n=K[j>>2];c=K[n>>2];b=b-c|0;li:{if(d>>>16>>>0<c>>>0){o=K[n+4>>2];y=j;j=b>>>0<c>>>0;K[y>>2]=K[n+(j?8:12)>>2];while(1){mi:{if(h){break mi}n=K[e+16>>2];b=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break mi}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break mi}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=j?o:!o;break li}d=d-(c<<16)|0;if(!(b&32768)){o=K[n+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[n+(c?12:8)>>2];while(1){ni:{if(h){break ni}n=K[e+16>>2];j=n+1|0;m=L[n+1|0];if(L[n|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ni}K[e+16>>2]=j;d=(m<<9)+d|0;h=7;break ni}K[e+16>>2]=j;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!o:o;break li}c=K[n+4>>2]}if(!c){break _h}}m=f-4|0;n=K[f+4>>2]>>>23&4|(K[m>>2]>>>25&1|(g>>>18&16|(g>>>22&64|g>>>6&170)));j=u+(L[n+24336|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;oi:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];y=j;j=b>>>0<c>>>0;K[y>>2]=K[o+(j?8:12)>>2];while(1){pi:{if(h){break pi}o=K[e+16>>2];b=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break pi}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break pi}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;j=j?i:!i;break oi}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){qi:{if(h){break qi}o=K[e+16>>2];j=o+1|0;r=L[o+1|0];if(L[o|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break qi}K[e+16>>2]=j;d=(r<<9)+d|0;h=7;break qi}K[e+16>>2]=j;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}j=c?!i:i;break oi}j=K[o+4>>2]}c=L[n+24592|0];K[k+512>>2]=(j|0)==(c|0)?A:v;K[m>>2]=K[m>>2]|2048;K[f+4>>2]=K[f+4>>2]|512;g=(c^j)<<25|g|1024;i=K[e+108>>2]}j=u+(L[(g>>>9&495)+i|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;ri:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[o+(n?8:12)>>2];while(1){si:{if(h){break si}o=K[e+16>>2];b=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break si}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break si}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break ri}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){ti:{if(h){break ti}o=K[e+16>>2];n=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break ti}K[e+16>>2]=n;d=(m<<9)+d|0;h=7;break ti}K[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break ri}c=K[o+4>>2]}if(!c){break Oh}}F=f-4|0;t=K[f+4>>2]>>>26&4|(K[F>>2]>>>28&1|(g>>>21&16|(g>>>25&64|g>>>9&170)));j=u+(L[t+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;break Ph}ui:{if(g&2097168){break ui}j=u+(L[K[e+108>>2]+(g&495)|0]<<2)|0;o=K[j>>2];c=K[o>>2];b=b-c|0;vi:{if(d>>>16>>>0<c>>>0){i=K[o+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[o+(n?8:12)>>2];while(1){wi:{if(h){break wi}o=K[e+16>>2];b=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break wi}K[e+16>>2]=b;d=(m<<9)+d|0;h=7;break wi}K[e+16>>2]=b;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?i:!i;break vi}d=d-(c<<16)|0;if(!(b&32768)){i=K[o+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[o+(c?12:8)>>2];while(1){xi:{if(h){break xi}o=K[e+16>>2];n=o+1|0;m=L[o+1|0];if(L[o|0]==255){if(m>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break xi}K[e+16>>2]=n;d=(m<<9)+d|0;h=7;break xi}K[e+16>>2]=n;h=8;d=(m<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!i:i;break vi}c=K[o+4>>2]}if(!c){break ui}r=f-4|0;o=K[f+4>>2]>>>17&4|(K[r>>2]>>>19&1|(g>>>14&16|(g>>>16&64|g&170)));j=u+(L[o+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;yi:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){zi:{if(h){break zi}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break zi}K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break zi}K[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break yi}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){Ai:{if(h){break Ai}i=K[e+16>>2];n=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ai}K[e+16>>2]=n;d=(t<<9)+d|0;h=7;break Ai}K[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break yi}n=K[i+4>>2]}c=L[o+24592|0];K[k>>2]=(n|0)==(c|0)?A:v;K[r>>2]=K[r>>2]|32;K[f+4>>2]=K[f+4>>2]|8;g=(c^n)<<19|g|16}Bi:{if(g&16777344){break Bi}o=g>>>3|0;j=u+(L[K[e+108>>2]+(o&495)|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;Ci:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){Di:{if(h){break Di}i=K[e+16>>2];b=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Di}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break Di}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break Ci}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){Ei:{if(h){break Ei}i=K[e+16>>2];n=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ei}K[e+16>>2]=n;d=(r<<9)+d|0;h=7;break Ei}K[e+16>>2]=n;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break Ci}c=K[i+4>>2]}if(!c){break Bi}r=f-4|0;o=K[f+4>>2]>>>20&4|(K[r>>2]>>>22&1|(g>>>15&16|(g>>>19&64|o&170)));j=u+(L[o+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;Fi:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){Gi:{if(h){break Gi}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Gi}K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Gi}K[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break Fi}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){Hi:{if(h){break Hi}i=K[e+16>>2];n=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Hi}K[e+16>>2]=n;d=(t<<9)+d|0;h=7;break Hi}K[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break Fi}n=K[i+4>>2]}c=L[o+24592|0];K[k+256>>2]=(n|0)==(c|0)?A:v;K[r>>2]=K[r>>2]|256;K[f+4>>2]=K[f+4>>2]|64;g=(c^n)<<22|g|128}Ii:{if(g&134218752){break Ii}o=g>>>6|0;j=u+(L[K[e+108>>2]+(o&495)|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;Ji:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){Ki:{if(h){break Ki}i=K[e+16>>2];b=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ki}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break Ki}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break Ji}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){Li:{if(h){break Li}i=K[e+16>>2];n=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Li}K[e+16>>2]=n;d=(r<<9)+d|0;h=7;break Li}K[e+16>>2]=n;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break Ji}c=K[i+4>>2]}if(!c){break Ii}r=f-4|0;o=K[f+4>>2]>>>23&4|(K[r>>2]>>>25&1|(g>>>18&16|(g>>>22&64|o&170)));j=u+(L[o+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;Mi:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){Ni:{if(h){break Ni}i=K[e+16>>2];b=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ni}K[e+16>>2]=b;d=(t<<9)+d|0;h=7;break Ni}K[e+16>>2]=b;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break Mi}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){Oi:{if(h){break Oi}i=K[e+16>>2];n=i+1|0;t=L[i+1|0];if(L[i|0]==255){if(t>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Oi}K[e+16>>2]=n;d=(t<<9)+d|0;h=7;break Oi}K[e+16>>2]=n;h=8;d=(t<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break Mi}n=K[i+4>>2]}c=L[o+24592|0];K[k+512>>2]=(n|0)==(c|0)?A:v;K[r>>2]=K[r>>2]|2048;K[f+4>>2]=K[f+4>>2]|512;g=(c^n)<<25|g|1024}if(g&1073750016){break Oh}o=g>>>9|0;j=u+(L[K[e+108>>2]+(o&495)|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0;Pi:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[i+(n?8:12)>>2];while(1){Qi:{if(h){break Qi}i=K[e+16>>2];b=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Qi}K[e+16>>2]=b;d=(r<<9)+d|0;h=7;break Qi}K[e+16>>2]=b;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;c=n?m:!m;break Pi}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[i+(c?12:8)>>2];while(1){Ri:{if(h){break Ri}i=K[e+16>>2];n=i+1|0;r=L[i+1|0];if(L[i|0]==255){if(r>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ri}K[e+16>>2]=n;d=(r<<9)+d|0;h=7;break Ri}K[e+16>>2]=n;h=8;d=(r<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}c=c?!m:m;break Pi}c=K[i+4>>2]}if(!c){break Oh}F=f-4|0;t=K[f+4>>2]>>>26&4|(K[F>>2]>>>28&1|(g>>>21&16|(g>>>25&64|o&170)));j=u+(L[t+24336|0]<<2)|0;i=K[j>>2];c=K[i>>2];b=b-c|0}Si:{if(d>>>16>>>0<c>>>0){m=K[i+4>>2];n=b>>>0<c>>>0;K[j>>2]=K[(n?8:12)+i>>2];while(1){Ti:{if(h){break Ti}o=K[e+16>>2];b=o+1|0;i=L[o+1|0];if(L[o|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ti}K[e+16>>2]=b;d=(i<<9)+d|0;h=7;break Ti}K[e+16>>2]=b;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;c=c<<1;if(c>>>0<32768){continue}break}b=c;n=n?m:!m;break Si}d=d-(c<<16)|0;if(!(b&32768)){m=K[i+4>>2];c=b>>>0<c>>>0;K[j>>2]=K[(c?12:8)+i>>2];while(1){Ui:{if(h){break Ui}o=K[e+16>>2];n=o+1|0;i=L[o+1|0];if(L[o|0]==255){if(i>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;d=d+65280|0;h=8;break Ui}K[e+16>>2]=n;d=(i<<9)+d|0;h=7;break Ui}K[e+16>>2]=n;h=8;d=(i<<8)+d|0}h=h-1|0;d=d<<1;b=b<<1;if(b>>>0<32768){continue}break}n=c?!m:m;break Si}n=K[i+4>>2]}c=L[t+24592|0];K[k+768>>2]=(n|0)==(c|0)?A:v;K[F>>2]=K[F>>2]|16384;K[f+4>>2]=K[f+4>>2]|4096;K[f+260>>2]=K[f+260>>2]|4;K[f+268>>2]=K[f+268>>2]|1;c=c^n;K[f+264>>2]=K[f+264>>2]|c<<18|2;g=c<<28|g|8192}K[f>>2]=g&-1226833921}g=f+4|0;c=k+4|0;p=p+1|0;if((p|0)!=64){continue}break}g=f+12|0;c=k+772|0;n=s>>>0<60;s=s+4|0;if(n){continue}break}}K[e+8>>2]=h;K[e+4>>2]=b;K[e>>2]=d;K[e+104>>2]=j}Vi:{if(!(Z&32)){break Vi}K[e+104>>2]=e+100;g=K[e+100>>2];b=K[g>>2];d=K[e+4>>2]-b|0;K[e+4>>2]=d;h=K[e>>2];Wi:{if(h>>>16>>>0<b>>>0){K[e+4>>2]=b;g=K[(b>>>0>d>>>0?8:12)+g>>2];K[e+100>>2]=g;d=K[e+8>>2];while(1){Xi:{if(d){break Xi}l=K[e+16>>2];c=l+1|0;j=L[l+1|0];if(L[l|0]==255){if(j>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;d=8;break Xi}K[e+16>>2]=c;h=(j<<9)+h|0;d=7;break Xi}K[e+16>>2]=c;d=8;h=(j<<8)+h|0}d=d-1|0;K[e+8>>2]=d;h=h<<1;K[e>>2]=h;b=b<<1;K[e+4>>2]=b;if(b>>>0<32768){continue}break}d=b;break Wi}h=h-(b<<16)|0;K[e>>2]=h;if(d&32768){break Wi}g=K[(b>>>0>d>>>0?12:8)+g>>2];K[e+100>>2]=g;b=K[e+8>>2];while(1){Yi:{if(b){break Yi}c=K[e+16>>2];b=c+1|0;l=L[c+1|0];if(L[c|0]==255){if(l>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;b=8;break Yi}K[e+16>>2]=b;h=(l<<9)+h|0;b=7;break Yi}K[e+16>>2]=b;b=8;h=(l<<8)+h|0}b=b-1|0;K[e+8>>2]=b;h=h<<1;K[e>>2]=h;d=d<<1;K[e+4>>2]=d;if(d>>>0<32768){continue}break}}b=K[g>>2];d=d-b|0;K[e+4>>2]=d;Zi:{if(h>>>16>>>0<b>>>0){K[e+4>>2]=b;g=K[(b>>>0>d>>>0?8:12)+g>>2];K[e+100>>2]=g;d=K[e+8>>2];while(1){_i:{if(d){break _i}l=K[e+16>>2];c=l+1|0;j=L[l+1|0];if(L[l|0]==255){if(j>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;d=8;break _i}K[e+16>>2]=c;h=(j<<9)+h|0;d=7;break _i}K[e+16>>2]=c;d=8;h=(j<<8)+h|0}d=d-1|0;K[e+8>>2]=d;h=h<<1;K[e>>2]=h;b=b<<1;K[e+4>>2]=b;if(b>>>0<32768){continue}break}d=b;break Zi}h=h-(b<<16)|0;K[e>>2]=h;if(d&32768){break Zi}g=K[(b>>>0>d>>>0?12:8)+g>>2];K[e+100>>2]=g;b=K[e+8>>2];while(1){$i:{if(b){break $i}c=K[e+16>>2];b=c+1|0;l=L[c+1|0];if(L[c|0]==255){if(l>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;b=8;break $i}K[e+16>>2]=b;h=(l<<9)+h|0;b=7;break $i}K[e+16>>2]=b;b=8;h=(l<<8)+h|0}b=b-1|0;K[e+8>>2]=b;h=h<<1;K[e>>2]=h;d=d<<1;K[e+4>>2]=d;if(d>>>0<32768){continue}break}}b=K[g>>2];d=d-b|0;K[e+4>>2]=d;aj:{if(h>>>16>>>0<b>>>0){K[e+4>>2]=b;g=K[(b>>>0>d>>>0?8:12)+g>>2];K[e+100>>2]=g;d=K[e+8>>2];while(1){bj:{if(d){break bj}l=K[e+16>>2];c=l+1|0;j=L[l+1|0];if(L[l|0]==255){if(j>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;d=8;break bj}K[e+16>>2]=c;h=(j<<9)+h|0;d=7;break bj}K[e+16>>2]=c;d=8;h=(j<<8)+h|0}d=d-1|0;K[e+8>>2]=d;h=h<<1;K[e>>2]=h;b=b<<1;K[e+4>>2]=b;if(b>>>0<32768){continue}break}d=b;break aj}h=h-(b<<16)|0;K[e>>2]=h;if(d&32768){break aj}g=K[(b>>>0>d>>>0?12:8)+g>>2];K[e+100>>2]=g;b=K[e+8>>2];while(1){cj:{if(b){break cj}c=K[e+16>>2];b=c+1|0;l=L[c+1|0];if(L[c|0]==255){if(l>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;b=8;break cj}K[e+16>>2]=b;h=(l<<9)+h|0;b=7;break cj}K[e+16>>2]=b;b=8;h=(l<<8)+h|0}b=b-1|0;K[e+8>>2]=b;h=h<<1;K[e>>2]=h;d=d<<1;K[e+4>>2]=d;if(d>>>0<32768){continue}break}}b=K[g>>2];d=d-b|0;K[e+4>>2]=d;if(h>>>16>>>0<b>>>0){K[e+4>>2]=b;K[e+100>>2]=K[(b>>>0>d>>>0?8:12)+g>>2];d=K[e+8>>2];while(1){dj:{if(d){break dj}g=K[e+16>>2];c=g+1|0;l=L[g+1|0];if(L[g|0]==255){if(l>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;h=h+65280|0;d=8;break dj}K[e+16>>2]=c;h=(l<<9)+h|0;d=7;break dj}K[e+16>>2]=c;d=8;h=(l<<8)+h|0}d=d-1|0;K[e+8>>2]=d;h=h<<1;K[e>>2]=h;b=b<<1;K[e+4>>2]=b;if(b>>>0<32768){continue}break}break Vi}c=h-(b<<16)|0;K[e>>2]=c;if(d&32768){break Vi}K[e+100>>2]=K[(b>>>0>d>>>0?12:8)+g>>2];h=K[e+8>>2];while(1){ej:{if(h){break ej}g=K[e+16>>2];b=g+1|0;l=L[g+1|0];if(L[g|0]==255){if(l>>>0>=144){K[e+12>>2]=K[e+12>>2]+1;c=c+65280|0;h=8;break ej}K[e+16>>2]=b;c=(l<<9)+c|0;h=7;break ej}K[e+16>>2]=b;h=8;c=(l<<8)+c|0}h=h-1|0;K[e+8>>2]=h;c=c<<1;K[e>>2]=c;d=d<<1;K[e+4>>2]=d;if(d>>>0<32768){continue}break}}}if(!X){break Za}mc(e);bb(e,18,46);bb(e,17,3);bb(e,0,4)}b=ma+1|0;c=(b|0)==3;ma=c?0:b;x=x-c|0;ea=ea+1|0;if(ea>>>0>=N[na+8>>2]){break Ya}if((x|0)>0){continue}break}}G=H+G|0;c=K[e+24>>2];b=M[e+112>>1];I[c|0]=b;I[c+1|0]=b>>>8;U=U+1|0;if(U>>>0<N[D+44>>2]){continue}break}}fj:{if(!_){break fj}gj:{c=K[e+24>>2];g=K[e+16>>2];if(c>>>0>g+2>>>0){if(!ba){break gj}g=K[e+16>>2];c=K[e+24>>2];b=K[e+20>>2];K[aa+56>>2]=c-b;K[aa+52>>2]=g-b;K[aa+48>>2]=(c-g|0)-2;Fa(S,2,15198,aa+48|0);break fj}b=K[e+12>>2];if(b>>>0<3){break fj}if(ba){K[aa+80>>2]=K[e+12>>2];Fa(S,2,7070,aa+80|0);break fj}K[aa+64>>2]=b;Fa(S,2,7070,aa- -64|0);break fj}b=K[e+20>>2];K[aa+40>>2]=c-b;K[aa+36>>2]=g-b;K[aa+32>>2]=(c-g|0)-2;Fa(S,2,15198,aa+32|0)}if(!K[D+60>>2]){break i}K[e+116>>2]=ia}l=K[sa+4>>2];g=K[D+12>>2];m=K[D+8>>2]-K[sa>>2]|0;c=K[sa+16>>2];if(c&1){b=K[pa+28>>2]+Q(ta,152)|0;m=(K[b-144>>2]+m|0)-K[b-152>>2]|0}j=g-l|0;if(c&2){b=K[pa+28>>2]+Q(ta,152)|0;j=(K[b-140>>2]+j|0)-K[b-148>>2]|0}k=K[D+60>>2];i=k;i=i?i:K[e+116>>2];X=K[e+128>>2];H=K[e+124>>2];n=K[qa+808>>2];hj:{if(!n){break hj}b=!X|!H;if((n|0)<=30){if(b){break hj}h=0;while(1){l=(Q(h,H)<<2)+i|0;b=0;while(1){g=l+(b<<2)|0;q=K[g>>2];c=q>>31;c=(c^q)-c|0;if(c>>>n|0){c=c>>>K[qa+808>>2]|0;K[g>>2]=(q|0)<0?0-c|0:c}b=b+1|0;if((H|0)!=(b|0)){continue}break}h=h+1|0;if((X|0)!=(h|0)){continue}break}break hj}if(b){break hj}b=Q(H,X)<<2;if(!b){break hj}B(i,0,b)}if(k){j=Q(H,X);if(K[qa+20>>2]==1){if(!j){break a}b=0;if((j|0)!=1){c=j&-2;g=0;while(1){l=(b<<2)+i|0;K[l>>2]=K[l>>2]/2;K[l+4>>2]=K[l+4>>2]/2;b=b+2|0;g=g+2|0;if((c|0)!=(g|0)){continue}break}}if(!(j&1)){break a}b=(b<<2)+i|0;K[b>>2]=K[b>>2]/2;break a}if(!j){break a}ha=R(O[sa+32>>2]*R(.5));if(j>>>0>=4){c=j&-4;b=0;while(1){O[i>>2]=ha*R(K[i>>2]);O[i+4>>2]=ha*R(K[i+4>>2]);O[i+8>>2]=ha*R(K[i+8>>2]);O[i+12>>2]=ha*R(K[i+12>>2]);i=i+16|0;b=b+4|0;if((c|0)!=(b|0)){continue}break}}c=j&3;if(!c){break a}b=0;while(1){O[i>>2]=ha*R(K[i>>2]);i=i+4|0;b=b+1|0;if((c|0)!=(b|0)){continue}break}break a}s=wa-ua|0;if(K[qa+20>>2]==1){if(!X){break a}f=(K[pa+36>>2]+(Q(j,s)<<2)|0)+(m<<2)|0;d=H&-4;j=0;while(1){b=0;if(d){k=f+(Q(j,s)<<2)|0;n=(Q(j,H)<<2)+i|0;while(1){q=b<<2;o=q+n|0;l=K[o+4>>2];g=K[o+8>>2];c=K[o+12>>2];q=k+q|0;K[q>>2]=K[o>>2]/2;K[q+12>>2]=(c|0)/2;K[q+8>>2]=(g|0)/2;K[q+4>>2]=(l|0)/2;b=b+4|0;if(d>>>0>b>>>0){continue}break}}ij:{if(b>>>0>=H>>>0){break ij}c=b+1|0;l=f+(Q(j,s)<<2)|0;g=(Q(j,H)<<2)+i|0;if(H-b&1){b=b<<2;K[b+l>>2]=K[b+g>>2]/2;b=c}if((c|0)==(H|0)){break ij}while(1){c=b<<2;K[c+l>>2]=K[c+g>>2]/2;c=c+4|0;K[c+l>>2]=K[c+g>>2]/2;b=b+2|0;if((H|0)!=(b|0)){continue}break}}j=j+1|0;if((X|0)!=(j|0)){continue}break}break a}if(!X|!H){break a}ha=R(O[sa+32>>2]*R(.5));j=(K[pa+36>>2]+(Q(j,s)<<2)|0)+(m<<2)|0;g=H&-4;l=H&3;f=0;c=H-1>>>0<3;while(1){b=j;e=0;if(!c){while(1){O[b>>2]=ha*R(K[i>>2]);O[b+4>>2]=ha*R(K[i+4>>2]);O[b+8>>2]=ha*R(K[i+8>>2]);O[b+12>>2]=ha*R(K[i+12>>2]);b=b+16|0;i=i+16|0;e=e+4|0;if((g|0)!=(e|0)){continue}break}}e=0;if(l){while(1){O[b>>2]=ha*R(K[i>>2]);b=b+4|0;i=i+4|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}j=(s<<2)+j|0;f=f+1|0;if((X|0)!=(f|0)){continue}break}break a}K[aa>>2]=x;Fa(S,2,8679,aa)}K[K[d>>2]>>2]=0}Ga(a);ra=aa+96|0}
function jb(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,C=0,D=0,F=0,G=0,H=0,M=0,P=0,T=0,U=0,V=0,Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=R(0),ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,sa=0,ta=0,va=0,wa=0,xa=0,ya=R(0),za=0,Aa=0,Ba=0,Ca=0,Da=0,Ea=0,Ka=0,Oa=0,Pa=0,Qa=0,Ra=0,Sa=0,Ta=0,Wa=0,Ya=R(0),$a=0,ab=0,bb=0,cb=0,eb=0,fb=0,gb=0,hb=0,ib=0,jb=0,mb=0,ob=0,pb=0,qb=0,tb=0,ub=0,vb=0,wb=0,xb=0,yb=0,zb=0,Ab=0,Bb=0,Cb=0,Db=0,Eb=0,Ib=0,Jb=0,Kb=0,Lb=0,Mb=0,Nb=0,Ob=0,Pb=0,Qb=0,Rb=0,Sb=0,Tb=0,Ub=0,Vb=0,Wb=0,Xb=0,Yb=0,Zb=0,_b=0,$b=0;za=ra-16|0;ra=za;a:{if(!(L[a+8|0]&128)|K[a+228>>2]!=(b|0)){break a}xa=K[a+180>>2]+Q(b,5644)|0;ba=K[xa+5596>>2];if(!ba){nb(xa);break a}o=K[a+100>>2];if(!o){o=K[a+96>>2]}h=K[o>>2];m=K[o+4>>2];q=K[o+8>>2];n=K[o+12>>2];g=K[a+60>>2];l=K[a+64>>2];i=K[xa+5600>>2];sa=ra-16|0;ra=sa;G=K[a+232>>2];K[G+36>>2]=b;o=K[K[G+28>>2]+76>>2];K[G+64>>2]=1;K[G+60>>2]=n;K[G+56>>2]=q;K[G+52>>2]=m;K[G+48>>2]=h;K[G+32>>2]=o+Q(b,5644);Ga(K[G+68>>2]);q=0;K[G+68>>2]=0;b:{if(g){q=Ia(4,K[K[G+24>>2]+16>>2]);if(!q){break b}if(g>>>0>=4){m=g&-4;b=0;while(1){h=l+(Y<<2)|0;K[(K[h>>2]<<2)+q>>2]=1;K[(K[h+4>>2]<<2)+q>>2]=1;K[(K[h+8>>2]<<2)+q>>2]=1;K[(K[h+12>>2]<<2)+q>>2]=1;Y=Y+4|0;b=b+4|0;if((m|0)!=(b|0)){continue}break}}b=g&3;if(b){while(1){K[(K[l+(Y<<2)>>2]<<2)+q>>2]=1;Y=Y+1|0;t=t+1|0;if((b|0)!=(t|0)){continue}break}}K[G+68>>2]=q}c:{H=K[G+24>>2];x=K[H+16>>2];d:{if(!x){break d}Y=0;e:{while(1){f:{if(K[(Y<<2)+q>>2]?0:q){break f}h=K[H+24>>2]+Q(Y,52)|0;b=K[h+4>>2];m=b-1|0;l=K[G+60>>2];n=m+l|0;g=0-!b|0;n=Ne(n,l>>>0>n>>>0?g+1|0:g,b,0);h=K[h>>2];t=h-1|0;o=K[G+56>>2];k=t+o|0;l=0-!h|0;o=Ne(k,k>>>0<o>>>0?l+1|0:l,h,0);s=m;m=K[G+52>>2];k=s+m|0;m=Ne(k,k>>>0<m>>>0?g+1|0:g,b,0);b=K[K[K[G+20>>2]>>2]+20>>2]+Q(Y,76)|0;g=K[b+20>>2]-K[b+24>>2]|0;if(g>>>0>31){break f}s=t;t=K[G+48>>2];k=s+t|0;h=Ne(k,k>>>0<t>>>0?l+1|0:l,h,0);l=h-K[b>>2]|0;g:{if((h>>>0>=l>>>0?l:0)>>>g|0){break g}h=m-K[b+4>>2]|0;if((h>>>0<=m>>>0?h:0)>>>g|0){break g}h=K[b+8>>2];l=h-o|0;if((h>>>0>=l>>>0?l:0)>>>g|0){break g}b=K[b+12>>2];h=b-n|0;if(!((b>>>0>=h>>>0?h:0)>>>g|0)){break f}}K[G+64>>2]=0;break e}Y=Y+1|0;if((x|0)!=(Y|0)){continue}break}if(!K[G+64>>2]){break e}t=0;while(1){b=K[K[K[G+20>>2]>>2]+20>>2]+Q(t,76)|0;g=K[b+28>>2]+Q(K[b+24>>2],152)|0;h=K[g-148>>2];l=K[g-140>>2];m=K[g-152>>2];g=K[g-144>>2];q=K[G+68>>2];h:{if(K[q+(t<<2)>>2]?0:q){break h}q=l-h|0;g=g-m|0;Le(q,0,g);if(!(!ua|(h|0)==(l|0))){Y=0;Fa(f,1,2945,0);break b}g=Q(g,q);if(g>>>0>=1073741824){Y=0;Fa(f,1,2945,0);break b}g=g<<2;K[b+44>>2]=g;i:{j:{k:{h=K[b+36>>2];if(h){if(g>>>0<=N[b+48>>2]){break h}if(K[b+40>>2]){break k}}g=Ma(g);K[b+36>>2]=g;h=g;g=K[b+44>>2];if(!(g?h:1)){break j}K[b+40>>2]=1;K[b+48>>2]=g;break h}Ga(h);g=Ma(K[b+44>>2]);K[b+36>>2]=g;if(g){break i}K[b+48>>2]=0;K[b+40>>2]=0;K[b+44>>2]=0}Y=0;Fa(f,1,2945,0);break b}K[b+40>>2]=1;K[b+48>>2]=K[b+44>>2]}t=t+1|0;H=K[G+24>>2];if(t>>>0<N[H+16>>2]){continue}break}break d}u=K[H+24>>2];A=K[K[K[G+20>>2]>>2]+20>>2];b=0;while(1){l:{if(K[(b<<2)+q>>2]?0:q){break l}g=A+Q(b,76)|0;l=K[g>>2];m=u+Q(b,52)|0;h=K[m>>2];k=h-1|0;n=K[G+48>>2];o=k+n|0;t=0-!h|0;n=Ne(o,n>>>0>o>>>0?t+1|0:t,h,0);l=l>>>0>n>>>0?l:n;K[g+56>>2]=l;n=K[g+4>>2];m=K[m+4>>2];s=m-1|0;r=K[G+52>>2];p=s+r|0;o=0-!m|0;r=Ne(p,p>>>0<r>>>0?o+1|0:o,m,0);n=n>>>0>r>>>0?n:r;K[g+60>>2]=n;r=K[g+8>>2];p=k;k=K[G+56>>2];p=p+k|0;h=Ne(p,k>>>0>p>>>0?t+1|0:t,h,0);h=h>>>0>r>>>0?r:h;K[g+64>>2]=h;t=K[g+12>>2];k=K[G+60>>2];s=s+k|0;m=Ne(s,k>>>0>s>>>0?o+1|0:o,m,0);m=m>>>0>t>>>0?t:m;K[g+68>>2]=m;if(m>>>0<n>>>0|h>>>0<l>>>0){break c}s=K[g+20>>2];if(!s){break l}r=m-1|0;y=0-!m|0;ga=h-1|0;D=0-!h|0;C=n-1|0;T=0-!n|0;ha=l-1|0;U=0-!l|0;M=K[g+28>>2];l=0;h=0;while(1){t=M+Q(l,152)|0;m=s+(l^-1)|0;g=m&31;if((m&63)>>>0>=32){k=1<<g;n=0}else{n=1<<g;k=n-1&1>>>32-g}g=r+n|0;o=k+y|0;p=g>>>0<r>>>0?o+1|0:o;o=m&31;if((m&63)>>>0>=32){g=p>>>o|0}else{g=((1<<o)-1&p)<<32-o|g>>>o}K[t+148>>2]=g;g=k+D|0;p=g+1|0;o=g;g=n+ga|0;p=g>>>0<n>>>0?p:o;o=m&31;if((m&63)>>>0>=32){g=p>>>o|0}else{g=((1<<o)-1&p)<<32-o|g>>>o}K[t+144>>2]=g;g=k+T|0;p=g+1|0;o=g;g=n+C|0;p=g>>>0<n>>>0?p:o;o=m&31;if((m&63)>>>0>=32){g=p>>>o|0}else{g=((1<<o)-1&p)<<32-o|g>>>o}K[t+140>>2]=g;g=k+U|0;o=n+ha|0;n=o>>>0<n>>>0?g+1|0:g;g=m&31;if((m&63)>>>0>=32){g=n>>>g|0}else{g=((1<<g)-1&n)<<32-g|o>>>g}K[t+136>>2]=g;l=l+1|0;h=l?h:h+1|0;if(h|(l|0)!=(s|0)){continue}break}}b=b+1|0;if((x|0)!=(b|0)){continue}break}}Y=0;K[sa+8>>2]=0;b=K[G+28>>2];ga=Ia(1,8);if(ga){K[ga+4>>2]=b;K[ga>>2]=H}if(!ga){break b}C=K[K[G+20>>2]>>2];A=ra-144|0;ra=A;x=K[G+36>>2];b=Q(x,5644);h=K[ga+4>>2];ha=b+K[h+76>>2]|0;Z=K[ha+420>>2];m=0;l=0;r=ra-32|0;ra=r;D=b+K[h+76>>2]|0;M=K[D+420>>2];y=K[ga>>2];u=K[y+16>>2];o=Ja(Q(u,528));m:{if(!o){break m}b=Ja(u<<2);n:{if(!b){b=o;break n}q=K[h+76>>2]+Q(x,5644)|0;n=K[q+420>>2];t=n+1|0;g=Ia(t,240);o:{if(g){p:{if(t){p=K[y+16>>2];t=g;while(1){K[t+236>>2]=f;k=Ia(p,16);K[t+200>>2]=k;if(!k){break p}k=K[y+16>>2];K[t+196>>2]=k;s=0;p=0;if(k){while(1){k=K[t+200>>2]+(s<<4)|0;p=K[q+5584>>2]+Q(s,1080)|0;T=Ia(K[p+4>>2],16);K[k+12>>2]=T;if(!T){break p}K[k+8>>2]=K[p+4>>2];s=s+1|0;p=K[y+16>>2];if(s>>>0<p>>>0){continue}break}}t=t+240|0;k=(n|0)==(w|0);w=w+1|0;if(!k){continue}break}}break o}q=K[g+4>>2];if(q){Ga(q);K[g+4>>2]=0}t=g;q=0;while(1){s=K[t+200>>2];if(s){p=0;w=K[t+196>>2];if(w){while(1){k=K[s+12>>2];if(k){Ga(k);K[s+12>>2]=0;w=K[t+196>>2]}s=s+16|0;p=p+1|0;if(p>>>0<w>>>0){continue}break}s=K[t+200>>2]}Ga(s);K[t+200>>2]=0}t=t+240|0;k=(n|0)==(q|0);q=q+1|0;if(!k){continue}break}Ga(g)}g=0}if(g){q:{if(!u){break q}q=o;if(u>>>0>=8){t=u&-8;while(1){n=(j<<2)+b|0;K[n>>2]=q;K[n+4>>2]=q+528;K[n+8>>2]=q+1056;K[n+12>>2]=q+1584;K[n+16>>2]=q+2112;K[n+20>>2]=q+2640;K[n+24>>2]=q+3168;K[n+28>>2]=q+3696;j=j+8|0;q=q+4224|0;m=m+8|0;if((t|0)!=(m|0)){continue}break}}m=u&7;if(!m){break q}while(1){K[(j<<2)+b>>2]=q;j=j+1|0;q=q+528|0;P=P+1|0;if((m|0)!=(P|0)){continue}break}}s=b;p=0;t=K[(K[h+76>>2]+Q(x,5644)|0)+5584>>2];q=K[y+24>>2];b=K[h+24>>2];m=(x>>>0)/(b>>>0)|0;b=K[h+4>>2]+Q(K[h+12>>2],x-Q(b,m)|0)|0;j=K[y>>2];K[r+20>>2]=b>>>0>j>>>0?b:j;j=b+K[h+12>>2]|0;b=b>>>0>j>>>0?-1:j;j=K[y+8>>2];K[r+16>>2]=b>>>0<j>>>0?b:j;b=K[h+8>>2]+Q(m,K[h+16>>2])|0;m=K[y+4>>2];K[r+12>>2]=b>>>0>m>>>0?b:m;h=b+K[h+16>>2]|0;b=b>>>0>h>>>0?-1:h;h=K[y+12>>2];K[r+8>>2]=b>>>0<h>>>0?b:h;K[r+24>>2]=0;K[r+28>>2]=0;K[r+4>>2]=2147483647;K[r>>2]=2147483647;if(K[y+16>>2]){while(1){b=s?K[s+(p<<2)>>2]:0;h=K[q+4>>2];w=h-1|0;m=K[r+8>>2];n=w+m|0;j=0-!h|0;n=Ne(n,m>>>0>n>>>0?j+1|0:j,h,0);m=K[q>>2];P=m-1|0;k=K[r+16>>2];T=P+k|0;x=0-!m|0;k=Ne(T,k>>>0>T>>>0?x+1|0:x,m,0);V=w;w=K[r+12>>2];T=V+w|0;j=Ne(T,w>>>0>T>>>0?j+1|0:j,h,0);h=K[r+20>>2];w=h+P|0;m=Ne(w,h>>>0>w>>>0?x+1|0:x,m,0);h=K[t+4>>2];if(h>>>0>N[r+28>>2]){K[r+28>>2]=h;h=K[t+4>>2]}if(h){ka=t+944|0;ja=t+812|0;ma=n-1|0;v=0-!n|0;F=k-1|0;H=0-!k|0;_=j-1|0;aa=0-!j|0;ca=m-1|0;ia=0-!m|0;w=0;while(1){m=w<<2;x=K[m+ka>>2];P=K[m+ja>>2];j=0;if(b){K[b+4>>2]=x;K[b>>2]=P;j=b+8|0}h=h-1|0;b=P+h|0;r:{if(b>>>0>31){break r}m=K[q>>2];if(m>>>0>-1>>>b>>>0){break r}n=K[r+4>>2];b=m<<b;K[r+4>>2]=b>>>0>n>>>0?n:b}b=h+x|0;s:{if(b>>>0>31){break s}m=K[q+4>>2];if(m>>>0>-1>>>b>>>0){break s}n=K[r>>2];b=m<<b;K[r>>2]=b>>>0>n>>>0?n:b}b=0;m=h&31;if((h&63)>>>0>=32){k=1<<m;n=0}else{n=1<<m;k=n-1&1>>>32-m}T=n;n=ma+T|0;m=k;k=v+k|0;$=n>>>0<ma>>>0?k+1|0:k;U=h&31;k=x&31;if((x&63)>>>0>=32){k=1<<k;V=0}else{V=1<<k;k=V-1&1>>>32-k}if((h&63)>>>0>=32){$=$>>>U|0}else{$=((1<<U)-1&$)<<32-U|n>>>U}n=V+$|0;da=n-1|0;k=(n>>>0<V>>>0?k+1|0:k)-!n|0;n=x&31;U=m+aa|0;V=T+_|0;U=V>>>0<T>>>0?U+1|0:U;if((x&63)>>>0>=32){k=k>>>n|0}else{k=((1<<n)-1&k)<<32-n|da>>>n}n=h&31;if((h&63)>>>0>=32){n=U>>>n|0}else{n=((1<<n)-1&U)<<32-n|V>>>n}U=(n|0)!=($|0)?k-(n>>>x|0)&-1>>>x:0;n=m+H|0;k=n+1|0;V=n;n=F+T|0;$=n>>>0<T>>>0?k:V;x=h&31;k=P&31;if((P&63)>>>0>=32){k=1<<k;V=0}else{V=1<<k;k=V-1&1>>>32-k}if((h&63)>>>0>=32){$=$>>>x|0}else{$=((1<<x)-1&$)<<32-x|n>>>x}n=V+$|0;da=n-1|0;x=(n>>>0<V>>>0?k+1|0:k)-!n|0;n=P&31;m=m+ia|0;V=T;T=T+ca|0;k=V>>>0>T>>>0?m+1|0:m;m=h&31;if((P&63)>>>0>=32){n=x>>>n|0}else{n=((1<<n)-1&x)<<32-n|da>>>n}if((h&63)>>>0>=32){m=k>>>m|0}else{m=((1<<m)-1&k)<<32-m|T>>>m}m=(m|0)!=($|0)?n-(m>>>P|0)&-1>>>P:0;if(j){K[j+4>>2]=U;K[j>>2]=m;b=j+8|0}m=Q(m,U);if(m>>>0>N[r+24>>2]){K[r+24>>2]=m}w=w+1|0;if(w>>>0<N[t+4>>2]){continue}break}}q=q+52|0;t=t+1080|0;p=p+1|0;if(p>>>0<N[y+16>>2]){continue}break}}P=M+1|0;w=K[r+28>>2];k=K[r+24>>2];K[g+4>>2]=0;b=K[D+8>>2]+1|0;T=Q(k,u);p=Q(T,w);Le(b,0,p);t:{if(!ua){b=Q(b,p);K[g+8>>2]=b;b=Ia(b,2);K[g+4>>2]=b;if(b){break t}}Ga(o);Ga(s);b=K[g+4>>2];if(b){Ga(b);K[g+4>>2]=0}if(!P){b=g;break n}b=0;m=g;while(1){q=K[m+200>>2];if(q){n=0;j=K[m+196>>2];if(j){while(1){h=K[q+12>>2];if(h){Ga(h);K[q+12>>2]=0;j=K[m+196>>2]}q=q+16|0;n=n+1|0;if(j>>>0>n>>>0){continue}break}q=K[m+200>>2]}Ga(q);K[m+200>>2]=0}m=m+240|0;h=(b|0)==(M|0);b=b+1|0;if(!h){continue}break}b=g;break n}x=K[y+24>>2];U=K[r+20>>2];K[g+204>>2]=U;ma=K[r+12>>2];K[g+208>>2]=ma;$=K[r+16>>2];K[g+212>>2]=$;ka=K[r+8>>2];K[g+216>>2]=ka;K[g+12>>2]=p;K[g+16>>2]=T;K[g+20>>2]=k;l=1;K[g+24>>2]=1;if(u){m=K[g+200>>2];t=0;b=x;while(1){q=K[s+(t<<2)>>2];K[m>>2]=K[b>>2];K[m+4>>2]=K[b+4>>2];h=K[m+8>>2];u:{if(!h){break u}n=K[m+12>>2];if((h|0)!=1){ja=h&-2;j=0;while(1){K[n>>2]=K[q>>2];K[n+4>>2]=K[q+4>>2];K[n+8>>2]=K[q+8>>2];K[n+12>>2]=K[q+12>>2];K[n+16>>2]=K[q+16>>2];K[n+20>>2]=K[q+20>>2];K[n+24>>2]=K[q+24>>2];K[n+28>>2]=K[q+28>>2];n=n+32|0;q=q+32|0;j=j+2|0;if((ja|0)!=(j|0)){continue}break}}if(!(h&1)){break u}K[n>>2]=K[q>>2];K[n+4>>2]=K[q+4>>2];K[n+8>>2]=K[q+8>>2];K[n+12>>2]=K[q+12>>2]}b=b+52|0;m=m+16|0;t=t+1|0;if((u|0)!=(t|0)){continue}break}}if(P>>>0>1){h=g;while(1){K[h+456>>2]=ka;K[h+452>>2]=$;K[h+448>>2]=ma;K[h+444>>2]=U;K[h+264>>2]=1;K[h+260>>2]=k;K[h+256>>2]=T;K[h+252>>2]=p;if(u){m=K[h+440>>2];t=0;b=x;while(1){q=K[s+(t<<2)>>2];K[m>>2]=K[b>>2];K[m+4>>2]=K[b+4>>2];P=K[m+8>>2];v:{if(!P){break v}n=K[m+12>>2];if((P|0)!=1){ja=P&-2;j=0;while(1){K[n>>2]=K[q>>2];K[n+4>>2]=K[q+4>>2];K[n+8>>2]=K[q+8>>2];K[n+12>>2]=K[q+12>>2];K[n+16>>2]=K[q+16>>2];K[n+20>>2]=K[q+20>>2];K[n+24>>2]=K[q+24>>2];K[n+28>>2]=K[q+28>>2];n=n+32|0;q=q+32|0;j=j+2|0;if((ja|0)!=(j|0)){continue}break}}if(!(P&1)){break v}K[n>>2]=K[q>>2];K[n+4>>2]=K[q+4>>2];K[n+8>>2]=K[q+8>>2];K[n+12>>2]=K[q+12>>2]}b=b+52|0;m=m+16|0;t=t+1|0;if((u|0)!=(t|0)){continue}break}}b=K[h+8>>2];K[h+244>>2]=K[h+4>>2];K[h+248>>2]=b;b=(l|0)!=(M|0);h=h+240|0;l=l+1|0;if(b){continue}break}}Ga(o);Ga(s);b=K[D+420>>2];w:{if(L[D+5640|0]&4){if((b|0)==-1){break w}n=D+424|0;h=K[D+8>>2];j=0;q=g;while(1){l=K[n+36>>2];K[q+44>>2]=1;K[q+84>>2]=l;K[q+48>>2]=K[n>>2];l=K[n+4>>2];K[q+68>>2]=0;K[q+72>>2]=0;K[q+52>>2]=l;K[q+60>>2]=K[n+12>>2];K[q+64>>2]=K[n+16>>2];l=K[n+8>>2];K[q+76>>2]=k;K[q+56>>2]=h>>>0>l>>>0?l:h;n=n+148|0;q=q+240|0;l=(b|0)==(j|0);j=j+1|0;if(!l){continue}break}break w}if((b|0)==-1){break w}h=K[D+8>>2];l=K[D+4>>2];q=g;if(b){m=b+1&-2;P=0;while(1){K[q+68>>2]=0;K[q+72>>2]=0;K[q+52>>2]=0;K[q+44>>2]=1;K[q+48>>2]=0;K[q+84>>2]=l;K[q+60>>2]=w;K[q+324>>2]=l;K[q+76>>2]=k;K[q+56>>2]=h;K[q+308>>2]=0;K[q+312>>2]=0;K[q+292>>2]=0;K[q+284>>2]=1;K[q+288>>2]=0;K[q+300>>2]=w;K[q+296>>2]=h;K[q+316>>2]=k;K[q+64>>2]=K[q+196>>2];K[q+304>>2]=K[q+436>>2];q=q+480|0;P=P+2|0;if((m|0)!=(P|0)){continue}break}}if(b&1){break w}K[q+68>>2]=0;K[q+72>>2]=0;K[q+52>>2]=0;K[q+44>>2]=1;K[q+48>>2]=0;K[q+84>>2]=l;K[q+60>>2]=w;K[q+76>>2]=k;K[q+56>>2]=h;K[q+64>>2]=K[q+196>>2]}l=g;break m}Ga(o)}Ga(b)}ra=r+32|0;g=l;x:{y:{if(!g){break y}P=Z+1|0;t=ba;x=g;z:{A:{while(1){if(K[x+84>>2]==-1){break z}o=Ja(K[y+16>>2]<<2);if(!o){break z}b=K[y+16>>2]<<2;if(b){B(o,1,b)}if(jc(x)){while(1){m=K[C+20>>2];B:{C:{if(N[x+40>>2]>=N[ha+12>>2]){break C}b=K[x+32>>2];h=Q(K[x+28>>2],76)+m|0;if(b>>>0>=N[h+24>>2]){break C}h=K[h+28>>2]+Q(b,152)|0;if(!K[h+24>>2]){break C}l=h+28|0;r=0;D:{while(1){m=l+Q(r,36)|0;b=K[m+20>>2]+Q(K[x+36>>2],40)|0;if(!Fb(G,K[x+28>>2],K[x+32>>2],K[m+16>>2],K[b>>2],K[b+4>>2],K[b+8>>2],K[b+12>>2])){r=r+1|0;if(r>>>0<N[h+24>>2]){continue}break D}break}K[o+(K[x+28>>2]<<2)>>2]=0;K[A+136>>2]=0;if(!ic(K[ga+4>>2],K[C+20>>2],ha,x,A+140|0,t,A+136|0,i,f)){break A}r=K[x+32>>2];n=K[x+28>>2];s=K[A+136>>2];if(K[A+140>>2]){K[A+136>>2]=0;D=K[(K[C+20>>2]+Q(n,76)|0)+28>>2]+Q(r,152)|0;m=K[D+24>>2];if(m){k=i-s|0;T=i+t|0;n=D+28|0;h=0;j=0;U=s+t|0;w=U;while(1){E:{if(K[n+8>>2]==K[n>>2]|K[n+12>>2]==K[n+4>>2]){break E}b=K[n+20>>2]+Q(K[x+36>>2],40)|0;M=Q(K[b+20>>2],K[b+16>>2]);if(!M){break E}m=K[b+24>>2];p=0;while(1){q=K[m+36>>2];if(q){F:{if(j|K[m+64>>2]){K[m+52>>2]=0;r=1;b=64;break F}r=K[m>>2];b=K[m+40>>2];G:{if(b){r=Q(b,24)+r|0;if(K[r-20>>2]!=K[r-12>>2]){r=r-24|0;break G}b=b+1|0}else{b=1}K[m+40>>2]=b}b=K[r+20>>2];H:{I:{if(b>>>0>(w^-1)>>>0){break I}l=r+20|0;while(1){if(T>>>0<b+w>>>0){break I}u=K[m+4>>2];j=K[m+52>>2];if((j|0)!=K[m+56>>2]){l=q}else{b=j<<1|1;u=La(u,b<<3);if(!u){Fa(f,1,1024,0);break A}K[m+56>>2]=b;K[m+4>>2]=u;j=K[m+52>>2];b=K[l>>2];l=K[m+36>>2]}q=(j<<3)+u|0;K[q+4>>2]=b;K[q>>2]=w;K[m+52>>2]=j+1;K[r>>2]=K[r>>2]+b;j=K[r+16>>2];u=j+K[r+4>>2]|0;K[r+4>>2]=u;q=l-j|0;K[m+36>>2]=q;K[r+8>>2]=u;w=b+w|0;b=0;if((j|0)==(l|0)){break H}K[m+40>>2]=K[m+40>>2]+1;l=r+44|0;b=K[r+44>>2];r=r+24|0;if((w^-1)>>>0>=b>>>0){continue}break}}l=K[x+28>>2];q=K[x+32>>2];j=K[x+36>>2];if(K[K[ga+4>>2]+104>>2]){K[A+120>>2]=l;K[A+116>>2]=q;K[A+112>>2]=h;K[A+108>>2]=j;K[A+104>>2]=p;K[A+100>>2]=k;K[A+96>>2]=b;Fa(f,1,14656,A+96|0);break A}K[A+88>>2]=l;K[A+84>>2]=q;K[A+80>>2]=h;K[A+76>>2]=j;K[A+72>>2]=p;K[A+68>>2]=k;K[A+64>>2]=b;Fa(f,2,14656,A- -64|0);K[m+52>>2]=0;K[m+64>>2]=1;b=1}j=b;r=K[m+40>>2];b=44}K[b+m>>2]=r}m=m+68|0;p=p+1|0;if((M|0)!=(p|0)){continue}break}m=K[D+24>>2]}n=n+36|0;h=h+1|0;if(m>>>0>h>>>0){continue}break}r=K[x+32>>2];n=K[x+28>>2];b=j?k:w-U|0}else{b=0}s=b+s|0}h=K[y+24>>2]+Q(n,52)|0;b=K[h+36>>2];K[h+36>>2]=b>>>0<r>>>0?r:b;break B}m=K[C+20>>2]}K[A+136>>2]=0;if(!ic(K[ga+4>>2],m,ha,x,A+140|0,t,A+136|0,i,f)){break A}n=K[x+28>>2];s=K[A+136>>2];if(!K[A+140>>2]){break B}T=K[x+32>>2];b=K[(K[C+20>>2]+Q(n,76)|0)+28>>2]+Q(T,152)|0;U=K[b+24>>2];if(!U){break B}k=i-s|0;u=b+28|0;D=K[x+36>>2];r=0;j=0;J:{K:{while(1){L:{if(K[u+8>>2]==K[u>>2]|K[u+12>>2]==K[u+4>>2]){break L}b=K[u+20>>2]+Q(D,40)|0;M=Q(K[b+20>>2],K[b+16>>2]);if(!M){break L}q=K[b+24>>2];h=0;while(1){b=K[q+36>>2];if(b){m=K[q>>2];p=K[q+40>>2];M:{if(p){m=Q(p,24)+m|0;if(K[m-20>>2]!=K[m-12>>2]){m=m-24|0;break M}p=p+1|0}else{p=1}K[q+40>>2]=p}w=K[m+20>>2];r=w+r|0;if(r>>>0<w>>>0|k>>>0<r>>>0){break J}while(1){N:{w=K[m+16>>2];K[m+4>>2]=w+K[m+4>>2];l=b-w|0;if((b|0)==(w|0)){break N}p=p+1|0;K[q+40>>2]=p;w=K[m+44>>2];r=w+r|0;if(r>>>0<w>>>0){break K}m=m+24|0;b=l;if(k>>>0>=r>>>0){continue}break K}break}K[q+36>>2]=l}q=q+68|0;h=h+1|0;if((M|0)!=(h|0)){continue}break}}u=u+36|0;j=j+1|0;if((U|0)!=(j|0)){continue}break}s=r+s|0;break B}K[q+36>>2]=l}if(!K[K[ga+4>>2]+104>>2]){K[A+24>>2]=n;K[A+20>>2]=T;K[A+16>>2]=j;K[A+12>>2]=D;K[A+8>>2]=h;K[A+4>>2]=k;K[A>>2]=w;Fa(f,2,14571,A);n=K[x+28>>2];s=k+s|0;break B}K[A+56>>2]=n;K[A+52>>2]=T;K[A+48>>2]=j;K[A+44>>2]=D;K[A+40>>2]=h;K[A+36>>2]=k;K[A+32>>2]=w;Fa(f,1,14571,A+32|0);break A}O:{if(!K[o+(n<<2)>>2]){break O}b=K[y+24>>2]+Q(n,52)|0;if(K[b+36>>2]){break O}K[b+36>>2]=K[(K[C+20>>2]+Q(n,76)|0)+24>>2]-1}i=i-s|0;t=s+t|0;if(jc(x)){continue}break}}Ga(o);x=x+240|0;z=z+1|0;if(z>>>0<=N[ha+420>>2]){continue}break}Gb(g,P);K[sa+8>>2]=t-ba;b=1;break x}Gb(g,P);Ga(o);break y}Gb(g,P)}b=0}ra=A+144|0;kb(ga);if(!b){break b}Y=K[K[G+32>>2]+5584>>2];o=K[K[G+20>>2]>>2];F=K[o+20>>2];K[sa+12>>2]=1;t=0;b=K[G+32>>2];s=K[Y+16>>2]>>>4&1&K[b+12>>2]==K[b+8>>2];H=K[o+16>>2];P:{if(!H){break P}while(1){b=K[G+68>>2];if(!(K[b+(t<<2)>>2]?0:b)){r=sa+12|0;H=0;b=K[F+24>>2];Q:{if(!b){break Q}k=K[G+44>>2];while(1){l=K[F+28>>2]+Q(H,152)|0;q=K[l+24>>2];if(q){w=l+28|0;b=K[l+20>>2];x=K[l+16>>2];j=0;while(1){if(Q(b,x)){n=w+Q(j,36)|0;m=0;while(1){g=K[n+20>>2]+Q(m,40)|0;i=Fb(G,K[F+16>>2],H,K[n+16>>2],K[g>>2],K[g+4>>2],K[g+8>>2],K[g+12>>2]);h=K[g+16>>2];q=K[g+20>>2];b=Q(h,q);R:{if(i){if(!b){break R}h=0;while(1){i=K[g+24>>2]+Q(h,68)|0;S:{if(!Fb(G,K[F+16>>2],H,K[n+16>>2],K[i+8>>2],K[i+12>>2],K[i+16>>2],K[i+20>>2])){b=K[i+60>>2];if(!b){break S}Ga(b);K[i+60>>2]=0;break S}if(!K[G+64>>2]){if(K[i+60>>2]|K[i+16>>2]==K[i+8>>2]|K[i+20>>2]==K[i+12>>2]){break S}}b=Ia(1,44);if(!b){K[sa+12>>2]=0;break Q}q=K[G+64>>2];K[b+36>>2]=0;K[b+28>>2]=r;K[b+20>>2]=Y;K[b+16>>2]=F;K[b+12>>2]=n;K[b+8>>2]=i;K[b+4>>2]=H;K[b>>2]=q;K[b+40>>2]=s;K[b+32>>2]=f;K[b+24>>2]=K[k+4>>2]>1;lb(k,14,b);if(!K[sa+12>>2]){break Q}}h=h+1|0;if(h>>>0<Q(K[g+20>>2],K[g+16>>2])>>>0){continue}break}break R}if(!b){break R}x=0;while(1){b=K[g+24>>2]+Q(x,68)|0;i=K[b+60>>2];if(i){Ga(i);K[b+60>>2]=0;q=K[g+20>>2];h=K[g+16>>2]}x=x+1|0;if(x>>>0<Q(h,q)>>>0){continue}break}}m=m+1|0;b=K[l+20>>2];x=K[l+16>>2];if(m>>>0<Q(b,x)>>>0){continue}break}q=K[l+24>>2]}j=j+1|0;if(q>>>0>j>>>0){continue}break}b=K[F+24>>2]}H=H+1|0;if(H>>>0<b>>>0){continue}break}}if(!K[sa+12>>2]){break P}H=K[o+16>>2]}Y=Y+1080|0;F=F+76|0;t=t+1|0;if(H>>>0>t>>>0){continue}break}}Y=0;Xa(K[G+44>>2]);if(!K[sa+12>>2]){break b}T:{if(K[G+64>>2]){break T}t=K[G+24>>2];if(!K[t+16>>2]){break T}F=0;while(1){b=K[K[K[G+20>>2]>>2]+20>>2]+Q(F,76)|0;g=K[b+28>>2]+Q(K[(K[t+24>>2]+Q(F,52)|0)+36>>2],152)|0;i=K[g+136>>2];h=K[g+144>>2];l=K[g+140>>2];g=K[g+148>>2];Ga(K[b+52>>2]);K[b+52>>2]=0;U:{m=K[G+68>>2];if((h|0)==(i|0)|(g|0)==(l|0)|(K[m+(F<<2)>>2]?0:m)){break U}g=g-l|0;i=h-i|0;Le(g,0,i);if(ua){Fa(f,1,2945,0);break b}g=Q(g,i);if(g>>>0>=1073741824){Fa(f,1,2945,0);break b}i=b;b=Ma(g<<2);K[i+52>>2]=b;if(b){break U}Fa(f,1,2945,0);break b}F=F+1|0;t=K[G+24>>2];if(F>>>0<N[t+16>>2]){continue}break}}t=K[G+32>>2];x=K[K[G+20>>2]>>2];if(K[x+16>>2]){F=K[x+20>>2];t=K[t+5584>>2];H=K[K[G+24>>2]+24>>2];q=0;while(1){V:{b=K[G+68>>2];if(K[b+(q<<2)>>2]?0:b){break V}o=K[H+36>>2]+1|0;if(K[t+20>>2]==1){A=o;b=0;ia=ra-32|0;ra=ia;W:{X:{if(K[G+64>>2]){g=1;if((o|0)==1){break W}l=K[F+28>>2];b=l+Q(K[F+24>>2],152)|0;h=K[b-144>>2];m=K[b-152>>2];if((h|0)==(m|0)){break W}p=o-1|0;n=p&1;j=K[G+44>>2];k=K[j+4>>2];Y:{if((o|0)==2){b=0;i=l;break Y}o=p&-2;b=0;i=l;g=0;while(1){s=K[i+160>>2]-K[i+152>>2]|0;b=b>>>0>s>>>0?b:s;s=K[i+164>>2]-K[i+156>>2]|0;b=b>>>0>s>>>0?b:s;s=K[i+312>>2]-K[i+304>>2]|0;b=b>>>0>s>>>0?b:s;s=K[i+316>>2]-K[i+308>>2]|0;b=b>>>0>s>>>0?b:s;i=i+304|0;g=g+2|0;if((o|0)!=(g|0)){continue}break}}g=0;if(n){n=K[i+160>>2]-K[i+152>>2]|0;b=b>>>0>n>>>0?b:n;i=K[i+164>>2]-K[i+156>>2]|0;b=b>>>0>i>>>0?b:i}if(b>>>0>134217727){break W}i=K[l+4>>2];s=K[l+12>>2];w=K[l>>2];u=K[l+8>>2];r=b<<5;n=sb(r);K[ia+16>>2]=n;if(!n){break W}o=h-m|0;h=s-i|0;g=u-w|0;K[ia>>2]=n;while(1){s=K[F+36>>2];m=h;K[ia+8>>2]=h;b=g;K[ia+24>>2]=b;i=K[l+156>>2];h=K[l+164>>2];g=K[l+160>>2];w=K[l+152>>2];K[ia+28>>2]=(w|0)%2;g=g-w|0;K[ia+20>>2]=g-b;z=(k|0)<2;h=h-i|0;Z:{if(!(!z&h>>>0>1)){i=0;if(!h){break Z}while(1){pc(ia+16|0,s+(Q(i,o)<<2)|0);i=i+1|0;if((i|0)!=(h|0)){continue}break}break Z}w=h>>>0<k>>>0?h:k;ba=w-1|0;u=(h>>>0)/(w>>>0)|0;b=0;while(1){i=Ja(36);if(!i){break X}A=K[ia+20>>2];K[i>>2]=K[ia+16>>2];K[i+4>>2]=A;A=K[ia+28>>2];K[i+8>>2]=K[ia+24>>2];K[i+12>>2]=A;K[i+28>>2]=Q(b,u);K[i+24>>2]=s;K[i+20>>2]=o;K[i+16>>2]=g;A=(b|0)==(ba|0);b=b+1|0;K[i+32>>2]=A?h:Q(u,b);A=sb(r);K[i>>2]=A;if(!A){g=0;Xa(j);Ga(i);Ga(n);break W}lb(j,10,i);if((b|0)!=(w|0)){continue}break}Xa(j)}K[ia+4>>2]=h-m;K[ia+12>>2]=K[l+156>>2]%2;_:{if(!(!z&g>>>0>1)){b=8;i=0;if(g>>>0>=8){while(1){rb(ia,s+(i<<2)|0,o,8);i=b;b=b+8|0;if(g>>>0>=b>>>0){continue}break}}if(g>>>0<=i>>>0){break _}rb(ia,s+(i<<2)|0,o,g-i|0);break _}m=g>>>0<k>>>0?g:k;u=m-1|0;w=(g>>>0)/(m>>>0)|0;b=0;while(1){i=Ja(36);if(!i){break X}z=K[ia+4>>2];K[i>>2]=K[ia>>2];K[i+4>>2]=z;z=K[ia+12>>2];K[i+8>>2]=K[ia+8>>2];K[i+12>>2]=z;K[i+28>>2]=Q(b,w);K[i+24>>2]=s;K[i+20>>2]=o;K[i+16>>2]=h;z=(b|0)==(u|0);b=b+1|0;K[i+32>>2]=z?g:Q(w,b);z=sb(r);K[i>>2]=z;if(!z){g=0;Xa(j);Ga(i);Ga(n);break W}lb(j,11,i);if((b|0)!=(m|0)){continue}break}Xa(j)}l=l+152|0;p=p-1|0;if(p){continue}break}g=1;Ga(n);break W}g=1;m=K[F+28>>2];ta=m+Q(A,152)|0;pb=ta-152|0;if(K[pb>>2]==K[ta-144>>2]){break W}qb=ta-148|0;if(K[qb>>2]==K[ta-140>>2]){break W}h=K[m+4>>2];l=K[m+12>>2];j=K[m>>2];n=K[m+8>>2];ga=K[F+68>>2];P=K[F+64>>2];D=K[F+60>>2];C=K[F+56>>2];la=oc(F,A);if(!la){g=0;break W}$:{aa:{if((A|0)!=1){g=A-1|0;o=g&1;ba:{if((A|0)==2){i=m;break ba}k=g&-2;i=m;g=0;while(1){s=K[i+160>>2]-K[i+152>>2]|0;b=b>>>0>s>>>0?b:s;s=K[i+164>>2]-K[i+156>>2]|0;b=b>>>0>s>>>0?b:s;s=K[i+312>>2]-K[i+304>>2]|0;b=b>>>0>s>>>0?b:s;s=K[i+316>>2]-K[i+308>>2]|0;b=b>>>0>s>>>0?b:s;i=i+304|0;g=g+2|0;if((k|0)!=(g|0)){continue}break}}if(o){g=K[i+160>>2]-K[i+152>>2]|0;b=b>>>0>g>>>0?b:g;g=K[i+164>>2]-K[i+156>>2]|0;b=b>>>0>g>>>0?b:g}if(b>>>0>=268435456){break $}v=sb(b<<4);if(!v){break $}ca:{if(!A){break ca}w=l-h|0;s=n-j|0;na=v-4|0;zb=v+44|0;Ab=v+40|0;Bb=v+36|0;tb=v+28|0;ma=v+24|0;$=v+20|0;Pa=v-12|0;ca=v+12|0;aa=v+8|0;Qa=v-16|0;Aa=v-8|0;_=v+4|0;Ba=1;da:while(1){b=K[m+156>>2];Ra=(b|0)%2|0;g=K[m+152>>2];wa=(g|0)%2|0;T=K[m+164>>2]-b|0;V=T-w|0;ha=K[m+160>>2]-g|0;da=ha-s|0;h=C;g=h;p=D;o=p;b=P;oa=b;i=ga;u=i;l=K[F+20>>2];ea:{if((l|0)==(Ba|0)){break ea}j=l-Ba|0;o=0;g=0;if(h){b=j&31;if((j&63)>>>0>=32){k=-1<<b;g=0}else{g=-1<<b;k=g|(1<<b)-1&-1>>>32-b}b=h+(g^-1)|0;g=k^-1;i=b>>>0<C>>>0?g+1|0:g;g=j&31;if((j&63)>>>0>=32){g=i>>>g|0}else{g=((1<<g)-1&i)<<32-g|b>>>g}}if(D){b=j&31;if((j&63)>>>0>=32){k=-1<<b;i=0}else{i=-1<<b;k=i|(1<<b)-1&-1>>>32-b}b=D+(i^-1)|0;i=k^-1;h=b>>>0<D>>>0?i+1|0:i;i=j&31;if((j&63)>>>0>=32){o=h>>>i|0}else{o=((1<<i)-1&h)<<32-i|b>>>i}}i=0;b=0;if(P){b=j&31;if((j&63)>>>0>=32){k=-1<<b;h=0}else{h=-1<<b;k=h|(1<<b)-1&-1>>>32-b}b=P+(h^-1)|0;h=k^-1;l=b>>>0<P>>>0?h+1|0:h;h=j&31;if((j&63)>>>0>=32){b=l>>>h|0}else{b=((1<<h)-1&l)<<32-h|b>>>h}}if(ga){i=j&31;if((j&63)>>>0>=32){k=-1<<i;h=0}else{h=-1<<i;k=h|(1<<i)-1&-1>>>32-i}i=ga+(h^-1)|0;h=k^-1;l=i>>>0<ga>>>0?h+1|0:h;h=j&31;if((j&63)>>>0>=32){i=l>>>h|0}else{i=((1<<h)-1&l)<<32-h|i>>>h}}oa=0;h=0;n=1<<j-1;if(n>>>0<C>>>0){h=j&31;if((j&63)>>>0>=32){k=-1<<h;l=0}else{l=-1<<h;k=l|(1<<h)-1&-1>>>32-h}l=l^-1;h=l+(C-n|0)|0;k=k^-1;k=h>>>0<l>>>0?k+1|0:k;l=j&31;if((j&63)>>>0>=32){h=k>>>l|0}else{h=((1<<l)-1&k)<<32-l|h>>>l}}if(n>>>0<P>>>0){l=j&31;if((j&63)>>>0>=32){k=-1<<l;r=0}else{r=-1<<l;k=r|(1<<l)-1&-1>>>32-l}r=r^-1;l=r+(P-n|0)|0;k=k^-1;r=l>>>0<r>>>0?k+1|0:k;k=j&31;if((j&63)>>>0>=32){oa=r>>>k|0}else{oa=((1<<k)-1&r)<<32-k|l>>>k}}u=0;p=0;if(n>>>0<D>>>0){l=j&31;if((j&63)>>>0>=32){k=-1<<l;r=0}else{r=-1<<l;k=r|(1<<l)-1&-1>>>32-l}r=r^-1;l=r+(D-n|0)|0;k=k^-1;r=l>>>0<r>>>0?k+1|0:k;k=j&31;if((j&63)>>>0>=32){p=r>>>k|0}else{p=((1<<k)-1&r)<<32-k|l>>>k}}if(n>>>0>=ga>>>0){break ea}l=j&31;if((j&63)>>>0>=32){k=-1<<l;r=0}else{r=-1<<l;k=r|(1<<l)-1&-1>>>32-l}r=r^-1;l=r+(ga-n|0)|0;n=k^-1;k=l>>>0<r>>>0?n+1|0:n;n=j&31;if((j&63)>>>0>=32){u=k>>>n|0}else{u=((1<<n)-1&k)<<32-n|l>>>n}}l=K[m+180>>2];j=oa-l|0;j=j>>>0<=oa>>>0?j:0;n=j+2|0;j=j>>>0>n>>>0?-1:n;ea=j>>>0<da>>>0?j:da;j=K[m+216>>2];n=b-j|0;b=b>>>0>=n>>>0?n:0;n=b+2|0;b=b>>>0>n>>>0?-1:n;pa=b>>>0<s>>>0?b:s;b=(wa?ea:pa)<<1;n=(wa?pa:ea)<<1|1;Ca=b>>>0>n>>>0?b:n;b=Ca>>>0<ha>>>0;l=h-l|0;h=h>>>0>=l>>>0?l:0;l=h-2|0;r=h>>>0>=l>>>0?l:0;h=g-j|0;g=g>>>0>=h>>>0?h:0;h=g-2|0;k=g>>>0>=h>>>0?h:0;g=(wa?r:k)<<1;h=(wa?k:r)<<1|1;n=g>>>0<h>>>0;ba=K[m+184>>2];l=o-ba|0;l=l>>>0<=o>>>0?l:0;j=l-2|0;l=j>>>0<=l>>>0?j:0;z=l;o=K[m+220>>2];j=p-o|0;j=j>>>0<=p>>>0?j:0;p=j-2|0;j=j>>>0>=p>>>0?p:0;Z=j;p=i-ba|0;i=i>>>0>=p>>>0?p:0;p=i+2|0;i=i>>>0>p>>>0?-1:p;ba=i>>>0<w>>>0?i:w;ka=ba;i=u-o|0;i=i>>>0<=u>>>0?i:0;o=i+2|0;i=i>>>0>o>>>0?-1:o;va=i>>>0<V>>>0?i:V;o=va;if(Ra){Z=l;ka=o;z=j;o=ba}Sa=b?Ca:ha;n=n?g:h;ub=w+va|0;vb=j+w|0;if(T){qa=(k<<3)+v|0;b=da<<3;Da=b+na|0;g=(k|0)<(da|0);$a=g?qa+4|0:Da;U=(s|0)>(ea|0)?ea:s-1|0;u=0;ab=(s|0)>1|(da|0)>0;i=wa<<2;bb=(_-i|0)+(r<<3)|0;cb=i+qa|0;M=(da|0)>(pa|0)?pa:da;ja=k+1|0;eb=s+ea|0;fb=r+s|0;gb=(n<<2)+v|0;i=s<<3;Ta=i+Aa|0;Ea=i+na|0;Wa=b+Aa|0;hb=!s&(da|0)==1;b=Sa<<2;ib=b+v|0;jb=b+na|0;mb=((g?k:da)<<3)+na|0;while(1){fa:{if(!(u>>>0<ba>>>0&l>>>0<=u>>>0|u>>>0<ub>>>0&u>>>0>=vb>>>0)){y=u+1|0;break fa}if(ha>>>0>Ca>>>0){K[jb>>2]=0;K[ib>>2]=0}y=u+1|0;Ua(la,k,u,pa,y,cb,2,0);Ua(la,fb,u,eb,y,bb,2,0);ga:{ha:{ia:{if(!wa){if(!ab){break ga}if((k|0)>=(pa|0)){break ha}ja:{ka:{if((k|0)>0){b=K[mb>>2];break ka}b=K[_>>2];g=b;if((k|0)<0){break ja}}g=b;b=K[$a>>2]}K[qa>>2]=K[qa>>2]-((b+g|0)+2>>2);b=ja;g=b;i=k;if((b|0)>=(M|0)){break ia}while(1){b=(g<<3)+v|0;K[b>>2]=K[b>>2]-((K[((i<<3)+v|0)+4>>2]+K[b+4>>2]|0)+2>>2);i=g;g=g+1|0;if((M|0)!=(g|0)){continue}break}b=M;break ia}la:{if(!hb){b=k;if((pa|0)<=(b|0)){break la}while(1){g=(b<<3)+v|0;i=g;h=K[g+4>>2];ma:{na:{if((b|0)>=0){oa=K[((b|0)<(da|0)?g:Wa)>>2];g=b+1|0;break na}oa=K[v>>2];g=0;b=b+1|0;p=v;if(b){break ma}}if((g|0)>=(da|0)){b=g;p=Wa;break ma}b=g;p=(b<<3)+v|0}g=p;K[i+4>>2]=h-((K[g>>2]+oa|0)+2>>2);if((b|0)<(pa|0)){continue}break}break la}K[v>>2]=K[v>>2]/2;break ga}b=r;if((ea|0)<=(b|0)){break ga}while(1){g=b<<3;i=g+v|0;h=K[i>>2];oa:{if((b|0)<0){p=K[_>>2];oa=_;break oa}p=K[((b|0)<(s|0)?((b<<3)+v|0)+4|0:Ea)>>2];oa=_;if(!b){break oa}oa=Ea;if((b|0)>(s|0)){break oa}oa=g+na|0}g=oa;K[i>>2]=h+(K[g>>2]+p>>1);b=b+1|0;if((ea|0)!=(b|0)){continue}break}break ga}if((b|0)>=(pa|0)){break ha}while(1){g=(b<<3)+v|0;h=K[g>>2];pa:{qa:{if((b|0)>0){i=K[(((b|0)<(da|0)?b:da)<<3)+na>>2];break qa}i=K[_>>2];p=_;if((b|0)<0){break pa}}p=Da;if((b|0)>=(da|0)){break pa}p=((b<<3)+v|0)+4|0}K[g>>2]=h-((K[p>>2]+i|0)+2>>2);b=b+1|0;if((pa|0)!=(b|0)){continue}break}}if((r|0)>=(ea|0)){break ga}g=r;b=g;if((U|0)>(b|0)){while(1){g=(b<<3)+v|0;b=b+1|0;K[g+4>>2]=K[g+4>>2]+(K[(b<<3)+v>>2]+K[g>>2]>>1);if((b|0)!=(U|0)){continue}break}g=U}if((g|0)>=(ea|0)){break ga}while(1){b=g;ra:{sa:{if((b|0)>=0){h=K[((b|0)<(s|0)?(b<<3)+v|0:Ta)>>2];i=b+1|0;break sa}h=K[v>>2];i=0;g=b+1|0;p=v;if(g){break ra}}if((i|0)>=(s|0)){g=i;p=Ta;break ra}g=i;p=(g<<3)+v|0}i=p;b=(b<<3)+v|0;K[b+4>>2]=K[b+4>>2]+(K[i>>2]+h>>1);if((g|0)<(ea|0)){continue}break}}if(!db(la,n,u,Sa,y,gb,1,0)){break aa}}u=y;if((T|0)!=(u|0)){continue}break}}m=m+152|0;b=ka<<1;g=o<<1|1;b=b>>>0>g>>>0?b:g;Ca=b>>>0<T>>>0?b:T;b=l<<5;i=b|16;h=V<<5;g=(l|0)<(V|0);Ta=g?i+ca|0:h+na|0;Ea=g?i+aa|0:h+Aa|0;Wa=g?i+_|0:h+Pa|0;Da=g?i+v|0:h+Qa|0;o=(w|0)>(va|0)?va:w-1|0;k=(V|0)>0;$a=k|(w|0)>1;y=b+v|0;ab=y+(Ra<<4)|0;i=w<<3;ka=i-8|0;h=((w|0)<=0?ka:0)<<2;bb=h+ca|0;cb=h+aa|0;eb=h+_|0;fb=h+v|0;h=V<<3;ja=h-8|0;k=(k?0:ja)<<2;gb=k+ca|0;hb=k+aa|0;ib=k+_|0;jb=k+v|0;mb=((4-(Ra<<2)<<2)+v|0)+(j<<5)|0;k=(V|0)>(ba|0)?ba:V;r=l+1|0;s=z<<1;p=Z<<1|1;wb=p>>>0>s>>>0?s:p;Cb=(wb<<4)+v|0;da=b+ca|0;z=b+aa|0;U=b+_|0;b=w<<5;Db=b+ca|0;ea=i-1|0;Eb=b+aa|0;pa=i-2|0;Ib=b+_|0;oa=i-3|0;Jb=b+v|0;wa=i-4|0;Kb=h-5|0;Lb=h-6|0;Mb=h-7|0;Nb=!w&(V|0)==1;b=ka<<2;Ob=b+ca|0;Pb=b+aa|0;Qb=b+_|0;Rb=b+v|0;xb=h-4|0;b=xb<<2;Sb=b+ca|0;Tb=b+aa|0;Ub=b+_|0;Vb=b+v|0;b=(g?l:V)<<5;Wb=b+na|0;p=b+Aa|0;u=b+Pa|0;Xb=b+Qa|0;b=ja<<2;Yb=b+ca|0;Zb=b+aa|0;_b=b+_|0;$b=b+v|0;while(1){ta:{ua:{va:{wa:{s=n;if(n>>>0<Sa>>>0){b=Sa-n|0;n=n+(b>>>0>=4?4:b)|0;Ua(la,s,l,n,ba,ab,1,8);Ua(la,s,vb,n,ub,mb,1,8);if(!Ra){if(!$a){break ta}if((l|0)>=(ba|0)){break ua}xa:{if((l|0)>0){b=K[Xb>>2];h=p;i=u;g=Wb;break xa}b=K[v+16>>2];if((l|0)<0){break wa}h=ma;i=$;g=tb}K[y>>2]=K[y>>2]-((K[Da>>2]+b|0)+2>>2);K[U>>2]=K[U>>2]-((K[i>>2]+K[Wa>>2]|0)+2>>2);K[z>>2]=K[z>>2]-((K[h>>2]+K[Ea>>2]|0)+2>>2);b=K[Ta>>2];g=K[g>>2];break va}if(Nb){K[v>>2]=K[v>>2]/2;K[v+4>>2]=K[v+4>>2]/2;K[aa>>2]=K[aa>>2]/2;K[ca>>2]=K[ca>>2]/2;break ta}b=l;if((ba|0)>(b|0)){while(1){i=b<<3;ya:{za:{if((b|0)<0){if((b|0)==-1){break za}g=(i<<2)+v|0;K[g+16>>2]=K[g+16>>2]-((K[v>>2]<<1)+2>>2);K[g+20>>2]=K[g+20>>2]-((K[v+4>>2]<<1)+2>>2);K[g+24>>2]=K[g+24>>2]-((K[aa>>2]<<1)+2>>2);K[g+28>>2]=K[g+28>>2]-((K[ca>>2]<<1)+2>>2);break ya}g=(i<<2)+v|0;h=K[g+16>>2];M=b+1|0;if((M|0)>=(V|0)){Z=h;h=(b|0)<(V|0);K[g+16>>2]=Z-((K[((h?i:ja)<<2)+v>>2]+K[$b>>2]|0)+2>>2);K[g+20>>2]=K[g+20>>2]-((K[((h?i|1:Mb)<<2)+v>>2]+K[_b>>2]|0)+2>>2);K[g+24>>2]=K[g+24>>2]-((K[((h?i|2:Lb)<<2)+v>>2]+K[Zb>>2]|0)+2>>2);K[g+28>>2]=K[g+28>>2]-((K[((h?i|3:Kb)<<2)+v>>2]+K[Yb>>2]|0)+2>>2);break ya}i=(M<<5)+v|0;K[g+16>>2]=h-((K[g>>2]+K[i>>2]|0)+2>>2);K[g+20>>2]=K[g+20>>2]-((K[g+4>>2]+K[i+4>>2]|0)+2>>2);K[g+24>>2]=K[g+24>>2]-((K[g+8>>2]+K[i+8>>2]|0)+2>>2);K[g+28>>2]=K[g+28>>2]-((K[g+12>>2]+K[i+12>>2]|0)+2>>2);break ya}K[Qa>>2]=K[Qa>>2]-((K[v>>2]+K[jb>>2]|0)+2>>2);K[Pa>>2]=K[Pa>>2]-((K[v+4>>2]+K[ib>>2]|0)+2>>2);K[Aa>>2]=K[Aa>>2]-((K[aa>>2]+K[hb>>2]|0)+2>>2);K[na>>2]=K[na>>2]-((K[ca>>2]+K[gb>>2]|0)+2>>2)}b=b+1|0;if((ba|0)!=(b|0)){continue}break}}b=j;if((va|0)<=(b|0)){break ta}while(1){i=b<<3;Aa:{if((b|0)<0){g=(i<<2)+v|0;K[g>>2]=K[g>>2]+(K[v+16>>2]<<1>>1);K[g+4>>2]=K[g+4>>2]+(K[v+20>>2]<<1>>1);K[g+8>>2]=K[g+8>>2]+(K[v+24>>2]<<1>>1);K[g+12>>2]=K[g+12>>2]+(K[v+28>>2]<<1>>1);break Aa}if(b){h=i<<2;g=h+v|0;M=(b|0)>(w|0);Z=(b|0)<(w|0);K[g>>2]=K[g>>2]+(K[(M?Jb:g)-16>>2]+K[((Z?i|4:wa)<<2)+v>>2]>>1);K[g+4>>2]=K[g+4>>2]+(K[(M?Ib:h+_|0)-16>>2]+K[((Z?i|5:oa)<<2)+v>>2]>>1);K[g+8>>2]=K[g+8>>2]+(K[(M?Eb:h+aa|0)-16>>2]+K[((Z?i|6:pa)<<2)+v>>2]>>1);K[g+12>>2]=K[g+12>>2]+(K[(M?Db:h+ca|0)-16>>2]+K[((Z?i|7:ea)<<2)+v>>2]>>1);break Aa}g=(b|0)<(w|0);K[v>>2]=K[v>>2]+(K[v+16>>2]+K[((g?4:wa)<<2)+v>>2]>>1);K[v+4>>2]=K[v+4>>2]+(K[v+20>>2]+K[((g?5:oa)<<2)+v>>2]>>1);K[aa>>2]=K[aa>>2]+(K[v+24>>2]+K[((g?6:pa)<<2)+v>>2]>>1);K[ca>>2]=K[ca>>2]+(K[v+28>>2]+K[((g?7:ea)<<2)+v>>2]>>1)}b=b+1|0;if((va|0)!=(b|0)){continue}break}break ta}s=ha;w=T;Ba=Ba+1|0;if((A|0)!=(Ba|0)){continue da}break ca}K[y>>2]=K[y>>2]-((b<<1)+2>>2);K[U>>2]=K[U>>2]-((K[$>>2]<<1)+2>>2);K[z>>2]=K[z>>2]-((K[ma>>2]<<1)+2>>2);b=K[tb>>2];g=b}K[da>>2]=K[da>>2]-((b+g|0)+2>>2);i=l;g=r;b=g;if((k|0)>(b|0)){while(1){b=(g<<5)+v|0;i=i<<5|16;K[b>>2]=K[b>>2]-((K[i+v>>2]+K[b+16>>2]|0)+2>>2);K[b+4>>2]=K[b+4>>2]-((K[i+_>>2]+K[b+20>>2]|0)+2>>2);K[b+8>>2]=K[b+8>>2]-((K[i+aa>>2]+K[b+24>>2]|0)+2>>2);K[b+12>>2]=K[b+12>>2]-((K[i+ca>>2]+K[b+28>>2]|0)+2>>2);i=g;g=g+1|0;if((k|0)!=(g|0)){continue}break}b=k}if((b|0)>=(ba|0)){break ua}while(1){i=b<<3;M=i|4;h=(b|0)<(V|0);Ba:{if((b|0)<=0){Z=K[v+16>>2];if((b|0)>=0){qa=i<<2;g=qa+v|0;Ka=g;Oa=K[g>>2];g=(h?M:xb)<<2;K[Ka>>2]=Oa-((Z+K[g+v>>2]|0)+2>>2);h=_+qa|0;K[h>>2]=K[h>>2]-((K[v+20>>2]+K[g+_>>2]|0)+2>>2);h=aa+qa|0;K[h>>2]=K[h>>2]-((K[v+24>>2]+K[g+aa>>2]|0)+2>>2);g=(K[v+28>>2]+K[g+ca>>2]|0)+2|0;break Ba}g=i<<2;h=g+v|0;K[h>>2]=K[h>>2]-((Z<<1)+2>>2);h=g+_|0;K[h>>2]=K[h>>2]-((K[v+20>>2]<<1)+2>>2);g=g+aa|0;K[g>>2]=K[g>>2]-((K[v+24>>2]<<1)+2>>2);g=(K[v+28>>2]<<1)+2|0;break Ba}g=((h?b:V)<<3)-4<<2;Z=K[g+v>>2];if(!h){h=i<<2;M=h+v|0;K[M>>2]=K[M>>2]-((Z+K[Vb>>2]|0)+2>>2);M=h+_|0;K[M>>2]=K[M>>2]-((K[g+_>>2]+K[Ub>>2]|0)+2>>2);h=h+aa|0;K[h>>2]=K[h>>2]-((K[g+aa>>2]+K[Tb>>2]|0)+2>>2);g=(K[g+ca>>2]+K[Sb>>2]|0)+2|0;break Ba}qa=i<<2;h=qa+v|0;Ka=h;Oa=K[h>>2];h=M<<2;K[Ka>>2]=Oa-((Z+K[h+v>>2]|0)+2>>2);M=_+qa|0;K[M>>2]=K[M>>2]-((K[g+_>>2]+K[h+_>>2]|0)+2>>2);M=aa+qa|0;K[M>>2]=K[M>>2]-((K[g+aa>>2]+K[h+aa>>2]|0)+2>>2);g=(K[g+ca>>2]+K[h+ca>>2]|0)+2|0}i=(i<<2)+ca|0;K[i>>2]=K[i>>2]-(g>>2);b=b+1|0;if((ba|0)!=(b|0)){continue}break}}if((j|0)>=(va|0)){break ta}h=j;b=h;if((o|0)>(b|0)){while(1){g=h<<5;b=g+v|0;K[b+16>>2]=K[b+16>>2]+(K[b+32>>2]+K[b>>2]>>1);K[b+20>>2]=K[b+20>>2]+(K[g+Bb>>2]+K[b+4>>2]>>1);K[b+24>>2]=K[b+24>>2]+(K[g+Ab>>2]+K[b+8>>2]>>1);K[b+28>>2]=K[b+28>>2]+(K[g+zb>>2]+K[b+12>>2]>>1);h=h+1|0;if((o|0)!=(h|0)){continue}break}b=o}if((b|0)>=(va|0)){break ta}while(1){g=b<<3;i=g|4;Z=(i<<2)+ca|0;Ca:{if((b|0)<0){g=K[v>>2];if((b|0)!=-1){i=i<<2;h=i+v|0;K[h>>2]=g+K[h>>2];g=i+_|0;K[g>>2]=K[g>>2]+K[_>>2];g=i+aa|0;K[g>>2]=K[g>>2]+K[aa>>2];g=K[ca>>2];break Ca}i=i<<2;h=i+v|0;K[h>>2]=K[h>>2]+(g+K[fb>>2]>>1);g=i+_|0;K[g>>2]=K[g>>2]+(K[eb>>2]+K[_>>2]>>1);g=i+aa|0;K[g>>2]=K[g>>2]+(K[cb>>2]+K[aa>>2]>>1);g=K[bb>>2]+K[ca>>2]>>1;break Ca}g=(((b|0)<(w|0)?g:ka)<<2)+v|0;h=K[g>>2];qa=b+1|0;if((qa|0)>=(w|0)){i=i<<2;M=i+v|0;K[M>>2]=K[M>>2]+(h+K[Rb>>2]>>1);h=i+_|0;K[h>>2]=K[h>>2]+(K[Qb>>2]+K[g+4>>2]>>1);i=i+aa|0;K[i>>2]=K[i>>2]+(K[Pb>>2]+K[g+8>>2]>>1);g=K[Ob>>2]+K[g+12>>2]>>1;break Ca}M=i<<2;i=M+v|0;Ka=i;Oa=K[i>>2];i=(qa<<5)+v|0;K[Ka>>2]=Oa+(h+K[i>>2]>>1);h=M+_|0;K[h>>2]=K[h>>2]+(K[i+4>>2]+K[g+4>>2]>>1);h=M+aa|0;K[h>>2]=K[h>>2]+(K[i+8>>2]+K[g+8>>2]>>1);g=K[i+12>>2]+K[g+12>>2]>>1}K[Z>>2]=g+K[Z>>2];b=b+1|0;if((va|0)!=(b|0)){continue}break}}if(db(la,s,wb,n,Ca,Cb,1,4)){continue}break}break}break aa}Ga(v);g=1}b=K[ta-16>>2];i=K[pb>>2];h=K[qb>>2];l=K[ta-8>>2];Ua(la,b-i|0,K[ta-12>>2]-h|0,l-i|0,K[ta-4>>2]-h|0,K[F+52>>2],1,l-b|0);_a(la);break W}_a(la);Ga(v);g=0;break W}_a(la);g=0;break W}g=0;Xa(j);Ga(n)}ra=ia+32|0;if(g){break V}break b}g=0;m=0;D=ra+-64|0;ra=D;Da:{Ea:{Fa:{if(K[G+64>>2]){h=K[F+28>>2];g=h+Q(K[F+24>>2],152)|0;i=K[g-152>>2];j=1;s=K[G+44>>2];ga=K[s+4>>2];if((o|0)==1){break Da}n=o-1|0;k=n&1;Ga:{if((o|0)==2){l=0;b=h;break Ga}j=n&-2;l=0;b=h;while(1){o=K[b+160>>2]-K[b+152>>2]|0;l=l>>>0>o>>>0?l:o;o=K[b+164>>2]-K[b+156>>2]|0;l=l>>>0>o>>>0?l:o;o=K[b+312>>2]-K[b+304>>2]|0;l=l>>>0>o>>>0?l:o;o=K[b+316>>2]-K[b+308>>2]|0;l=l>>>0>o>>>0?l:o;b=b+304|0;m=m+2|0;if((j|0)!=(m|0)){continue}break}}j=0;if(k){m=K[b+160>>2]-K[b+152>>2]|0;l=l>>>0>m>>>0?l:m;b=K[b+164>>2]-K[b+156>>2]|0;l=b>>>0<l>>>0?l:b}if(l>>>0>134217727){break Da}g=K[g-144>>2];m=K[h+4>>2];o=K[h+12>>2];k=K[h>>2];w=K[h+8>>2];ha=l<<5;b=Ma(ha);K[D+32>>2]=b;if(!b){break Da}r=o-m|0;l=w-k|0;K[D>>2]=b;b=ga>>>1|0;U=b>>>0<=2?2:b;o=g-i|0;ma=o<<5;$=Q(o,28);Z=Q(o,24);ka=Q(o,20);ja=o<<4;v=Q(o,12);_=o<<3;i=K[F+36>>2];while(1){K[D+8>>2]=r;b=l;K[D+40>>2]=b;ba=K[h+156>>2];A=K[h+164>>2];m=K[h+160>>2];g=K[h+152>>2];K[D+56>>2]=0;K[D+52>>2]=b;K[D+48>>2]=0;z=(g|0)%2|0;K[D+44>>2]=z;l=m-g|0;w=l-b|0;K[D+60>>2]=w;K[D+36>>2]=w;y=(ga|0)<2;r=A-ba|0;Ha:{if(!(!y&r>>>0>15)){j=0;g=i;if(r>>>0<8){break Ha}m=0;w=K[D+32>>2];while(1){b=D+32|0;Hb(b,g,o,8);Za(b);b=0;if(l){while(1){j=(b<<2)+g|0;k=w+(b<<5)|0;O[j>>2]=O[k>>2];O[j+(o<<2)>>2]=O[k+4>>2];O[j+_>>2]=O[k+8>>2];O[j+v>>2]=O[k+12>>2];b=b+1|0;if((l|0)!=(b|0)){continue}break}b=0;while(1){j=(b<<2)+g|0;k=w+(b<<5)|0;O[j+ja>>2]=O[k+16>>2];O[j+ka>>2]=O[k+20>>2];O[j+Z>>2]=O[k+24>>2];O[j+$>>2]=O[k+28>>2];b=b+1|0;if((l|0)!=(b|0)){continue}break}}g=g+ma|0;b=m+15|0;j=m+8|0;m=j;if(b>>>0<r>>>0){continue}break}break Ha}g=r>>>3|0;p=g>>>0<ga>>>0?g:ga;u=(r>>>0)/(p>>>0)&-8;j=r&-8;m=0;g=i;while(1){k=Ja(48);if(!k){break Fa}P=Ma(ha);K[k>>2]=P;if(!P){Xa(s);Ga(k);j=0;break Ea}K[k+40>>2]=g;K[k+36>>2]=o;K[k+32>>2]=l;K[k+28>>2]=w;K[k+24>>2]=0;K[k+20>>2]=b;K[k+16>>2]=0;K[k+12>>2]=z;K[k+8>>2]=b;K[k+4>>2]=w;V=j-Q(m,u)|0;m=m+1|0;P=(p|0)==(m|0)?V:u;K[k+44>>2]=P;lb(s,12,k);g=(Q(o,P)<<2)+g|0;if((m|0)!=(p|0)){continue}break}Xa(s)}Ia:{if(j>>>0>=r>>>0){break Ia}m=D+32|0;b=r-j|0;Hb(m,g,o,b);Za(m);if(!l){break Ia}p=b&-4;w=b&3;z=0;u=K[D+32>>2];P=ba+(j-A|0)>>>0>4294967292;while(1){m=(z<<2)+g|0;k=u+(z<<5)|0;b=0;j=0;if(!P){while(1){O[m+(Q(b,o)<<2)>>2]=O[k+(b<<2)>>2];C=b|1;O[m+(Q(C,o)<<2)>>2]=O[k+(C<<2)>>2];C=b|2;O[m+(Q(C,o)<<2)>>2]=O[k+(C<<2)>>2];C=b|3;O[m+(Q(C,o)<<2)>>2]=O[k+(C<<2)>>2];b=b+4|0;j=j+4|0;if((p|0)!=(j|0)){continue}break}}j=0;if(w){while(1){O[m+(Q(b,o)<<2)>>2]=O[k+(b<<2)>>2];b=b+1|0;j=j+1|0;if((w|0)!=(j|0)){continue}break}}z=z+1|0;if((z|0)!=(l|0)){continue}break}}k=K[D+8>>2];w=r-k|0;K[D+4>>2]=w;b=K[h+156>>2];K[D+16>>2]=0;K[D+20>>2]=k;K[D+24>>2]=0;K[D+28>>2]=w;P=(b|0)%2|0;K[D+12>>2]=P;Ja:{if(!(!y&l>>>0>15)){g=i;if(l>>>0<8){break Ja}aa=r&-2;ca=r&1;ia=w&-2;V=w&1;da=k&-2;la=k&1;M=A+(ba^-1)|0;z=K[D>>2];b=P<<5;C=z+b|0;T=(z-b|0)+32|0;na=Q(k,o)<<2;m=l;while(1){b=0;j=0;Ka:{La:{switch(k|0){default:while(1){p=(Q(b,o)<<2)+g|0;y=K[p+4>>2];u=C+(b<<6)|0;K[u>>2]=K[p>>2];K[u+4>>2]=y;y=K[p+28>>2];K[u+24>>2]=K[p+24>>2];K[u+28>>2]=y;y=K[p+20>>2];K[u+16>>2]=K[p+16>>2];K[u+20>>2]=y;y=K[p+12>>2];K[u+8>>2]=K[p+8>>2];K[u+12>>2]=y;u=b|1;p=C+(u<<6)|0;u=(Q(o,u)<<2)+g|0;y=K[u+28>>2];K[p+24>>2]=K[u+24>>2];K[p+28>>2]=y;y=K[u+20>>2];K[p+16>>2]=K[u+16>>2];K[p+20>>2]=y;y=K[u+12>>2];K[p+8>>2]=K[u+8>>2];K[p+12>>2]=y;y=K[u+4>>2];K[p>>2]=K[u>>2];K[p+4>>2]=y;b=b+2|0;j=j+2|0;if((da|0)!=(j|0)){continue}break};break;case 0:break Ka;case 1:break La}}if(!la){break Ka}j=C+(b<<6)|0;b=(Q(b,o)<<2)+g|0;p=K[b+4>>2];K[j>>2]=K[b>>2];K[j+4>>2]=p;p=K[b+28>>2];K[j+24>>2]=K[b+24>>2];K[j+28>>2]=p;p=K[b+20>>2];K[j+16>>2]=K[b+16>>2];K[j+20>>2]=p;p=K[b+12>>2];K[j+8>>2]=K[b+8>>2];K[j+12>>2]=p}Ma:{if((k|0)==(r|0)){break Ma}u=g+na|0;b=0;y=0;if((k|0)!=(M|0)){while(1){j=u+(Q(b,o)<<2)|0;ea=K[j+4>>2];p=T+(b<<6)|0;K[p>>2]=K[j>>2];K[p+4>>2]=ea;ea=K[j+28>>2];K[p+24>>2]=K[j+24>>2];K[p+28>>2]=ea;ea=K[j+20>>2];K[p+16>>2]=K[j+16>>2];K[p+20>>2]=ea;ea=K[j+12>>2];K[p+8>>2]=K[j+8>>2];K[p+12>>2]=ea;p=b|1;j=T+(p<<6)|0;p=u+(Q(o,p)<<2)|0;ea=K[p+28>>2];K[j+24>>2]=K[p+24>>2];K[j+28>>2]=ea;ea=K[p+20>>2];K[j+16>>2]=K[p+16>>2];K[j+20>>2]=ea;ea=K[p+12>>2];K[j+8>>2]=K[p+8>>2];K[j+12>>2]=ea;ea=K[p+4>>2];K[j>>2]=K[p>>2];K[j+4>>2]=ea;b=b+2|0;y=y+2|0;if((ia|0)!=(y|0)){continue}break}}if(!V){break Ma}j=T+(b<<6)|0;b=u+(Q(b,o)<<2)|0;p=K[b+4>>2];K[j>>2]=K[b>>2];K[j+4>>2]=p;p=K[b+28>>2];K[j+24>>2]=K[b+24>>2];K[j+28>>2]=p;p=K[b+20>>2];K[j+16>>2]=K[b+16>>2];K[j+20>>2]=p;p=K[b+12>>2];K[j+8>>2]=K[b+8>>2];K[j+12>>2]=p}Za(D);Na:{if(!r){break Na}b=0;j=0;if(M){while(1){p=z+(b<<5)|0;y=K[p+4>>2];u=(Q(b,o)<<2)+g|0;K[u>>2]=K[p>>2];K[u+4>>2]=y;y=K[p+28>>2];K[u+24>>2]=K[p+24>>2];K[u+28>>2]=y;y=K[p+20>>2];K[u+16>>2]=K[p+16>>2];K[u+20>>2]=y;y=K[p+12>>2];K[u+8>>2]=K[p+8>>2];K[u+12>>2]=y;u=b|1;p=(Q(u,o)<<2)+g|0;u=z+(u<<5)|0;y=K[u+28>>2];K[p+24>>2]=K[u+24>>2];K[p+28>>2]=y;y=K[u+20>>2];K[p+16>>2]=K[u+16>>2];K[p+20>>2]=y;y=K[u+12>>2];K[p+8>>2]=K[u+8>>2];K[p+12>>2]=y;y=K[u+4>>2];K[p>>2]=K[u>>2];K[p+4>>2]=y;b=b+2|0;j=j+2|0;if((aa|0)!=(j|0)){continue}break}}if(!ca){break Na}j=(Q(b,o)<<2)+g|0;b=z+(b<<5)|0;p=K[b+4>>2];K[j>>2]=K[b>>2];K[j+4>>2]=p;p=K[b+28>>2];K[j+24>>2]=K[b+24>>2];K[j+28>>2]=p;p=K[b+20>>2];K[j+16>>2]=K[b+16>>2];K[j+20>>2]=p;p=K[b+12>>2];K[j+8>>2]=K[b+8>>2];K[j+12>>2]=p}g=g+32|0;m=m-8|0;if(m>>>0>7){continue}break}break Ja}b=l>>>3|0;j=b>>>0<U>>>0?b:U;u=j>>>0<=1?1:j;p=(l>>>0)/(j>>>0)&-8;z=l&-8;m=0;g=i;while(1){b=Ja(48);if(!b){break Fa}y=Ma(ha);K[b>>2]=y;if(!y){Xa(s);Ga(b);j=0;break Ea}K[b+40>>2]=g;K[b+36>>2]=o;K[b+32>>2]=r;K[b+28>>2]=w;K[b+24>>2]=0;K[b+20>>2]=k;K[b+16>>2]=0;K[b+12>>2]=P;K[b+8>>2]=k;K[b+4>>2]=w;V=z-Q(m,p)|0;m=m+1|0;y=(j|0)==(m|0)?V:p;K[b+44>>2]=y;lb(s,13,b);g=(y<<2)+g|0;if((m|0)!=(u|0)){continue}break}Xa(s)}u=l&7;Oa:{if(!u){break Oa}y=P<<5;m=K[D>>2];Pa:{if(!k){break Pa}z=m+y|0;p=u<<2;b=0;if((k|0)!=1){P=k&-2;j=0;while(1){C=!p;if(!C){E(z+(b<<6)|0,(Q(b,o)<<2)+g|0,p)}if(!C){C=b|1;E(z+(C<<6)|0,(Q(o,C)<<2)+g|0,p)}b=b+2|0;j=j+2|0;if((P|0)!=(j|0)){continue}break}}if(!(k&1)|!p){break Pa}E(z+(b<<6)|0,(Q(b,o)<<2)+g|0,p)}Qa:{if((k|0)==(r|0)){break Qa}z=(m-y|0)+32|0;y=(Q(k,o)<<2)+g|0;p=u<<2;b=0;if((k|0)!=(A+(ba^-1)|0)){k=w&-2;j=0;while(1){P=!p;if(!P){E(z+(b<<6)|0,y+(Q(b,o)<<2)|0,p)}if(!P){P=b|1;E(z+(P<<6)|0,y+(Q(o,P)<<2)|0,p)}b=b+2|0;j=j+2|0;if((k|0)!=(j|0)){continue}break}}if(!(w&1)|!p){break Qa}E(z+(b<<6)|0,y+(Q(b,o)<<2)|0,p)}Za(D);if(!r){break Oa}k=u<<2;b=0;if((A|0)!=(ba+1|0)){w=r&-2;j=0;while(1){p=!k;if(!p){E((Q(b,o)<<2)+g|0,m+(b<<5)|0,k)}if(!p){p=b|1;E((Q(p,o)<<2)+g|0,m+(p<<5)|0,k)}b=b+2|0;j=j+2|0;if((w|0)!=(j|0)){continue}break}}if(!(r&1)|!k){break Oa}E((Q(b,o)<<2)+g|0,m+(b<<5)|0,k)}h=h+152|0;n=n-1|0;if(n){continue}break}j=1;break Ea}j=1;i=K[F+28>>2];ha=i+Q(o,152)|0;v=ha-152|0;if(K[v>>2]==K[ha-144>>2]){break Da}_=ha-148|0;if(K[_>>2]==K[ha-140>>2]){break Da}h=K[i+4>>2];l=K[i+12>>2];m=K[i>>2];n=K[i+8>>2];u=K[F+68>>2];ba=K[F+64>>2];A=K[F+60>>2];y=K[F+56>>2];T=oc(F,o);if(!T){j=0;break Da}if((o|0)==1){b=K[ha-16>>2];g=K[v>>2];i=K[_>>2];h=K[ha-8>>2];Ua(T,b-g|0,K[ha-12>>2]-i|0,h-g|0,K[ha-4>>2]-i|0,K[F+52>>2],1,h-b|0);_a(T);break Da}b=o-1|0;k=b&1;Ra:{if((o|0)==2){j=0;b=i;break Ra}s=b&-2;j=0;b=i;while(1){r=K[b+160>>2]-K[b+152>>2]|0;j=j>>>0>r>>>0?j:r;r=K[b+164>>2]-K[b+156>>2]|0;j=j>>>0>r>>>0?j:r;r=K[b+312>>2]-K[b+304>>2]|0;j=j>>>0>r>>>0?j:r;r=K[b+316>>2]-K[b+308>>2]|0;j=j>>>0>r>>>0?j:r;b=b+304|0;g=g+2|0;if((s|0)!=(g|0)){continue}break}}if(k){g=K[b+160>>2]-K[b+152>>2]|0;g=g>>>0<j>>>0?j:g;b=K[b+164>>2]-K[b+156>>2]|0;j=b>>>0<g>>>0?g:b}Sa:{if(j>>>0>=134217728){break Sa}U=Ma(j<<5);K[D+32>>2]=U;if(!U){break Sa}K[D>>2]=U;Ta:{if(o){r=l-h|0;b=n-m|0;aa=U+32|0;P=o;ca=K[F+20>>2];w=1;ma=0;while(1){K[D+8>>2]=r;K[D+40>>2]=b;h=K[i+164>>2];l=K[i+160>>2];g=K[i+156>>2];m=K[i+152>>2];M=(m|0)%2|0;K[D+44>>2]=M;ia=(g|0)%2|0;K[D+12>>2]=ia;ga=l-m|0;$=ga-b|0;K[D+36>>2]=$;p=h-g|0;V=p-r|0;K[D+4>>2]=V;n=y;g=n;h=A;l=h;j=ba;z=j;m=u;s=m;Ua:{if(!ma&(w|0)==(ca|0)){break Ua}C=ca-w|0;l=0;g=0;if(n){g=C&31;if((C&63)>>>0>=32){k=-1<<g;h=0}else{h=-1<<g;k=h|(1<<g)-1&-1>>>32-g}g=n+(h^-1)|0;h=k^-1;m=g>>>0<y>>>0?h+1|0:h;h=C&31;if((C&63)>>>0>=32){g=m>>>h|0}else{g=((1<<h)-1&m)<<32-h|g>>>h}}if(A){h=C&31;if((C&63)>>>0>=32){k=-1<<h;l=0}else{l=-1<<h;k=l|(1<<h)-1&-1>>>32-h}h=A+(l^-1)|0;l=k^-1;m=h>>>0<A>>>0?l+1|0:l;l=C&31;if((C&63)>>>0>=32){l=m>>>l|0}else{l=((1<<l)-1&m)<<32-l|h>>>l}}m=0;j=0;if(ba){h=C&31;if((C&63)>>>0>=32){k=-1<<h;j=0}else{j=-1<<h;k=j|(1<<h)-1&-1>>>32-h}h=ba+(j^-1)|0;j=k^-1;n=h>>>0<ba>>>0?j+1|0:j;j=C&31;if((C&63)>>>0>=32){j=n>>>j|0}else{j=((1<<j)-1&n)<<32-j|h>>>j}}if(u){h=C&31;if((C&63)>>>0>=32){k=-1<<h;m=0}else{m=-1<<h;k=m|(1<<h)-1&-1>>>32-h}h=u+(m^-1)|0;m=k^-1;n=h>>>0<u>>>0?m+1|0:m;m=C&31;if((C&63)>>>0>=32){m=n>>>m|0}else{m=((1<<m)-1&n)<<32-m|h>>>m}}z=0;n=0;o=1<<C-1;if(o>>>0<y>>>0){h=C&31;if((C&63)>>>0>=32){k=-1<<h;n=0}else{n=-1<<h;k=n|(1<<h)-1&-1>>>32-h}n=n^-1;h=n+(y-o|0)|0;k=k^-1;k=h>>>0<n>>>0?k+1|0:k;n=C&31;if((C&63)>>>0>=32){n=k>>>n|0}else{n=((1<<n)-1&k)<<32-n|h>>>n}}if(o>>>0<ba>>>0){h=C&31;if((C&63)>>>0>=32){k=-1<<h;s=0}else{s=-1<<h;k=s|(1<<h)-1&-1>>>32-h}s=s^-1;h=s+(ba-o|0)|0;k=k^-1;s=h>>>0<s>>>0?k+1|0:k;k=C&31;if((C&63)>>>0>=32){z=s>>>k|0}else{z=((1<<k)-1&s)<<32-k|h>>>k}}s=0;h=0;if(o>>>0<A>>>0){h=C&31;if((C&63)>>>0>=32){k=-1<<h;Z=0}else{Z=-1<<h;k=Z|(1<<h)-1&-1>>>32-h}Z=Z^-1;h=Z+(A-o|0)|0;k=k^-1;Z=h>>>0<Z>>>0?k+1|0:k;k=C&31;if((C&63)>>>0>=32){h=Z>>>k|0}else{h=((1<<k)-1&Z)<<32-k|h>>>k}}if(o>>>0>=u>>>0){break Ua}k=C&31;if((C&63)>>>0>=32){k=-1<<k;s=0}else{s=-1<<k;k=s|(1<<k)-1&-1>>>32-k}s=s^-1;o=s+(u-o|0)|0;k=k^-1;s=o>>>0<s>>>0?k+1|0:k;k=C&31;if((C&63)>>>0>=32){s=s>>>k|0}else{s=((1<<k)-1&s)<<32-k|o>>>k}}o=K[i+180>>2];k=z-o|0;k=k>>>0<=z>>>0?k:0;z=k+4|0;k=k>>>0>z>>>0?-1:z;ka=k>>>0<$>>>0?k:$;k=K[i+216>>2];z=j-k|0;j=j>>>0>=z>>>0?z:0;z=j+4|0;j=j>>>0>z>>>0?-1:z;ja=b>>>0>j>>>0?j:b;j=(M?ka:ja)<<1;z=(M?ja:ka)<<1|1;z=j>>>0>z>>>0?j:z;C=z>>>0<ga>>>0;j=n-o|0;j=j>>>0<=n>>>0?j:0;n=j-4|0;$=j>>>0>=n>>>0?n:0;j=g-k|0;g=g>>>0>=j>>>0?j:0;j=g-4|0;Z=g>>>0>=j>>>0?j:0;da=(M?$:Z)<<1;la=(M?Z:$)<<1|1;na=da>>>0<la>>>0;o=K[i+184>>2];g=l-o|0;g=g>>>0<=l>>>0?g:0;l=g-4|0;k=g>>>0>=l>>>0?l:0;j=k;l=K[i+220>>2];g=h-l|0;g=g>>>0<=h>>>0?g:0;h=g-4|0;g=g>>>0>=h>>>0?h:0;n=g;h=m-o|0;h=h>>>0<=m>>>0?h:0;m=h+4|0;h=h>>>0>m>>>0?-1:m;h=h>>>0<r>>>0?h:r;o=h;l=s-l|0;l=l>>>0<=s>>>0?l:0;m=l+4|0;l=l>>>0>m>>>0?-1:m;m=l>>>0<V>>>0?l:V;s=m;if(ia){n=j;o=m;s=h;j=g}C=C?z:ga;z=na?da:la;K[D+60>>2]=ka;K[D+56>>2]=$;K[D+52>>2]=ja;K[D+48>>2]=Z;Va:{if(p>>>0<8){b=7;l=0;break Va}l=M<<5;V=(aa-l|0)+($<<6)|0;da=(l+U|0)+(Z<<6)|0;ka=b+ka|0;la=b+$|0;na=m+r|0;ea=g+r|0;pa=U+(z<<5)|0;l=0;while(1){b=l|7;Wa:{if(!(h>>>0>l>>>0&b>>>0>=k>>>0|l>>>0<na>>>0&b>>>0>=ea>>>0)){l=l+8|0;break Wa}b=p-l|0;va=b>>>0>=8?8:b;b=0;while(1){M=b+l|0;$=M+1|0;ta=b<<2;Ua(T,Z,M,ja,$,ta+da|0,16,0);Ua(T,la,M,ka,$,V+ta|0,16,0);b=b+1|0;if((va|0)!=(b|0)){continue}break}Za(D+32|0);b=l;l=l+8|0;if(!db(T,z,b,C,l,pa,8,1)){break Ta}}b=l|7;if(p>>>0>b>>>0){continue}break}}if(!(!(h>>>0>l>>>0&b>>>0>=k>>>0)&(m+r>>>0<=l>>>0|g+r>>>0>b>>>0)|l>>>0>=p>>>0)){b=D+32|0;M=0;ka=p-l|0;if(ka){while(1){$=l+M|0;Z=$+1|0;ja=K[b+16>>2];V=M<<2;Ua(T,ja,$,K[b+20>>2],Z,V+((K[b>>2]+(K[b+12>>2]<<5)|0)+(ja<<6)|0)|0,16,0);ja=K[b+24>>2];da=K[b+8>>2];Ua(T,ja+da|0,$,da+K[b+28>>2]|0,Z,(V+((K[b>>2]-(K[b+12>>2]<<5)|0)+(ja<<6)|0)|0)+32|0,16,0);M=M+1|0;if((ka|0)!=(M|0)){continue}break}}Za(b);if(!db(T,z,l,C,p,U+(z<<5)|0,8,1)){break Ta}}K[D+28>>2]=m;K[D+24>>2]=g;K[D+20>>2]=h;K[D+16>>2]=k;if(C>>>0>z>>>0){b=o<<1;l=s<<1|1;b=b>>>0>l>>>0?b:l;l=b>>>0<p>>>0?b:p;b=ia<<5;o=(aa-b|0)+(g<<6)|0;s=(b+U|0)+(k<<6)|0;m=m+r|0;g=g+r|0;b=j<<1;j=n<<1|1;j=b>>>0<j>>>0?b:j;n=U+(j<<5)|0;while(1){b=C-z|0;b=(b>>>0>=8?8:b)+z|0;Ua(T,z,k,b,h,s,1,16);Ua(T,z,g,b,m,o,1,16);Za(D);if(!db(T,z,j,b,l,n,1,8)){break Ta}z=z+8|0;if(C>>>0>z>>>0){continue}break}}i=i+152|0;b=ga;r=p;w=w+1|0;ma=w?ma:ma+1|0;if(ma|(w|0)!=(P|0)){continue}break}}j=1;b=K[ha-16>>2];g=K[v>>2];i=K[_>>2];h=K[ha-8>>2];Ua(T,b-g|0,K[ha-12>>2]-i|0,h-g|0,K[ha-4>>2]-i|0,K[F+52>>2],1,h-b|0);_a(T);Ga(U);break Da}_a(T);Ga(U);j=0;break Da}_a(T);j=0;break Da}Xa(s);j=0}Ga(K[D+32>>2])}ra=D- -64|0;if(j){break V}break b}t=t+1080|0;H=H+52|0;F=F+76|0;q=q+1|0;if(q>>>0<N[x+16>>2]){continue}break}x=K[K[G+20>>2]>>2];t=K[G+32>>2]}m=K[t+16>>2];Xa:{if(K[G+68>>2]|!m){break Xa}H=K[x+20>>2];g=K[H+28>>2];Ya:{Za:{i=K[G+64>>2];if(i){q=K[x+16>>2];if(q>>>0<3){break Ya}b=K[H+24>>2];if(!((b|0)==K[H+100>>2]&(b|0)==K[H+176>>2])){Fa(f,1,10052,0);break b}h=K[K[G+24>>2]+24>>2];l=K[h+36>>2];_a:{if((l|0)!=K[h+88>>2]|(l|0)!=K[h+140>>2]){break _a}h=Q(b,152);b=h+g|0;b=Q(K[b-140>>2]-K[b-148>>2]|0,K[b-144>>2]-K[b-152>>2]|0);g=h+K[H+104>>2]|0;if((b|0)!=(Q(K[g-140>>2]-K[g-148>>2]|0,K[g-144>>2]-K[g-152>>2]|0)|0)){break _a}g=h+K[H+180>>2]|0;if((Q(K[g-140>>2]-K[g-148>>2]|0,K[g-144>>2]-K[g-152>>2]|0)|0)==(b|0)){break Za}}Fa(f,1,10052,0);break b}q=K[x+16>>2];if(q>>>0<3){break Ya}h=K[K[G+24>>2]+24>>2];b=K[h+36>>2];$a:{if((b|0)!=K[h+88>>2]){break $a}h=K[h+140>>2];if((h|0)!=(b|0)){break $a}l=Q(b,152);b=g+l|0;b=Q(K[b+148>>2]-K[b+140>>2]|0,K[b+144>>2]-K[b+136>>2]|0);g=l+K[H+104>>2]|0;if((b|0)!=(Q(K[g+148>>2]-K[g+140>>2]|0,K[g+144>>2]-K[g+136>>2]|0)|0)){break $a}g=K[H+180>>2]+Q(h,152)|0;if((Q(K[g+148>>2]-K[g+140>>2]|0,K[g+144>>2]-K[g+136>>2]|0)|0)==(b|0)){break Za}}Fa(f,1,10052,0);break b}if((m|0)==2){if(!K[t+5608>>2]){break Xa}o=Ja(q<<2);if(!o){break b}q=K[x+16>>2];ab:{if(!q){break ab}bb:{cb:{if(K[G+64>>2]){l=q&3;g=0;if(q>>>0>=4){break cb}F=0;break bb}l=q&3;g=0;db:{if(q>>>0<4){F=0;break db}m=q&-4;F=0;h=0;while(1){i=o+(F<<2)|0;K[i>>2]=K[H+52>>2];K[i+4>>2]=K[H+128>>2];K[i+8>>2]=K[H+204>>2];K[i+12>>2]=K[H+280>>2];F=F+4|0;H=H+304|0;h=h+4|0;if((m|0)!=(h|0)){continue}break}}if(!l){break ab}while(1){K[o+(F<<2)>>2]=K[H+52>>2];F=F+1|0;H=H+76|0;g=g+1|0;if((l|0)!=(g|0)){continue}break}break ab}m=q&-4;F=0;h=0;while(1){i=o+(F<<2)|0;K[i>>2]=K[H+36>>2];K[i+4>>2]=K[H+112>>2];K[i+8>>2]=K[H+188>>2];K[i+12>>2]=K[H+264>>2];F=F+4|0;H=H+304|0;h=h+4|0;if((m|0)!=(h|0)){continue}break}}if(!l){break ab}while(1){K[o+(F<<2)>>2]=K[H+36>>2];F=F+1|0;H=H+76|0;g=g+1|0;if((l|0)!=(g|0)){continue}break}}m=K[t+5608>>2];j=0;t=Ja(q<<3);g=0;eb:{if(!t){break eb}if(!(!b|!q)){w=t+(q<<2)|0;s=q&-4;k=q&3;r=q-1|0;while(1){x=0;i=0;if(r>>>0>=3){while(1){g=x<<2;O[g+t>>2]=O[K[g+o>>2]>>2];h=g|4;O[h+t>>2]=O[K[h+o>>2]>>2];h=g|8;O[h+t>>2]=O[K[h+o>>2]>>2];g=g|12;O[g+t>>2]=O[K[g+o>>2]>>2];x=x+4|0;i=i+4|0;if((s|0)!=(i|0)){continue}break}}g=0;if(k){while(1){i=x<<2;O[i+t>>2]=O[K[i+o>>2]>>2];x=x+1|0;g=g+1|0;if((k|0)!=(g|0)){continue}break}}h=0;x=m;while(1){p=h<<2;i=p+w|0;K[i>>2]=0;fa=R(0);g=0;n=0;if(r>>>0>2){while(1){l=t+(g<<2)|0;fa=R(R(O[x>>2]*O[l>>2])+fa);O[i>>2]=fa;fa=R(R(O[x+4>>2]*O[l+4>>2])+fa);O[i>>2]=fa;fa=R(R(O[x+8>>2]*O[l+8>>2])+fa);O[i>>2]=fa;fa=R(R(O[x+12>>2]*O[l+12>>2])+fa);O[i>>2]=fa;g=g+4|0;x=x+16|0;n=n+4|0;if((s|0)!=(n|0)){continue}break}}l=0;if(k){while(1){fa=R(R(O[x>>2]*O[t+(g<<2)>>2])+fa);O[i>>2]=fa;g=g+1|0;x=x+4|0;l=l+1|0;if((k|0)!=(l|0)){continue}break}}i=o+p|0;g=K[i>>2];K[i>>2]=g+4;O[g>>2]=fa;h=h+1|0;if((q|0)!=(h|0)){continue}break}j=j+1|0;if((j|0)!=(b|0)){continue}break}}Ga(t);g=1}b=g;Ga(o);if(b){break Xa}break b}if(K[K[t+5584>>2]+20>>2]==1){if(i){sc(K[H+36>>2],K[H+112>>2],K[H+188>>2],b);break Xa}sc(K[H+52>>2],K[H+128>>2],K[H+204>>2],b);break Xa}if(i){rc(K[H+36>>2],K[H+112>>2],K[H+188>>2],b);break Xa}rc(K[H+52>>2],K[H+128>>2],K[H+204>>2],b);break Xa}K[sa>>2]=q;Fa(f,1,10113,sa)}k=K[K[G+20>>2]>>2];if(!K[k+16>>2]){Y=1;break b}s=K[G+68>>2];l=K[k+20>>2];b=K[K[G+32>>2]+5584>>2];m=K[K[G+24>>2]+24>>2];i=0;while(1){fb:{if(K[s+(i<<2)>>2]?0:s){break fb}h=K[l+28>>2];g=h+Q(K[m+36>>2],152)|0;gb:{if(!K[G+64>>2]){h=K[g+148>>2]-K[g+140>>2]|0;x=K[g+144>>2]-K[g+136>>2]|0;j=0;q=52;break gb}h=h+Q(K[l+24>>2],152)|0;x=K[g+8>>2]-K[g>>2]|0;j=K[h-144>>2]-(x+K[h-152>>2]|0)|0;h=K[g+12>>2]-K[g+4>>2]|0;q=36}g=K[m+24>>2];hb:{if(K[m+32>>2]){g=1<<g-1;F=g-1|0;n=0-g|0;break hb}F=-1<<g^-1;n=0}if(!x|!h){break fb}Y=K[l+q>>2];if(K[b+20>>2]==1){t=x&-2;o=x&1;H=0;j=j<<2;while(1){q=0;if((x|0)!=1){while(1){g=K[b+1076>>2]+K[Y>>2]|0;K[Y>>2]=(g|0)<(n|0)?n:(g|0)<(F|0)?g:F;g=K[b+1076>>2]+K[Y+4>>2]|0;K[Y+4>>2]=(g|0)<(n|0)?n:(g|0)<(F|0)?g:F;Y=Y+8|0;q=q+2|0;if((t|0)!=(q|0)){continue}break}}if(o){g=K[b+1076>>2]+K[Y>>2]|0;K[Y>>2]=(g|0)<(n|0)?n:(g|0)<(F|0)?g:F;Y=Y+4|0}Y=Y+j|0;H=H+1|0;if((H|0)!=(h|0)){continue}break}break fb}r=n>>31;g=0;while(1){q=0;while(1){fa=O[Y>>2];o=F;ib:{if(fa>R(2147483648)){break ib}o=n;if(fa<R(-2147483648)){break ib}t=K[b+1076>>2];o=t;ya=fa;fa=R(W(fa));Ya=R(ya-fa);if(!(Ya<R(.5))){ya=R(X(ya));jb:{if(Ya>R(.5)){break jb}Ya=fa;fa=R(fa*R(.5));ya=R(fa-R(W(fa)))==R(0)?Ya:ya}fa=ya}p=t>>31;if(R(S(fa))<R(2147483648)){t=~~fa}else{t=-2147483648}w=p+(t>>31)|0;o=o+t|0;t=t>>>0>o>>>0?w+1|0:w;o=n>>>0>o>>>0&(r|0)>=(t|0)|(r|0)>(t|0)?n:o>>>0<F>>>0&(t|0)<=0|(t|0)<0?o:F}K[Y>>2]=o;Y=Y+4|0;q=q+1|0;if((x|0)!=(q|0)){continue}break}Y=(j<<2)+Y|0;g=g+1|0;if((h|0)!=(g|0)){continue}break}}l=l+76|0;b=b+1080|0;m=m+52|0;Y=1;i=i+1|0;if(i>>>0<N[k+16>>2]){continue}break}break b}Y=0;Fa(f,1,3335,0)}ra=sa+16|0;if(!Y){nb(xa);K[a+8>>2]=K[a+8>>2]|32768;Fa(f,1,11414,0);break a}kb:{if(!c){break kb}b=0;g=K[a+232>>2];i=fc(g,1);if(!((i|0)==-1|d>>>0<i>>>0)){lb:{b=1;d=K[g+24>>2];if(!K[d+16>>2]){break lb}t=K[d+24>>2];q=K[K[K[g+20>>2]>>2]+20>>2];while(1){b=K[t+24>>2];i=b&7;h=b>>>3|0;d=K[q+28>>2];b=d+Q(K[t+36>>2],152)|0;mb:{if(K[g+64>>2]){l=d+Q(K[q+24>>2],152)|0;d=K[b+8>>2]-K[b>>2]|0;j=K[l-144>>2]-(d+K[l-152>>2]|0)|0;n=K[b+12>>2]-K[b+4>>2]|0;b=36;break mb}n=K[b+148>>2]-K[b+140>>2]|0;d=K[b+144>>2]-K[b+136>>2]|0;j=0;b=52}b=K[b+q>>2];nb:{ob:{pb:{qb:{i=h+((i|0)!=0)|0;switch(((i|0)==3?4:i)-1|0){case 0:break pb;case 1:break ob;case 3:break qb;default:break nb}}if(!n){break nb}d=d<<2;if((n|0)!=1){i=n&-2;o=0;while(1){h=!d;if(!h){E(c,b,d)}l=j<<2;b=l+(b+d|0)|0;c=c+d|0;if(!h){E(c,b,d)}c=c+d|0;b=l+(b+d|0)|0;o=o+2|0;if((i|0)!=(o|0)){continue}break}}if(!(n&1)){break nb}if(d){E(c,b,d)}c=c+d|0;break nb}i=!n|!d;if(K[t+32>>2]){if(i){break nb}h=d&-8;i=d&7;o=0;l=d-1>>>0<7;while(1){d=0;if(!l){while(1){I[c|0]=K[b>>2];I[c+1|0]=K[b+4>>2];I[c+2|0]=K[b+8>>2];I[c+3|0]=K[b+12>>2];I[c+4|0]=K[b+16>>2];I[c+5|0]=K[b+20>>2];I[c+6|0]=K[b+24>>2];I[c+7|0]=K[b+28>>2];c=c+8|0;b=b+32|0;d=d+8|0;if((h|0)!=(d|0)){continue}break}}d=0;if(i){while(1){I[c|0]=K[b>>2];c=c+1|0;b=b+4|0;d=d+1|0;if((i|0)!=(d|0)){continue}break}}b=(j<<2)+b|0;o=o+1|0;if((o|0)!=(n|0)){continue}break}break nb}if(i){break nb}h=d&-8;i=d&7;o=0;l=d-1>>>0<7;m=j<<2;while(1){d=0;if(!l){while(1){I[c|0]=K[b>>2];I[c+1|0]=K[b+4>>2];I[c+2|0]=K[b+8>>2];I[c+3|0]=K[b+12>>2];I[c+4|0]=K[b+16>>2];I[c+5|0]=K[b+20>>2];I[c+6|0]=K[b+24>>2];I[c+7|0]=K[b+28>>2];c=c+8|0;b=b+32|0;d=d+8|0;if((h|0)!=(d|0)){continue}break}}d=0;if(i){while(1){I[c|0]=K[b>>2];c=c+1|0;b=b+4|0;d=d+1|0;if((i|0)!=(d|0)){continue}break}}b=b+m|0;o=o+1|0;if((o|0)!=(n|0)){continue}break}break nb}i=!n|!d;if(K[t+32>>2]){if(i){break nb}h=d&-8;i=d&7;o=0;l=d-1>>>0<7;while(1){d=0;if(!l){while(1){J[c>>1]=K[b>>2];J[c+2>>1]=K[b+4>>2];J[c+4>>1]=K[b+8>>2];J[c+6>>1]=K[b+12>>2];J[c+8>>1]=K[b+16>>2];J[c+10>>1]=K[b+20>>2];J[c+12>>1]=K[b+24>>2];J[c+14>>1]=K[b+28>>2];c=c+16|0;b=b+32|0;d=d+8|0;if((h|0)!=(d|0)){continue}break}}d=0;if(i){while(1){J[c>>1]=K[b>>2];c=c+2|0;b=b+4|0;d=d+1|0;if((i|0)!=(d|0)){continue}break}}b=(j<<2)+b|0;o=o+1|0;if((o|0)!=(n|0)){continue}break}break nb}if(i){break nb}h=d&-8;i=d&7;o=0;l=d-1>>>0<7;while(1){d=0;if(!l){while(1){J[c>>1]=K[b>>2];J[c+2>>1]=K[b+4>>2];J[c+4>>1]=K[b+8>>2];J[c+6>>1]=K[b+12>>2];J[c+8>>1]=K[b+16>>2];J[c+10>>1]=K[b+20>>2];J[c+12>>1]=K[b+24>>2];J[c+14>>1]=K[b+28>>2];c=c+16|0;b=b+32|0;d=d+8|0;if((h|0)!=(d|0)){continue}break}}d=0;if(i){while(1){J[c>>1]=K[b>>2];c=c+2|0;b=b+4|0;d=d+1|0;if((i|0)!=(d|0)){continue}break}}b=(j<<2)+b|0;o=o+1|0;if((o|0)!=(n|0)){continue}break}}q=q+76|0;t=t+52|0;b=1;yb=yb+1|0;if(yb>>>0<N[K[g+24>>2]+16>>2]){continue}break}}}if(!b){break a}b=K[xa+5596>>2];if(!b){break kb}Ga(b);K[xa+5596>>2]=0;K[xa+5600>>2]=0}I[a+92|0]=L[a+92|0]&254;K[a+8>>2]=K[a+8>>2]&-129;ob=1;c=Va(e);b=K[a+8>>2];if(!(c|ua)&(b|0)==64|(b|0)==256){break a}if((Na(e,za+10|0,2,f)|0)!=2){Fa(f,K[a+208>>2]?1:2,2435,0);ob=!K[a+208>>2];break a}Ha(za+10|0,za+12|0,2);b=K[za+12>>2];if((b|0)==65424){break a}if((b|0)==65497){K[a+8>>2]=256;K[a+228>>2]=0;break a}if(!(Va(e)|ua)){K[a+8>>2]=64;Fa(f,2,8382,0);break a}ob=0;Fa(f,1,8269,0)}ra=za+16|0;return ob|0}function ab(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;var l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,y=0,A=0,C=0,D=0,F=0,G=0,H=0,M=0,P=0,S=0,T=0,U=0,V=0,W=0,X=R(0),Y=0,Z=0,_=0,$=0,aa=0,ba=0,ca=0,da=0,ea=0,fa=0,ga=0,ha=0,ia=0,ja=0,ka=0,la=0,ma=0,na=0,oa=0,pa=0,qa=0,sa=0,ta=0,wa=0,xa=R(0);s=ra-80|0;ra=s;K[s+40>>2]=65424;w=Q(K[a+132>>2],K[a+128>>2]);a:{b:{c:{l=K[a+8>>2];d:{if((l|0)!=8){j=0;if((l|0)!=256){break a}K[s+40>>2]=65497;break d}if(I[a+92|0]&1){break d}A=w&-2;D=w&1;P=s+77|0;S=s+76|0;T=s+72|0;n=65424;e:{f:{while(1){g:{h:{i:{j:{k:{l:{m:{n:{l=K[a+84>>2];if(!l){break n}p=l;l=K[a+80>>2];if(p>>>0<=l>>>0){break n}o=K[a+88>>2]+(l<<3)|0;n=K[o>>2];o=K[o+4>>2];K[a+80>>2]=l+1;if(!ib(j,n,o,k)){Fa(k,1,5403,0);j=0;break a}if((Na(j,K[a+16>>2],2,k)|0)!=2){Fa(k,1,2435,0);j=0;break a}Ha(K[a+16>>2],s+40|0,2);if(K[s+40>>2]==65424){break m}Fa(k,1,4036,0);j=0;break a}if((n|0)==65427){break l}}while(1){if(!(Va(j)|ua)){K[a+8>>2]=64;break l}if((Na(j,K[a+16>>2],2,k)|0)!=2){Fa(k,1,2435,0);j=0;break a}Ha(K[a+16>>2],s+36|0,2);if(N[s+36>>2]<=1){Fa(k,1,6011,0);j=0;break a}o:{if(K[s+40>>2]!=32896){break o}if(Va(j)|ua){break o}K[a+8>>2]=64;break l}r=K[a+8>>2];p:{if(!(r&16)){n=K[s+36>>2];break p}n=K[s+36>>2];l=K[a+24>>2];if(!l){break p}o=n+2|0;if(o>>>0>l>>>0){Fa(k,1,8333,0);j=0;break a}K[a+24>>2]=l-o}o=n-2|0;K[s+36>>2]=o;l=24864;t=K[s+40>>2];while(1){n=l;m=K[l>>2];if(m){l=l+12|0;if((m|0)!=(t|0)){continue}}break}if(!(r&K[n+4>>2])){Fa(k,1,5360,0);j=0;break a}q:{if(N[a+20>>2]>=o>>>0){l=K[a+16>>2];break q}l=Va(j);r=ua;if((r|0)<0){l=1}else{l=l>>>0<o>>>0&(r|0)<=0}if(l){Fa(k,1,5760,0);j=0;break a}l=La(K[a+16>>2],K[s+36>>2]);if(!l){Ga(K[a+16>>2]);K[a+16>>2]=0;K[a+20>>2]=0;Fa(k,1,4936,0);j=0;break a}K[a+16>>2]=l;o=K[s+36>>2];K[a+20>>2]=o}l=Na(j,l,o,k);if((l|0)!=K[s+36>>2]){Fa(k,1,2435,0);j=0;break a}o=K[n+8>>2];if(!o){Fa(k,1,11688,0);j=0;break a}if(!(va[o|0](a,K[a+16>>2],l,k)|0)){K[s+32>>2]=K[s+40>>2];Fa(k,1,13922,s+32|0);j=0;break a}n=K[j+56>>2];t=K[s+36>>2];y=K[a+224>>2];o=K[y+40>>2];p=K[a+228>>2];v=Q(p,40);l=o+v|0;G=K[l+20>>2];r=G+1|0;q=K[l+28>>2];if(r>>>0>q>>>0){X=R(R(q>>>0)+R(100));if(X<R(4294967296)&X>=R(0)){o=~~X>>>0}else{o=0}K[l+28>>2]=o;r=La(K[l+24>>2],Q(o,24));o=K[y+40>>2];l=v+o|0;if(!r){break k}K[l+24>>2]=r;G=K[l+20>>2];r=G+1|0}o=o+v|0;l=K[o+24>>2]+Q(G,24)|0;K[l+16>>2]=t+4;n=(n-t|0)-4|0;K[l+8>>2]=n;K[l+12>>2]=n>>31;J[l>>1]=m;K[o+20>>2]=r;r:{if((m|0)!=65424){break r}l=K[o+16>>2];s:{if(!l){break s}p=K[o+4>>2];o=K[o+12>>2];if(p>>>0<=o>>>0){break s}l=l+Q(o,24)|0;K[l>>2]=n;K[l+4>>2]=0}l=(K[j+56>>2]-K[s+36>>2]|0)-4|0;o=K[a+48>>2];n=K[a+52>>2];if((n|0)>0){p=1}else{p=l>>>0<=o>>>0&(n|0)>=0}if(p){break r}K[a+48>>2]=l;K[a+52>>2]=0}if(L[a+92|0]&4){if((vb(j,K[a+24>>2],k)|0)!=K[a+24>>2]|ua){Fa(k,1,2435,0);j=0;break a}K[s+40>>2]=65427;break l}if((Na(j,K[a+16>>2],2,k)|0)!=2){Fa(k,1,2435,0);j=0;break a}Ha(K[a+16>>2],s+40|0,2);if(K[s+40>>2]!=65427){continue}break}}if(!(!(Va(j)|ua)&K[a+8>>2]==64)){l=L[a+92|0];if(!(l&4)){l=Q(K[a+228>>2],5644);o=K[a+180>>2];t:{u:{if(K[a+56>>2]){m=Va(j);break u}m=K[a+24>>2];if(m>>>0<2){break t}}m=m-2|0;K[a+24>>2]=m}y=l+o|0;if(!m){break j}l=Va(j);o=ua;if((o|0)<0){l=1}else{l=l>>>0<m>>>0&(o|0)<=0}if(l){if(K[a+208>>2]){Fa(k,1,5805,0);j=0;break a}Fa(k,2,5805,0)}l=K[a+24>>2];if(l>>>0>=4294967294){Fa(k,1,1443,0);j=0;break a}o=K[y+5596>>2];v:{if(o){n=K[y+5600>>2];if(n>>>0>-3-l>>>0){Fa(k,1,1174,0);j=0;break a}l=La(o,(l+n|0)+2|0);if(l){K[y+5596>>2]=l;break j}Ga(K[y+5596>>2]);K[y+5596>>2]=0;break v}l=Ja(l+2|0);K[y+5596>>2]=l;if(l){break j}}Fa(k,1,6139,0);j=0;break a}K[a+8>>2]=8;I[a+92|0]=l&250;break i}n=K[s+40>>2];break g}Ga(K[l+24>>2]);a=K[y+40>>2]+Q(p,40)|0;K[a+28>>2]=0;K[a+20>>2]=0;K[a+24>>2]=0;Fa(k,1,3826,0);j=0;break a}v=K[j+56>>2];n=v-2|0;t=K[j+60>>2];r=t-(v>>>0<2)|0;p=K[a+224>>2];H=K[p+40>>2];C=K[a+228>>2];q=Q(C,40);o=H+q|0;l=K[o+16>>2]+Q(K[o+12>>2],24)|0;K[l+8>>2]=n;K[l+12>>2]=r;r=l;l=t;u=K[a+24>>2];v=u+v|0;K[r+16>>2]=v;K[r+20>>2]=u>>>0>v>>>0?l+1|0:l;t=K[a+24>>2];G=K[o+20>>2];r=G+1|0;l=K[o+28>>2];w:{if(r>>>0<=l>>>0){l=K[o+24>>2];break w}X=R(R(l>>>0)+R(100));if(X<R(4294967296)&X>=R(0)){l=~~X>>>0}else{l=0}K[o+28>>2]=l;l=La(K[o+24>>2],Q(l,24));H=K[p+40>>2];o=q+H|0;if(!l){break f}K[o+24>>2]=l;G=K[o+20>>2];r=G+1|0}l=Q(G,24)+l|0;K[l+16>>2]=t+2;K[l+8>>2]=n;K[l+12>>2]=n>>31;J[l>>1]=65427;K[(q+H|0)+20>>2]=r;x:{if(m){m=Na(j,K[y+5596>>2]+K[y+5600>>2]|0,K[a+24>>2],k);l=8;if((m|0)==K[a+24>>2]){break x}l=64;if((m|0)!=-1){break x}Fa(k,1,2435,0);j=0;break a}m=0;l=K[a+24>>2]?64:8}K[a+8>>2]=l;K[y+5600>>2]=K[y+5600>>2]+m;y:{if(I[a+92|0]&1){break y}l=K[a+44>>2];if(K[a+76>>2]|((l|0)<0|(l|0)!=K[a+228>>2])){break y}if(!Ib(j)){break y}o=K[a+228>>2];n=K[a+180>>2]+Q(o,5644)|0;l=K[n+5592>>2];o=K[K[a+224>>2]+40>>2]+Q(o,40)|0;if((l|0)!=K[o+4>>2]){break y}p=l;l=K[n+5588>>2]+1|0;if(p>>>0<=l>>>0){break y}z:{o=K[o+16>>2]+Q(l,24)|0;l=K[o>>2];o=K[o+4>>2];if((l|0)==K[j+56>>2]&(o|0)==K[j+60>>2]){break z}if(ib(j,l,o,k)){break z}Fa(k,1,5403,0);j=0;break a}if((Na(j,K[a+16>>2],2,k)|0)!=2){Fa(k,1,2435,0);j=0;break a}Ha(K[a+16>>2],s+40|0,2);if(K[s+40>>2]==65424){break h}Fa(k,1,4036,0);j=0;break a}l=L[a+92|0];if((l&9)!=1){break i}I[a+92|0]=l|8;r=K[a+228>>2];if(K[(K[a+180>>2]+Q(r,5644)|0)+5592>>2]==1){break i}if(!Ib(j)){break i}n=K[j+60>>2];t=n;o=K[j+56>>2];if((n&o)==-1){break i}A:{while(1){l=1;n=s+70|0;if((Na(j,n,2,k)|0)!=2){break A}Ha(n,s- -64|0,2);if(K[s+64>>2]!=65424){break A}m=2435;if((Na(j,n,2,k)|0)!=2){break c}Ha(n,s+60|0,2);if(K[s+60>>2]!=10){m=6011;break c}K[s+60>>2]=8;n=Na(j,s+70|0,8,k);if((n|0)!=K[s+60>>2]){break c}if((n|0)!=8){m=4010;break c}Ha(s+70|0,s+56|0,2);Ha(T,s+52|0,4);Ha(S,s+48|0,1);Ha(P,s+44|0,1);if((r|0)!=K[s+56>>2]){n=K[s+52>>2];if(n>>>0<14){break A}n=n-12|0;K[s+52>>2]=n;n=vb(j,n,k);if(!ua&K[s+52>>2]==(n|0)){continue}break A}break}l=K[s+48>>2]!=K[s+44>>2]}if(!Dc(j,o,t,k)){break b}if(l){break i}I[a+92|0]=L[a+92|0]&238|16;B:{if(!w){break B}o=K[a+180>>2];n=0;l=0;if((w|0)!=1){while(1){m=o+Q(n,5644)|0;r=K[m+5592>>2];if(r){K[m+5592>>2]=r+1}m=o+Q(n|1,5644)|0;r=K[m+5592>>2];if(r){K[m+5592>>2]=r+1}n=n+2|0;l=l+2|0;if((A|0)!=(l|0)){continue}break}}if(!D){break B}l=o+Q(n,5644)|0;o=K[l+5592>>2];if(!o){break B}K[l+5592>>2]=o+1}Fa(k,2,8998,0)}if(I[a+92|0]&1){break h}if((Na(j,K[a+16>>2],2,k)|0)!=2){if(!(!w|(w|0)!=(K[a+228>>2]+1|0))){j=K[a+180>>2];n=0;while(1){l=j+Q(n,5644)|0;if(!(K[l+5588>>2]|K[l+5592>>2])){break e}n=n+1|0;if((w|0)!=(n|0)){continue}break}}Fa(k,1,2435,0);j=0;break a}Ha(K[a+16>>2],s+40|0,2)}n=K[s+40>>2];if(I[a+92|0]&1){break g}if((n|0)!=65497){continue}}break}if(K[a+8>>2]==256|(n|0)!=65497){break d}K[a+8>>2]=256;K[a+228>>2]=0;break d}Ga(K[o+24>>2]);a=K[p+40>>2]+Q(C,40)|0;K[a+28>>2]=0;K[a+20>>2]=0;K[a+24>>2]=0;Fa(k,1,3826,0);j=0;break a}K[s+16>>2]=n;Fa(k,4,10967,s+16|0);K[a+228>>2]=n;K[s+40>>2]=65497;K[a+8>>2]=256}n=K[a+228>>2];j=K[a+180>>2];C:{D:{if(I[a+92|0]&1){break D}E:{F:{if(n>>>0>=w>>>0){break F}m=j+Q(n,5644)|0;while(1){if(K[m+5596>>2]){break F}n=n+1|0;K[a+228>>2]=n;m=m+5644|0;if((n|0)!=(w|0)){continue}break}break E}if((n|0)!=(w|0)){break D}}K[i>>2]=0;break C}G:{H:{l=j+Q(n,5644)|0;if(K[l+5172>>2]){a=6800}else{if(!(L[l+5640|0]&2)){break G}r=K[l+5160>>2];I:{if(!r){m=0;break I}w=K[l+5164>>2];j=0;m=0;n=0;if(r>>>0>=4){y=r&-4;o=0;while(1){t=w+(n<<3)|0;m=K[t+28>>2]+(K[t+20>>2]+(K[t+12>>2]+(K[t+4>>2]+m|0)|0)|0)|0;n=n+4|0;o=o+4|0;if((y|0)!=(o|0)){continue}break}}o=r&3;if(!o){break I}while(1){m=K[(w+(n<<3)|0)+4>>2]+m|0;n=n+1|0;j=j+1|0;if((o|0)!=(j|0)){continue}break}}j=Ja(m);K[l+5172>>2]=j;if(j){break H}a=3972}Fa(k,1,a,0);Fa(k,1,8022,0);j=0;break a}K[l+5180>>2]=m;m=K[l+5164>>2];j=K[l+5160>>2];if(j){o=0;n=0;while(1){r=n<<3;t=r+m|0;w=K[t>>2];if(w){j=K[t+4>>2];if(j){E(K[l+5172>>2]+o|0,w,j)}j=r+K[l+5164>>2]|0;t=K[j+4>>2];Ga(K[j>>2]);m=K[l+5164>>2];j=r+m|0;K[j>>2]=0;K[j+4>>2]=0;o=o+t|0;j=K[l+5160>>2]}n=n+1|0;if(n>>>0<j>>>0){continue}break}}K[l+5160>>2]=0;Ga(m);K[l+5164>>2]=0;K[l+5168>>2]=K[l+5172>>2];K[l+5176>>2]=K[l+5180>>2]}l=K[a+232>>2];Y=K[l+28>>2];o=K[a+228>>2];G=K[(K[Y+76>>2]+Q(o,5644)|0)+5584>>2];j=K[l+24>>2];Z=K[j+24>>2];n=K[Y+24>>2];m=(o>>>0)/(n>>>0)|0;U=K[K[l+20>>2]>>2];l=o-Q(m,n)|0;n=K[Y+12>>2];l=K[Y+4>>2]+Q(l,n)|0;o=K[j>>2];o=l>>>0>o>>>0?l:o;K[U>>2]=o;n=l+n|0;l=l>>>0>n>>>0?-1:n;n=K[j+8>>2];l=l>>>0<n>>>0?l:n;K[U+8>>2]=l;J:{K:{if(!((l|0)>(o|0)&(o|0)>=0)){Fa(k,1,6645,0);break K}n=K[U+20>>2];l=m;m=K[Y+16>>2];l=K[Y+8>>2]+Q(l,m)|0;o=K[j+4>>2];o=l>>>0>o>>>0?l:o;K[U+4>>2]=o;m=l+m|0;l=l>>>0>m>>>0?-1:m;j=K[j+12>>2];j=j>>>0>l>>>0?l:j;K[U+12>>2]=j;if(!((j|0)>(o|0)&(o|0)>=0)){Fa(k,1,6607,0);break K}L:{if(K[G+4>>2]){if(K[U+16>>2]){break L}j=1;break J}Fa(k,1,5321,0);break K}M:{N:{while(1){K[Z+36>>2]=0;j=K[Z>>2];m=j>>31;w=j-1|0;l=K[U>>2];r=l;o=w+l|0;v=m-!j|0;l=v+(l>>31)|0;ta=n,wa=Me(o,o>>>0<r>>>0?l+1|0:l,j,m),K[ta>>2]=wa;o=K[Z+4>>2];t=o>>31;r=o-1|0;l=K[U+4>>2];p=l;y=r+l|0;q=t-!o|0;l=q+(l>>31)|0;ta=n,wa=Me(y,p>>>0>y>>>0?l+1|0:l,o,t),K[ta+4>>2]=wa;l=K[U+8>>2];y=l;w=l+w|0;l=(l>>31)+v|0;ta=n,wa=Me(w,w>>>0<y>>>0?l+1|0:l,j,m),K[ta+8>>2]=wa;j=K[U+12>>2];K[n+16>>2]=ga;l=q+(j>>31)|0;j=j+r|0;l=j>>>0<r>>>0?l+1|0:l;ta=n,wa=Me(j,l,o,t),K[ta+12>>2]=wa;j=K[G+4>>2];K[n+20>>2]=j;l=K[Y+80>>2];K[n+24>>2]=j>>>0<l>>>0?1:j-l|0;Ga(K[n+52>>2]);K[n+68>>2]=0;K[n+60>>2]=0;K[n+64>>2]=0;K[n+52>>2]=0;K[n+56>>2]=0;j=Q(j,152);l=K[n+28>>2];O:{if(!l){l=Ja(j);K[n+28>>2]=l;if(!l){break K}K[n+32>>2]=j;if(!j){break O}B(l,0,j);break O}if(j>>>0<=N[n+32>>2]){break O}l=La(l,j);if(!l){Fa(k,1,3053,0);Ga(K[n+28>>2]);K[n+28>>2]=0;K[n+32>>2]=0;break K}K[n+28>>2]=l;o=K[n+32>>2];m=j-o|0;if(m){B(l+o|0,0,m)}K[n+32>>2]=j}j=K[n+20>>2];if(j){ja=G+944|0;ka=G+812|0;ea=G+28|0;o=K[n+28>>2];_=0;while(1){t=j-1|0;m=t&31;if((t&63)>>>0>=32){l=-1<<m;r=0}else{r=-1<<m;l=r|(1<<m)-1&-1>>>32-m}w=r^-1;r=K[n>>2];m=w+r|0;y=l^-1;l=y+(r>>31)|0;l=m>>>0<r>>>0?l+1|0:l;r=m;m=t&31;if((t&63)>>>0>=32){p=l>>m}else{p=((1<<m)-1&l)<<32-m|r>>>m}K[o>>2]=p;l=K[n+4>>2];r=l;m=l+w|0;l=(l>>31)+y|0;l=m>>>0<r>>>0?l+1|0:l;r=m;m=t&31;if((t&63)>>>0>=32){q=l>>m}else{q=((1<<m)-1&l)<<32-m|r>>>m}K[o+4>>2]=q;l=K[n+8>>2];r=l;m=l+w|0;l=(l>>31)+y|0;l=m>>>0<r>>>0?l+1|0:l;r=m;m=t&31;if((t&63)>>>0>=32){r=l>>m}else{r=((1<<m)-1&l)<<32-m|r>>>m}K[o+8>>2]=r;l=K[n+12>>2];v=l;m=l+w|0;l=(l>>31)+y|0;l=m>>>0<v>>>0?l+1|0:l;v=m;m=t&31;if((t&63)>>>0>=32){v=l>>m}else{v=((1<<m)-1&l)<<32-m|v>>>m}K[o+12>>2]=v;A=r>>31;D=_<<2;P=K[D+ka>>2];m=P&31;if((P&63)>>>0>=32){l=1<<m;u=0}else{u=1<<m;l=u-1&1>>>32-m}H=u;m=H+r|0;l=l+A|0;A=m-1|0;m=(m>>>0<H>>>0?l+1|0:l)-!m|0;l=P&31;if((P&63)>>>0>=32){l=m>>l}else{l=((1<<l)-1&m)<<32-l|A>>>l}A=l<<P;if((A|0)<0){break M}S=v>>31;H=K[D+ja>>2];m=H&31;if((H&63)>>>0>=32){l=-1<<m;m=0}else{l=(1<<m)-1&-1>>>32-m;m=-1<<m;l=l|m}D=m^-1;m=D+v|0;l=(l^-1)+S|0;l=m>>>0<D>>>0?l+1|0:l;D=m;m=H&31;if((H&63)>>>0>=32){l=l>>m}else{l=((1<<m)-1&l)<<32-m|D>>>m}l=l<<H;if((l|0)<0){break M}$=q&-1<<H;v=(q|0)!=(v|0)?l-$>>H:0;K[o+20>>2]=v;aa=p&-1<<P;m=(p|0)!=(r|0)?A-aa>>P:0;K[o+16>>2]=m;Le(m,0,v);if(!(!m|!ua)){break N}ca=Q(m,v);if(ca>>>0>=107374183){break N}V=Q(ca,40);if(_){H=H-1|0;P=P-1|0;l=$>>31;m=$+1|0;$=((m?l:l+1|0)&1)<<31|m>>>1;l=aa>>31;m=aa+1|0;aa=((m?l:l+1|0)&1)<<31|m>>>1;l=3}else{l=1}K[o+24>>2]=l;m=o+28|0;v=j;r=j&31;if((j&63)>>>0>=32){l=1<<r;j=0}else{j=1<<r;l=j-1&1>>>32-r}ia=j;r=l;j=K[G+12>>2];S=j>>>0<H>>>0?j:H;j=S&31;if((S&63)>>>0>=32){l=-1<<j;j=0}else{l=(1<<j)-1&-1>>>32-j;j=-1<<j;l=l|j}la=j^-1;ma=l^-1;j=K[G+8>>2];T=j>>>0<P>>>0?j:P;j=T&31;if((T&63)>>>0>=32){l=-1<<j;j=0}else{l=(1<<j)-1&-1>>>32-j;j=-1<<j;l=l|j}na=j^-1;oa=l^-1;fa=0;while(1){P:{if(!_){l=K[n+4>>2];p=l;j=l+w|0;l=(l>>31)+y|0;l=j>>>0<p>>>0?l+1|0:l;p=j;j=t&31;if((t&63)>>>0>=32){M=l>>j}else{M=((1<<j)-1&l)<<32-j|p>>>j}l=K[n>>2];p=l;j=l+w|0;l=(l>>31)+y|0;l=j>>>0<p>>>0?l+1|0:l;p=j;j=t&31;if((t&63)>>>0>=32){ba=l>>j}else{ba=((1<<j)-1&l)<<32-j|p>>>j}j=0;p=w;A=p;q=y;D=q;l=t;break P}j=fa+1|0;p=j>>>1|0;q=t&31;if((t&63)>>>0>=32){l=p<<q;p=0}else{l=(1<<q)-1&p>>>32-q;p=p<<q}p=p^-1;A=p+ia|0;l=(l^-1)+r|0;l=p>>>0>A>>>0?l+1|0:l;q=K[n+4>>2];p=q+A|0;D=l;l=l+(q>>31)|0;l=p>>>0<q>>>0?l+1|0:l;q=p;p=v&31;if((v&63)>>>0>=32){M=l>>p}else{M=((1<<p)-1&l)<<32-p|q>>>p}p=j&1;q=t&31;if((t&63)>>>0>=32){l=p<<q;p=0}else{l=(1<<q)-1&p>>>32-q;p=p<<q}q=p^-1;p=q+ia|0;l=(l^-1)+r|0;u=K[n>>2];C=u+p|0;q=p>>>0<q>>>0?l+1|0:l;l=q+(u>>31)|0;l=u>>>0>C>>>0?l+1|0:l;u=C;C=v&31;if((v&63)>>>0>=32){ba=l>>C}else{ba=((1<<C)-1&l)<<32-C|u>>>C}l=v}C=l;u=K[n+8>>2];ha=u>>31;F=K[n+12>>2];K[m+4>>2]=M;K[m>>2]=ba;K[m+16>>2]=j;l=(F>>31)+D|0;A=A+F|0;l=A>>>0<F>>>0?l+1|0:l;D=A;A=C&31;if((C&63)>>>0>=32){l=l>>A}else{l=((1<<A)-1&l)<<32-A|D>>>A}K[m+12>>2]=l;l=q+ha|0;p=p+u|0;l=p>>>0<u>>>0?l+1|0:l;q=p;p=C&31;if((C&63)>>>0>=32){l=l>>p}else{l=((1<<p)-1&l)<<32-p|q>>>p}K[m+8>>2]=l;da=1;p=K[ea>>2];j=(K[Z+24>>2]+(!K[G+20>>2]|!j?0:(j|0)==3?2:1)|0)-p|0;Q:{if((j|0)>=1024){da=898846567431158e293;if(j>>>0<2047){j=j-1023|0;break Q}da=Infinity;j=(j>>>0>=3069?3069:j)-2046|0;break Q}if((j|0)>-1023){break Q}da=2004168360008973e-307;if(j>>>0>4294965304){j=j+969|0;break Q}da=0;j=(j>>>0<=4294964336?-2960:j)+1938|0}pa=+K[ea+4>>2]*.00048828125+1;x(0,0);x(1,j+1023<<20);ta=m,xa=R(pa*(da*+z())),O[ta+32>>2]=xa;K[m+28>>2]=(p+K[G+804>>2]|0)-1;j=K[m+20>>2];R:{S:{if(!(j|!ca)){j=Ja(V);K[m+20>>2]=j;if(!j){Fa(k,1,2817,0);break K}if(V){B(j,0,V)}K[m+24>>2]=V;break S}if(V>>>0>N[m+24>>2]){j=La(j,V);if(!j){Fa(k,1,2817,0);Ga(K[m+20>>2]);K[m+20>>2]=0;K[m+24>>2]=0;break K}K[m+20>>2]=j;l=K[m+24>>2];p=V-l|0;if(p){B(j+l|0,0,p)}K[m+24>>2]=V}if(!ca){break R}}j=K[m+20>>2];A=0;while(1){p=K[o+16>>2];l=(A>>>0)/(p>>>0)|0;p=A-Q(l,p)|0;q=(p<<P)+aa|0;D=K[m>>2];D=(q|0)>(D|0)?q:D;K[j>>2]=D;q=(l<<H)+$|0;C=K[m+4>>2];C=(q|0)>(C|0)?q:C;K[j+4>>2]=C;p=(p+1<<P)+aa|0;q=K[m+8>>2];p=(p|0)<(q|0)?p:q;K[j+8>>2]=p;l=(l+1<<H)+$|0;q=K[m+12>>2];q=(l|0)<(q|0)?l:q;K[j+12>>2]=q;l=(p>>31)+oa|0;u=p;p=p+na|0;l=u>>>0>p>>>0?l+1|0:l;D=D>>T;u=p;p=T&31;if((T&63)>>>0>=32){l=l>>p}else{l=((1<<p)-1&l)<<32-p|u>>>p}u=l-D<<T>>T;K[j+16>>2]=u;l=(q>>31)+ma|0;p=q+la|0;l=p>>>0<q>>>0?l+1|0:l;C=C>>S;q=p;p=S&31;if((S&63)>>>0>=32){l=l>>p}else{l=((1<<p)-1&l)<<32-p|q>>>p}l=l-C<<S>>S;K[j+20>>2]=l;p=Q(l,u);Le(p,0,68);if(ua){Fa(k,1,2898,0);break K}l=Q(p,68);q=K[j+24>>2];T:{U:{if(!(q|!p)){q=Ja(l);K[j+24>>2]=q;if(!q){break K}if(!l){break U}B(q,0,l);break U}if(l>>>0<=N[j+28>>2]){break T}q=La(q,l);if(!q){Ga(K[j+24>>2]);K[j+24>>2]=0;K[j+28>>2]=0;Fa(k,1,2512,0);break K}K[j+24>>2]=q;u=K[j+28>>2];F=l-u|0;if(!F){break U}B(q+u|0,0,F)}K[j+28>>2]=l}l=K[j+20>>2];q=K[j+16>>2];u=K[j+32>>2];V:{if(!u){l=wc(q,l,k);break V}l=uc(u,q,l,k)}K[j+32>>2]=l;l=K[j+20>>2];q=K[j+16>>2];u=K[j+36>>2];W:{if(!u){l=wc(q,l,k);break W}l=uc(u,q,l,k)}K[j+36>>2]=l;if(p){ba=C+1|0;ha=D+1|0;q=0;while(1){W=K[j+16>>2];u=(q>>>0)/(W>>>0)|0;l=K[j+24>>2]+Q(q,68)|0;M=K[l>>2];X:{if(M){qa=K[l+56>>2];F=K[l+48>>2];sa=K[l+4>>2];Ga(K[l+60>>2]);K[l+48>>2]=0;K[l+52>>2]=0;K[l- -64>>2]=0;K[l+56>>2]=0;K[l+60>>2]=0;K[l+40>>2]=0;K[l+44>>2]=0;K[l+32>>2]=0;K[l+36>>2]=0;K[l+24>>2]=0;K[l+28>>2]=0;K[l+16>>2]=0;K[l+20>>2]=0;K[l+8>>2]=0;K[l+12>>2]=0;K[l>>2]=M;K[l+48>>2]=F;Y:{if(!F){break Y}F=Q(F,24);if(!F){break Y}B(M,0,F)}K[l+56>>2]=qa;K[l+4>>2]=sa;break X}F=Ia(10,24);K[l>>2]=F;if(!F){break K}K[l+48>>2]=10}F=q-Q(u,W)|0;M=F+D<<T;W=K[j>>2];K[l+8>>2]=(M|0)>(W|0)?M:W;M=u+C<<S;W=K[j+4>>2];K[l+12>>2]=(M|0)>(W|0)?M:W;F=F+ha<<T;M=K[j+8>>2];K[l+16>>2]=(F|0)<(M|0)?F:M;M=l;l=u+ba<<S;u=K[j+12>>2];K[M+20>>2]=(l|0)<(u|0)?l:u;q=q+1|0;if((p|0)!=(q|0)){continue}break}}j=j+40|0;A=A+1|0;if((A|0)!=(ca|0)){continue}break}}ea=ea+8|0;m=m+36|0;fa=fa+1|0;if(fa>>>0<N[o+24>>2]){continue}break}o=o+152|0;j=t;_=_+1|0;if(_>>>0<N[n+20>>2]){continue}break}}Z=Z+52|0;n=n+76|0;G=G+1080|0;ga=ga+1|0;if(ga>>>0<N[U+16>>2]){continue}break}j=1;break J}Fa(k,1,2945,0);break K}Fa(k,1,2336,0)}j=0}if(!j){Fa(k,1,3631,0);j=0;break a}j=K[a+228>>2];K[s+4>>2]=Q(K[a+128>>2],K[a+132>>2]);K[s>>2]=j+1;Fa(k,4,11788,s);K[b>>2]=K[a+228>>2];K[i>>2]=1;if(c){b=fc(K[a+232>>2],0);K[c>>2]=b;j=0;if((b|0)==-1){break a}}b=K[K[K[a+232>>2]+20>>2]>>2];K[d>>2]=K[b>>2];K[e>>2]=K[b+4>>2];K[f>>2]=K[b+8>>2];K[g>>2]=K[b+12>>2];K[h>>2]=K[b+16>>2];K[a+8>>2]=K[a+8>>2]|128}j=1;break a}Fa(k,1,m,0)}Fa(k,1,3665,0);j=0}ra=s+80|0;return j|0}function jc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,F=0,G=0,H=0,I=0,O=0,P=0,R=0,S=0,T=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{switch(K[a+84>>2]){case 0:k:{c=K[a+52>>2];b=K[a+196>>2];if(c>>>0<b>>>0){q=K[a+64>>2];if(q>>>0<b+1>>>0){break k}}Fa(K[a+236>>2],1,8454,0);break b}if(!K[a+44>>2]){k=K[a+36>>2];b=0;break i}K[a+44>>2]=0;i=K[a+68>>2];b=1;break i;case 1:l:{c=K[a+52>>2];b=K[a+196>>2];if(c>>>0<b>>>0){q=K[a+64>>2];if(q>>>0<b+1>>>0){break l}}Fa(K[a+236>>2],1,8499,0);break b}if(!K[a+44>>2]){e=K[a+36>>2];b=0;break e}K[a+44>>2]=0;i=K[a+48>>2];b=1;break e;case 2:m:{A=K[a+52>>2];x=K[a+196>>2];if(A>>>0<x>>>0){r=K[a+64>>2];if(r>>>0<x+1>>>0){break m}}Fa(K[a+236>>2],1,8634,0);break b}if(!K[a+44>>2]){y=K[a+40>>2];break f}K[a+228>>2]=0;K[a+232>>2]=0;K[a+44>>2]=0;j=K[a+200>>2];while(1){I=j+(u<<4)|0;l=K[I+8>>2];if(l){q=K[I+12>>2];b=0;while(1){g=l+(b^-1)|0;d=q+(b<<4)|0;s=g+K[d>>2]|0;n:{if(s>>>0>31){break n}c=K[I>>2];if(c>>>0>-1>>>s>>>0){break n}c=c<<s;k=k?c>>>0>k>>>0?k:c:c;K[a+228>>2]=k}g=g+K[d+4>>2]|0;o:{if(g>>>0>31){break o}c=K[I+4>>2];if(c>>>0>-1>>>g>>>0){break o}c=c<<g;i=i?c>>>0>i>>>0?i:c:c;K[a+232>>2]=i}b=b+1|0;if((l|0)!=(b|0)){continue}break}}u=u+1|0;if((x|0)!=(u|0)){continue}break};if(!k|!i){break d}if(!L[a|0]){K[a+108>>2]=K[a+208>>2];K[a+100>>2]=K[a+204>>2];K[a+112>>2]=K[a+216>>2];K[a+104>>2]=K[a+212>>2]}o=K[a+48>>2];b=1;break f;case 3:p:{A=K[a+52>>2];l=K[a+196>>2];if(A>>>0<l>>>0){O=K[a+64>>2];if(O>>>0<l+1>>>0){break p}}Fa(K[a+236>>2],1,8589,0);break b}if(!K[a+44>>2]){B=K[a+200>>2];e=K[a+28>>2];y=B+(e<<4)|0;E=K[a+40>>2];break g}K[a+228>>2]=0;K[a+232>>2]=0;K[a+44>>2]=0;B=K[a+200>>2];while(1){x=(p<<4)+B|0;s=K[x+8>>2];if(s){q=K[x+12>>2];b=0;while(1){g=s+(b^-1)|0;d=q+(b<<4)|0;j=g+K[d>>2]|0;q:{if(j>>>0>31){break q}c=K[x>>2];if(c>>>0>-1>>>j>>>0){break q}c=c<<j;k=k?c>>>0>k>>>0?k:c:c;K[a+228>>2]=k}g=g+K[d+4>>2]|0;r:{if(g>>>0>31){break r}c=K[x+4>>2];if(c>>>0>-1>>>g>>>0){break r}c=c<<g;i=i?c>>>0>i>>>0?i:c:c;K[a+232>>2]=i}b=b+1|0;if((s|0)!=(b|0)){continue}break}}p=p+1|0;if((l|0)!=(p|0)){continue}break};if(!k|!i){break d}s:{if(L[a|0]){p=K[a+108>>2];break s}p=K[a+208>>2];K[a+108>>2]=p;K[a+100>>2]=K[a+204>>2];K[a+112>>2]=K[a+216>>2];K[a+104>>2]=K[a+212>>2]}b=1;break g;case 4:break j;default:break d}}t:{p=K[a+52>>2];b=K[a+196>>2];if(p>>>0<b>>>0){r=K[a+64>>2];if(r>>>0<b+1>>>0){break t}}Fa(K[a+236>>2],1,8544,0);break d}if(!K[a+44>>2]){p=K[a+28>>2];o=K[a+200>>2]+(p<<4)|0;u=K[a+40>>2];b=0;break h}K[a+28>>2]=p;K[a+44>>2]=0;b=1;break h}u:while(1){v:{w:{if(!b){k=k+1|0;break w}K[a+40>>2]=i;if(N[a+56>>2]<=i>>>0){break b}e=K[a+48>>2];b=0;break v}b=1}x:while(1){y:{z:{A:{B:{if(!b){K[a+32>>2]=e;if(N[a+60>>2]<=e>>>0){break B}K[a+28>>2]=c;b=c;o=0;break y}K[a+36>>2]=k;if(N[a+76>>2]<=k>>>0){b=K[a+28>>2];o=1;break y}b=((Q(K[a+16>>2],K[a+32>>2])+Q(K[a+12>>2],K[a+40>>2])|0)+Q(K[a+20>>2],K[a+28>>2])|0)+Q(K[a+24>>2],k)|0;if(b>>>0>=N[a+8>>2]){break c}b=K[a+4>>2]+(b<<1)|0;if(M[b>>1]){break A}break a}i=K[a+40>>2]+1|0;break z}b=0;continue u}b=1;continue u}while(1){C:{D:{E:{if(!o){if(b>>>0>=q>>>0){break E}g=K[a+32>>2];d=K[a+200>>2]+(b<<4)|0;if(g>>>0>=N[d+8>>2]){break C}if(!L[a|0]){b=K[d+12>>2]+(g<<4)|0;K[a+76>>2]=Q(K[b+12>>2],K[b+8>>2])}k=K[a+72>>2];b=1;continue x}b=b+1|0;K[a+28>>2]=b;break D}e=K[a+32>>2]+1|0;b=0;continue x}o=0;continue}o=1;continue}}}}F:while(1){G:{H:{if(!b){u=u+1|0;K[a+40>>2]=u;break H}if(p>>>0>=r>>>0){break b}K[a+228>>2]=0;K[a+232>>2]=0;o=K[a+200>>2]+(p<<4)|0;s=K[o+8>>2];if(!s){break b}q=K[o+12>>2];k=0;e=0;b=0;while(1){g=s+(b^-1)|0;d=q+(b<<4)|0;j=g+K[d>>2]|0;I:{if(j>>>0>31){break I}c=K[o>>2];if(c>>>0>-1>>>j>>>0){break I}c=c<<j;e=e?c>>>0>e>>>0?e:c:c;K[a+228>>2]=e}g=g+K[d+4>>2]|0;J:{if(g>>>0>31){break J}c=K[o+4>>2];if(c>>>0>-1>>>g>>>0){break J}c=c<<g;k=k?c>>>0>k>>>0?k:c:c;K[a+232>>2]=k}b=b+1|0;if((s|0)!=(b|0)){continue}break}if(!e|!k){break d}K:{if(L[a|0]){k=K[a+108>>2];break K}k=K[a+208>>2];K[a+108>>2]=k;K[a+100>>2]=K[a+204>>2];K[a+112>>2]=K[a+216>>2];K[a+104>>2]=K[a+212>>2]}b=0;break G}b=1}L:while(1){M:{N:{O:{P:{if(!b){K[a+224>>2]=k;if(N[a+112>>2]<=k>>>0){break P}B=K[a+100>>2];b=0;break M}if(N[a+56>>2]<=u>>>0){i=K[a+32>>2];b=1;break M}b=((Q(K[a+16>>2],K[a+32>>2])+Q(K[a+12>>2],u)|0)+Q(K[a+20>>2],p)|0)+Q(K[a+24>>2],K[a+36>>2])|0;if(b>>>0>=N[a+8>>2]){break c}b=K[a+4>>2]+(b<<1)|0;if(M[b>>1]){break O}break a}p=p+1|0;K[a+28>>2]=p;break N}b=0;continue F}b=1;continue F}while(1){Q:{R:{S:{T:{if(!b){K[a+220>>2]=B;if(N[a+104>>2]<=B>>>0){break S}i=K[a+48>>2];break T}i=i+1|0}K[a+32>>2]=i;b=K[a+60>>2];d=K[o+8>>2];if((b>>>0<d>>>0?b:d)>>>0>i>>>0){g=K[o>>2];c=g;n=d+(i^-1)|0;m=n;d=m&31;if((m&63)>>>0>=32){b=c<<d;v=0}else{b=(1<<d)-1&g>>>32-d;v=g<<d}q=c;f=b;c=v;d=m&31;if((m&63)>>>0>=32){b=b>>>d|0}else{b=((1<<d)-1&b)<<32-d|c>>>d}if((q|0)!=(b|0)){break Q}b=m&31;if((m&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}c=K[o+4>>2];if((b&c)!=(c|0)){break Q}d=m&31;if((m&63)>>>0>=32){b=c<<d;w=0}else{b=(1<<d)-1&c>>>32-d;w=c<<d}C=b;q=b-!w|0;h=q;F=w-1|0;d=K[a+216>>2];j=F+d|0;I=Ne(j,d>>>0>j>>>0?h+1|0:h,w,b);b=h;G=K[a+208>>2];d=F+G|0;b=G>>>0>d>>>0?b+1|0:b;s=Ne(d,b,w,C);A=v-1|0;j=K[a+212>>2];l=A+j|0;d=f-!v|0;b=d;x=Ne(l,l>>>0<j>>>0?b+1|0:b,v,f);D=K[a+204>>2];j=A+D|0;b=D>>>0>j>>>0?b+1|0:b;j=Ne(j,b,v,f);z=K[o+12>>2]+(i<<4)|0;H=K[z>>2];t=H+n|0;b=t&31;if((t&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}if((g|0)!=(b&g)){break Q}h=c;O=K[z+4>>2];n=O+n|0;e=n&31;if((n&63)>>>0>=32){b=c<<e;e=0}else{b=(1<<e)-1&c>>>32-e;e=c<<e}c=e;l=n&31;if((n&63)>>>0>=32){c=b>>>l|0}else{c=((1<<l)-1&b)<<32-l|c>>>l}if((h|0)!=(c|0)){break Q}l=K[a+224>>2];e=!!(Oe(l,e,b)|ua);b=n&31;if((n&63)>>>0>=32){h=-1<<b;b=0}else{c=(1<<b)-1&-1>>>32-b;b=-1<<b;h=c|b}b=b^-1;c=h^-1;P=e;R=b;b=0;n=s;e=m&31;if((m&63)>>>0>=32){h=n<<e;b=0}else{h=(1<<e)-1&n>>>32-e|b<<e;b=n<<e}if(P&(!(R&b|c&h)|(l|0)!=(G|0))){break Q}n=t&31;c=K[a+220>>2];if((t&63)>>>0>=32){b=g<<n;e=0}else{b=(1<<n)-1&g>>>32-n;e=g<<n}n=!!(Oe(c,e,b)|ua);b=t&31;if((t&63)>>>0>=32){h=-1<<b;b=0}else{e=(1<<b)-1&-1>>>32-b;b=-1<<b;h=e|b}b=b^-1;g=h^-1;e=n;P=b;b=0;n=j;t=m&31;if((m&63)>>>0>=32){h=j<<t;b=0}else{h=(1<<t)-1&n>>>32-t|b<<t;b=n<<t}if(e&(!(P&b|g&h)|(c|0)!=(D|0))){break Q}n=K[z+8>>2];if(!n|(!K[z+12>>2]|(j|0)==(x|0))){break Q}if((s|0)==(I|0)){break Q}u=K[a+68>>2];K[a+40>>2]=u;b=d;c=c+A|0;b=c>>>0<A>>>0?b+1|0:b;g=(Ne(c,b,v,f)>>>H)-(j>>>H)|0;b=q;c=l+F|0;b=c>>>0<l>>>0?b+1|0:b;S=a,T=Q(n,(Ne(c,b,w,C)>>>O)-(s>>>O)|0)+g|0,K[S+36>>2]=T;b=1;continue L}c=K[a+220>>2];b=K[a+228>>2];B=c+b-(c>>>0)%(b>>>0)|0;break R}c=K[a+224>>2];b=K[a+232>>2];k=c+b-(c>>>0)%(b>>>0)|0;b=0;continue L}b=0;continue}b=1;continue}}}}U:while(1){V:{W:{if(!b){E=E+1|0;K[a+40>>2]=E;break W}K[a+224>>2]=p;if(N[a+112>>2]<=p>>>0){break b}v=K[a+100>>2];b=0;break V}b=1}X:while(1){Y:{Z:{_:{$:{if(!b){K[a+220>>2]=v;if(N[a+104>>2]<=v>>>0){break $}K[a+28>>2]=A;e=A;b=0;break Y}if(N[a+56>>2]<=E>>>0){u=K[a+32>>2];b=1;break Y}b=((Q(K[a+16>>2],K[a+32>>2])+Q(K[a+12>>2],E)|0)+Q(K[a+20>>2],e)|0)+Q(K[a+24>>2],K[a+36>>2])|0;if(b>>>0>=N[a+8>>2]){break c}b=K[a+4>>2]+(b<<1)|0;if(M[b>>1]){break _}break a}c=K[a+224>>2];b=K[a+232>>2];p=c+b-(c>>>0)%(b>>>0)|0;break Z}b=0;continue U}b=1;continue U}while(1){aa:{ba:{ca:{da:{if(!b){if(e>>>0>=O>>>0){break ca}u=K[a+48>>2];K[a+32>>2]=u;y=(e<<4)+B|0;break da}u=u+1|0;K[a+32>>2]=u}b=K[a+60>>2];d=K[y+8>>2];if((b>>>0<d>>>0?b:d)>>>0>u>>>0){g=K[y>>2];c=g;f=d+(u^-1)|0;i=f;d=f&31;if((f&63)>>>0>=32){b=c<<d;k=0}else{b=(1<<d)-1&g>>>32-d;k=g<<d}q=c;t=b;c=k;d=i&31;if((i&63)>>>0>=32){b=b>>>d|0}else{b=((1<<d)-1&b)<<32-d|c>>>d}if((q|0)!=(b|0)){break aa}b=i&31;if((i&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}c=K[y+4>>2];if((b&c)!=(c|0)){break aa}d=i&31;if((i&63)>>>0>=32){b=c<<d;o=0}else{b=(1<<d)-1&c>>>32-d;o=c<<d}n=b;q=b-!o|0;h=q;F=o-1|0;d=K[a+216>>2];j=F+d|0;I=Ne(j,d>>>0>j>>>0?h+1|0:h,o,b);b=h;w=K[a+208>>2];d=w+F|0;b=w>>>0>d>>>0?b+1|0:b;s=Ne(d,b,o,n);C=k-1|0;j=K[a+212>>2];l=C+j|0;d=t-!k|0;b=d;x=Ne(l,l>>>0<j>>>0?b+1|0:b,k,t);G=K[a+204>>2];j=C+G|0;b=G>>>0>j>>>0?b+1|0:b;j=Ne(j,b,k,t);D=K[y+12>>2]+(u<<4)|0;z=K[D>>2];m=z+f|0;b=m&31;if((m&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}if((g|0)!=(b&g)){break aa}h=c;H=K[D+4>>2];f=H+f|0;r=f&31;if((f&63)>>>0>=32){b=c<<r;r=0}else{b=(1<<r)-1&c>>>32-r;r=c<<r}c=r;l=f&31;if((f&63)>>>0>=32){c=b>>>l|0}else{c=((1<<l)-1&b)<<32-l|c>>>l}if((h|0)!=(c|0)){break aa}l=K[a+224>>2];r=!!(Oe(l,r,b)|ua);b=f&31;if((f&63)>>>0>=32){h=-1<<b;b=0}else{c=(1<<b)-1&-1>>>32-b;b=-1<<b;h=c|b}b=b^-1;c=h^-1;P=r;R=b;b=0;f=s;r=i&31;if((i&63)>>>0>=32){h=f<<r;b=0}else{h=(1<<r)-1&f>>>32-r|b<<r;b=f<<r}if(P&(!(R&b|c&h)|(l|0)!=(w|0))){break aa}f=m&31;c=K[a+220>>2];if((m&63)>>>0>=32){b=g<<f;f=0}else{b=(1<<f)-1&g>>>32-f;f=g<<f}f=!!(Oe(c,f,b)|ua);b=m&31;if((m&63)>>>0>=32){h=-1<<b;b=0}else{g=(1<<b)-1&-1>>>32-b;b=-1<<b;h=g|b}b=b^-1;g=h^-1;r=f;w=b;b=0;f=j;m=i&31;if((i&63)>>>0>=32){h=f<<m;b=0}else{h=(1<<m)-1&f>>>32-m|b<<m;b=f<<m}if(r&(!(w&b|g&h)|(c|0)!=(G|0))){break aa}f=K[D+8>>2];if(!f|(!K[D+12>>2]|(j|0)==(x|0))){break aa}if((s|0)==(I|0)){break aa}E=K[a+68>>2];K[a+40>>2]=E;b=d;c=c+C|0;b=c>>>0<C>>>0?b+1|0:b;g=(Ne(c,b,k,t)>>>z)-(j>>>z)|0;b=q;c=l+F|0;b=c>>>0<l>>>0?b+1|0:b;S=a,T=Q(f,(Ne(c,b,o,n)>>>H)-(s>>>H)|0)+g|0,K[S+36>>2]=T;b=1;continue X}e=e+1|0;K[a+28>>2]=e;break ba}c=K[a+220>>2];b=K[a+228>>2];v=c+b-(c>>>0)%(b>>>0)|0;b=0;continue X}b=0;continue}b=1;continue}}}}ea:while(1){fa:{ga:{if(!b){y=y+1|0;K[a+40>>2]=y;break ga}K[a+32>>2]=o;if(N[a+60>>2]<=o>>>0){break b}E=K[a+108>>2];b=0;break fa}b=1}ha:while(1){ia:{ja:{ka:{la:{if(!b){K[a+224>>2]=E;if(N[a+112>>2]<=E>>>0){break la}B=K[a+100>>2];b=0;break ia}if(N[a+56>>2]<=y>>>0){p=K[a+28>>2];b=1;break ia}b=((Q(K[a+16>>2],K[a+32>>2])+Q(K[a+12>>2],y)|0)+Q(K[a+20>>2],K[a+28>>2])|0)+Q(K[a+24>>2],K[a+36>>2])|0;if(b>>>0>=N[a+8>>2]){break c}b=K[a+4>>2]+(b<<1)|0;if(M[b>>1]){break ka}break a}o=K[a+32>>2]+1|0;break ja}b=0;continue ea}b=1;continue ea}while(1){ma:{na:{oa:{pa:{if(!b){K[a+220>>2]=B;if(N[a+104>>2]<=B>>>0){break oa}K[a+28>>2]=A;p=A;break pa}p=p+1|0;K[a+28>>2]=p}if(p>>>0<r>>>0){m=K[a+32>>2];e=K[a+200>>2]+(p<<4)|0;b=K[e+8>>2];if(m>>>0>=b>>>0){break ma}g=K[e>>2];c=g;f=b+(m^-1)|0;i=f;d=f&31;if((f&63)>>>0>=32){b=c<<d;v=0}else{b=(1<<d)-1&g>>>32-d;v=g<<d}q=c;t=b;c=v;d=i&31;if((i&63)>>>0>=32){b=b>>>d|0}else{b=((1<<d)-1&b)<<32-d|c>>>d}if((q|0)!=(b|0)){break ma}b=i&31;if((i&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}c=K[e+4>>2];if((b&c)!=(c|0)){break ma}d=i&31;if((i&63)>>>0>=32){b=c<<d;w=0}else{b=(1<<d)-1&c>>>32-d;w=c<<d}n=b;q=b-!w|0;h=q;F=w-1|0;d=K[a+216>>2];j=F+d|0;I=Ne(j,d>>>0>j>>>0?h+1|0:h,w,b);b=h;G=K[a+208>>2];d=F+G|0;b=G>>>0>d>>>0?b+1|0:b;s=Ne(d,b,w,n);C=v-1|0;j=K[a+212>>2];l=C+j|0;d=t-!v|0;b=d;x=Ne(l,l>>>0<j>>>0?b+1|0:b,v,t);D=K[a+204>>2];j=C+D|0;b=D>>>0>j>>>0?b+1|0:b;j=Ne(j,b,v,t);z=K[e+12>>2]+(m<<4)|0;H=K[z>>2];m=H+f|0;b=m&31;if((m&63)>>>0>=32){b=-1>>>b|0}else{b=(1<<b)-1<<32-b|-1>>>b}if((g|0)!=(b&g)){break ma}h=c;O=K[z+4>>2];f=O+f|0;e=f&31;if((f&63)>>>0>=32){b=c<<e;e=0}else{b=(1<<e)-1&c>>>32-e;e=c<<e}c=e;l=f&31;if((f&63)>>>0>=32){c=b>>>l|0}else{c=((1<<l)-1&b)<<32-l|c>>>l}if((h|0)!=(c|0)){break ma}l=K[a+224>>2];e=!!(Oe(l,e,b)|ua);b=f&31;if((f&63)>>>0>=32){h=-1<<b;b=0}else{c=(1<<b)-1&-1>>>32-b;b=-1<<b;h=c|b}b=b^-1;c=h^-1;k=e;P=b;b=0;f=s;e=i&31;if((i&63)>>>0>=32){h=f<<e;b=0}else{h=(1<<e)-1&f>>>32-e|b<<e;b=f<<e}if(k&(!(P&b|c&h)|(l|0)!=(G|0))){break ma}f=m&31;c=K[a+220>>2];if((m&63)>>>0>=32){b=g<<f;f=0}else{b=(1<<f)-1&g>>>32-f;f=g<<f}f=!!(Oe(c,f,b)|ua);b=m&31;if((m&63)>>>0>=32){h=-1<<b;b=0}else{e=(1<<b)-1&-1>>>32-b;b=-1<<b;h=e|b}b=b^-1;g=h^-1;e=f;k=b;b=0;f=j;m=i&31;if((i&63)>>>0>=32){h=f<<m;b=0}else{h=(1<<m)-1&f>>>32-m|b<<m;b=f<<m}if(e&(!(k&b|g&h)|(c|0)!=(D|0))){break ma}f=K[z+8>>2];if(!f|(!K[z+12>>2]|(j|0)==(x|0))){break ma}if((s|0)==(I|0)){break ma}y=K[a+68>>2];K[a+40>>2]=y;b=d;c=c+C|0;b=c>>>0<C>>>0?b+1|0:b;g=(Ne(c,b,v,t)>>>H)-(j>>>H)|0;b=q;c=l+F|0;b=c>>>0<l>>>0?b+1|0:b;S=a,T=Q(f,(Ne(c,b,w,n)>>>O)-(s>>>O)|0)+g|0,K[S+36>>2]=T;b=1;continue ha}c=K[a+220>>2];b=K[a+228>>2];B=c+b-(c>>>0)%(b>>>0)|0;break na}c=K[a+224>>2];b=K[a+232>>2];E=c+b-(c>>>0)%(b>>>0)|0;b=0;continue ha}b=0;continue}b=1;continue}}}}qa:while(1){ra:{sa:{if(!b){e=e+1|0;break sa}K[a+32>>2]=i;if(N[a+60>>2]<=i>>>0){break b}k=K[a+68>>2];b=0;break ra}b=1}ta:while(1){ua:{va:{wa:{xa:{if(!b){K[a+40>>2]=k;if(N[a+56>>2]<=k>>>0){break xa}K[a+28>>2]=c;b=c;o=0;break ua}K[a+36>>2]=e;if(N[a+76>>2]<=e>>>0){b=K[a+28>>2];o=1;break ua}b=((Q(K[a+16>>2],K[a+32>>2])+Q(K[a+12>>2],K[a+40>>2])|0)+Q(K[a+20>>2],K[a+28>>2])|0)+Q(K[a+24>>2],e)|0;if(b>>>0>=N[a+8>>2]){break c}b=K[a+4>>2]+(b<<1)|0;if(M[b>>1]){break wa}break a}i=K[a+32>>2]+1|0;break va}b=0;continue qa}b=1;continue qa}while(1){ya:{za:{Aa:{if(!o){if(b>>>0>=q>>>0){break Aa}g=K[a+32>>2];d=K[a+200>>2]+(b<<4)|0;if(g>>>0>=N[d+8>>2]){break ya}if(!L[a|0]){b=K[d+12>>2]+(g<<4)|0;K[a+76>>2]=Q(K[b+12>>2],K[b+8>>2])}e=K[a+72>>2];b=1;continue ta}b=b+1|0;K[a+28>>2]=b;break za}k=K[a+40>>2]+1|0;b=0;continue ta}o=0;continue}o=1;continue}}}}return 0}Fa(K[a+236>>2],1,1306,0)}return 0}J[b>>1]=1;return 1}function Cd(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=R(0),s=0,t=0,u=0,v=0,w=R(0),x=0,y=0,z=0,A=R(0),C=R(0),D=R(0),F=0,G=0,H=0,J=0,M=0,N=R(0),O=0,P=0,T=0;m=ra-8320|0;ra=m;K[m+64>>2]=0;i=2;f=K[a>>2];a:{b:{if((f|0)==176622093){break b}if((f|0)!=1375686655){if(!((f|0)!=201326592|K[a+4>>2]!=538988650)&K[a+8>>2]==176622093){break b}$(1101);i=1;break a}i=0}f=Ia(1,96);g=0;c:{if(!f){break c}K[f+76>>2]=1;d:{e:{f:{switch(i|0){case 0:K[f+88>>2]=68;K[f+84>>2]=69;K[f+80>>2]=70;K[f+16>>2]=71;K[f+4>>2]=72;K[f+28>>2]=73;K[f+24>>2]=74;K[f+20>>2]=75;K[f>>2]=76;K[f+92>>2]=77;K[f+44>>2]=78;K[f+40>>2]=79;K[f+36>>2]=80;K[f+32>>2]=81;K[f+12>>2]=82;K[f+8>>2]=83;g=Yb();K[f+48>>2]=g;if(g){break e}break d;case 2:break f;default:break d}}K[f+88>>2]=84;K[f+84>>2]=85;K[f+80>>2]=86;K[f+16>>2]=87;K[f+4>>2]=88;K[f+92>>2]=89;K[f+44>>2]=90;K[f+40>>2]=91;K[f+36>>2]=92;K[f+32>>2]=93;K[f+28>>2]=94;K[f+24>>2]=95;K[f+20>>2]=96;K[f+12>>2]=97;K[f+8>>2]=98;K[f>>2]=99;g=Ia(1,136);g:{if(g){j=Yb();K[g>>2]=j;h:{if(!j){break h}K[g+108>>2]=0;K[g+112>>2]=0;I[g+124|0]=0;K[g+116>>2]=0;K[g+120>>2]=0;j=ub();K[g+4>>2]=j;if(!j){break h}j=ub();K[g+8>>2]=j;if(!j){break h}break g}Tc(g)}g=0}K[f+48>>2]=g;if(!g){break d}}K[f+72>>2]=1;K[f+64>>2]=1;K[f+60>>2]=0;K[f+52>>2]=0;K[f+56>>2]=0;K[f+68>>2]=1;g=f;break c}Ga(f);g=0}f=g;if(f){K[f+60>>2]=0;K[f+72>>2]=100}if(f){K[f+56>>2]=0;K[f+68>>2]=101}if(f){K[f+52>>2]=0;K[f+64>>2]=102}g=m+68|0;if(g){B(g,0,8248);K[g+8248>>2]=0;K[g+8200>>2]=-1;K[g+8204>>2]=-1}if(d){K[m+8316>>2]=K[m+8316>>2]|1}K[m+60>>2]=b;K[m+56>>2]=a;K[m+52>>2]=a;i=1;b=0;g=m+52|0;i:{if(!g){break i}a=Ia(1,72);if(a){j:{K[a+64>>2]=1048576;j=Ja(1048576);K[a+32>>2]=j;if(!j){Ga(a);a=0;break j}K[a+36>>2]=j;K[a+28>>2]=2;K[a+24>>2]=3;K[a+20>>2]=4;K[a+16>>2]=5;K[a+44>>2]=6;K[a+40>>2]=8;K[a+68>>2]=K[a+68>>2]|2}}else{a=0}if(!a){break i}if(a){K[a+4>>2]=0;K[a>>2]=g}b=K[g+8>>2];if(a){K[a+8>>2]=b;K[a+12>>2]=0}if(!(!a|!(L[a+68|0]&2))){K[a+16>>2]=64}if(a){K[a+24>>2]=66}if(a){K[a+28>>2]=67}b=a}a=f;f=m+68|0;if(!a|!f){f=0}else{k:{if(!K[a+76>>2]){Fa(a+52|0,1,9865,0);f=0;break k}va[K[a+24>>2]](K[a+48>>2],f);f=1}}if(!f){$(1116);zb(b);Cb(a);break a}if(!b|!a){f=0}else{l:{if(!K[a+76>>2]){Fa(a+52|0,1,9946,0);f=0;break l}f=va[K[a>>2]](b,K[a+48>>2],m- -64|0,a+52|0)|0}}if(!f){$(1144);zb(b);Cb(a);Ya(K[m+64>>2]);break a}g=K[m+64>>2];f=0;m:{if(!K[a+76>>2]|(!a|!b)){g=f}else{g=va[K[a+4>>2]](K[a+48>>2],b,g,a+52|0)|0}if(g){if(!(!K[a+76>>2]|(!a|!b))){f=va[K[a+16>>2]](K[a+48>>2],b,a+52|0)|0}if(f){break m}}$(1279);Cb(a);zb(b);Ya(K[m+64>>2]);break a}zb(b);Cb(a);l=K[m+64>>2];a=K[l+28>>2];if(a){Ga(a);l=K[m+64>>2];K[l+28>>2]=0;K[l+32>>2]=0}v=K[l+16>>2];n:{o:{if(!c){if(!(!e|(v|0)!=4)){k=1;v=4;break n}p:{b=K[l+20>>2];if(!((b|0)==3|(v|0)!=3)){a=K[l+24>>2];if(K[a>>2]!=K[a+4>>2]|K[a+52>>2]==1){break p}K[l+20>>2]=3;break o}if(v>>>0>2){break p}K[l+20>>2]=2;break n}q:{switch(b-3|0){case 2:r:{s:{if(v>>>0<4){break s}f=K[l+24>>2];a=K[f>>2];if((a|0)!=K[f+52>>2]|(a|0)!=K[f+104>>2]|(a|0)!=K[f+156>>2]){break s}a=K[f+4>>2];if((a|0)!=K[f+56>>2]|(a|0)!=K[f+108>>2]){break s}if((a|0)==K[f+160>>2]){break r}}K[m+20>>2]=1053;K[m+16>>2]=1336;Ka(26032,8142,m+16|0);break n}j=Q(K[f+12>>2],K[f+8>>2]);A=R(R(1)/R((-1<<K[f+180>>2]^-1)>>>0));C=R(R(1)/R((-1<<K[f+128>>2]^-1)>>>0));w=R(R(1)/R((-1<<K[f+76>>2]^-1)>>>0));N=R(R(1)/R((-1<<K[f+24>>2]^-1)>>>0));a=0;while(1){if((a|0)!=(j|0)){g=a<<2;b=g+K[f+148>>2]|0;p=K[b>>2];c=g+K[f+96>>2]|0;i=K[c>>2];k=g+K[f+44>>2]|0;r=R(R(1)-R(A*R(K[g+K[f+200>>2]>>2])));D=R(R(R(R(1)-R(N*R(K[k>>2])))*R(255))*r);if(R(S(D))<R(2147483648)){g=~~D}else{g=-2147483648}K[k>>2]=g;D=R(R(R(R(1)-R(w*R(i|0)))*R(255))*r);if(R(S(D))<R(2147483648)){g=~~D}else{g=-2147483648}K[c>>2]=g;r=R(R(R(R(1)-R(C*R(p|0)))*R(255))*r);if(R(S(r))<R(2147483648)){c=~~r}else{c=-2147483648}K[b>>2]=c;a=a+1|0;continue}break};Ga(K[f+200>>2]);a=K[l+24>>2];K[a+128>>2]=8;K[a+76>>2]=8;K[a+24>>2]=8;k=0;K[a+200>>2]=0;K[l+20>>2]=1;a=K[l+16>>2]-1|0;K[l+16>>2]=a;h=3;while(1){if(a>>>0<=h>>>0){break n}a=K[l+24>>2]+Q(h,52)|0;E(a,a+52|0,52);h=h+1|0;a=K[l+16>>2];continue};case 0:break o;case 1:break q;default:break n}}j=K[l+24>>2];a=K[j>>2];t:{u:{if((a|0)!=K[j+52>>2]|(a|0)!=K[j+104>>2]){break u}a=K[j+4>>2];if((a|0)!=K[j+56>>2]){break u}if((a|0)==K[j+108>>2]){break t}}K[m+36>>2]=1115;K[m+32>>2]=1336;Ka(26032,8184,m+32|0);break n}a=K[j+24>>2];b=-1<<a^-1;a=1<<a-1;p=K[j+136>>2]?0:a;i=K[j+84>>2]?0:a;k=Q(K[j+12>>2],K[j+8>>2]);a=0;while(1){if((a|0)!=(k|0)){c=a<<2;h=c+K[j+44>>2]|0;f=c+K[j+148>>2]|0;r=R(K[f>>2]-p|0);g=c+K[j+96>>2]|0;A=R(K[g>>2]-i|0);C=R(K[h>>2]);w=R(R(R(r*R(1.4019900560379028))+R(R(A*R(-3680000008898787e-20))+C))+R(.5));if(R(S(w))<R(2147483648)){c=~~w}else{c=-2147483648}K[h>>2]=(b|0)<(c|0)?b:(c|0)>0?c:0;w=R(R(R(r*R(-.7141128182411194))+R(R(C*R(1.0003000497817993))+R(A*R(-.34412500262260437))))+R(.5));if(R(S(w))<R(2147483648)){c=~~w}else{c=-2147483648}K[g>>2]=(b|0)<(c|0)?b:(c|0)>0?c:0;r=R(R(R(r*R(-7999999979801942e-21))+R(R(C*R(.9998229742050171))+R(A*R(1.7720400094985962))))+R(.5));if(R(S(r))<R(2147483648)){c=~~r}else{c=-2147483648}K[f>>2]=(b|0)<(c|0)?b:(c|0)>0?c:0;a=a+1|0;continue}break}K[l+20>>2]=1;k=0;break n}v=c>>>0>v>>>0?v:c;k=1;break n}v:{w:{c=K[l+24>>2];if(K[c>>2]!=1){break w}x:{switch(K[c+52>>2]-1|0){case 1:if(K[c+104>>2]!=2){break w}if(!(K[c+4>>2]!=1|K[c+56>>2]!=2|K[c+108>>2]!=2)){b=K[c+24>>2];h=K[c+148>>2];a=K[c+96>>2];i=K[c+44>>2];F=K[c+60>>2];q=K[c+8>>2];f=K[c+12>>2];c=Q(q,f)<<2;g=Ma(c);j=Ma(c);p=Ma(c);if(!(!g|!j|!p)){n=-1<<b^-1;o=1<<b-1;b=K[l+4>>2]&1;J=f-b|0;G=K[l>>2]&1;x=q-G|0;if(!b){c=p;b=j;f=g;break v}c=p;b=j;f=g;while(1){if((k|0)==(q|0)){break v}Oa(o,n,K[i>>2],0,0,f,b,c);k=k+1|0;c=c+4|0;b=b+4|0;f=f+4|0;i=i+4|0;continue}}Ga(g);Ga(j);Ga(p);break n}if(K[c+4>>2]!=1|K[c+56>>2]!=1|K[c+108>>2]!=1){break w}a=K[c+24>>2];b=K[c+148>>2];f=K[c+96>>2];h=K[c+44>>2];s=K[c+60>>2];g=K[c+8>>2];u=K[c+12>>2];c=Q(g,u)<<2;j=Ma(c);p=Ma(c);k=Ma(c);if(!(!j|!p|!k)){n=-1<<a^-1;o=1<<a-1;x=K[l>>2]&1;a=g-x|0;y=a&1;t=a>>>1|0;F=a&-2;a=k;i=p;c=j;while(1){if((q|0)!=(u|0)){if(x){Oa(o,n,K[h>>2],0,0,c,i,a);i=i+4|0;c=c+4|0;h=h+4|0;a=a+4|0}g=0;while(1){if(g>>>0<F>>>0){Oa(o,n,K[h>>2],K[f>>2],K[b>>2],c,i,a);Oa(o,n,K[h+4>>2],K[f>>2],K[b>>2],c+4|0,i+4|0,a+4|0);g=g+2|0;b=b+4|0;f=f+4|0;a=a+8|0;i=i+8|0;c=c+8|0;h=h+8|0;continue}break}y:{if(!y){break y}g=K[h>>2];z:{if((s|0)==(t|0)){Oa(o,n,g,0,0,c,i,a);break z}Oa(o,n,g,K[f>>2],K[b>>2],c,i,a)}a=a+4|0;i=i+4|0;c=c+4|0;h=h+4|0;if(s>>>0<=t>>>0){break y}b=b+4|0;f=f+4|0}q=q+1|0;continue}break}Ga(K[K[l+24>>2]+44>>2]);a=K[l+24>>2];K[a+44>>2]=j;Ga(K[a+96>>2]);a=K[l+24>>2];K[a+96>>2]=p;Ga(K[a+148>>2]);a=K[l+24>>2];K[a+148>>2]=k;b=K[a+8>>2];K[a+112>>2]=b;K[a+60>>2]=b;b=K[a+12>>2];K[a+116>>2]=b;K[a+64>>2]=b;b=K[a>>2];K[a+104>>2]=b;K[a+52>>2]=b;b=K[a+4>>2];K[a+108>>2]=b;K[a+56>>2]=b;K[l+20>>2]=1;k=0;break n}Ga(j);Ga(p);Ga(k);k=0;break n;case 0:break x;default:break w}}if(K[c+104>>2]!=1|K[c+4>>2]!=1|(K[c+56>>2]!=1|K[c+108>>2]!=1)){break w}b=K[c+24>>2];h=K[c+148>>2];a=K[c+96>>2];i=K[c+44>>2];n=Q(K[c+12>>2],K[c+8>>2]);c=n<<2;j=Ma(c);p=Ma(c);k=Ma(c);if(!(!j|!p|!k)){o=-1<<b^-1;q=1<<b-1;c=0;b=k;f=p;g=j;while(1){if((c|0)!=(n|0)){Oa(q,o,K[i>>2],K[a>>2],K[h>>2],g,f,b);c=c+1|0;b=b+4|0;f=f+4|0;g=g+4|0;h=h+4|0;a=a+4|0;i=i+4|0;continue}break}Ga(K[K[l+24>>2]+44>>2]);a=K[l+24>>2];K[a+44>>2]=j;Ga(K[a+96>>2]);a=K[l+24>>2];K[a+96>>2]=p;Ga(K[a+148>>2]);K[K[l+24>>2]+148>>2]=k;K[l+20>>2]=1;k=0;break n}Ga(j);Ga(p);Ga(k);k=0;break n}K[m+4>>2]=463;K[m>>2]=1336;Ka(26032,8227,m);break n}H=x>>>1|0;y=x&-2;O=J&-2;u=q<<2;while(1){if(M>>>0<O>>>0){s=c+u|0;q=b+u|0;t=f+u|0;k=i+u|0;if(G){Oa(o,n,K[i>>2],0,0,f,b,c);Oa(o,n,K[k>>2],K[a>>2],K[h>>2],t,q,s);s=s+4|0;q=q+4|0;t=t+4|0;k=k+4|0;c=c+4|0;f=f+4|0;i=i+4|0;b=b+4|0}z=0;while(1){if(y>>>0>z>>>0){Oa(o,n,K[i>>2],K[a>>2],K[h>>2],f,b,c);Oa(o,n,K[i+4>>2],K[a>>2],K[h>>2],f+4|0,b+4|0,c+4|0);Oa(o,n,K[k>>2],K[a>>2],K[h>>2],t,q,s);Oa(o,n,K[k+4>>2],K[a>>2],K[h>>2],t+4|0,q+4|0,s+4|0);z=z+2|0;h=h+4|0;a=a+4|0;s=s+8|0;q=q+8|0;t=t+8|0;k=k+8|0;c=c+8|0;b=b+8|0;f=f+8|0;i=i+8|0;continue}break}A:{if((x|0)==(y|0)){break A}z=K[i>>2];B:{if((F|0)==(H|0)){Oa(o,n,z,0,0,f,b,c);Oa(o,n,K[k>>2],0,0,t,q,s);break B}Oa(o,n,z,K[a>>2],K[h>>2],f,b,c);Oa(o,n,K[k>>2],K[a>>2],K[h>>2],t,q,s)}c=c+4|0;b=b+4|0;f=f+4|0;i=i+4|0;if(F>>>0<=H>>>0){break A}h=h+4|0;a=a+4|0}M=M+2|0;c=c+u|0;b=b+u|0;f=f+u|0;i=i+u|0;continue}break}C:{if(!(J&1)){break C}if(G){Oa(o,n,K[i>>2],0,0,f,b,c);c=c+4|0;f=f+4|0;i=i+4|0;b=b+4|0}k=0;while(1){if(k>>>0<y>>>0){Oa(o,n,K[i>>2],K[a>>2],K[h>>2],f,b,c);Oa(o,n,K[i+4>>2],K[a>>2],K[h>>2],f+4|0,b+4|0,c+4|0);k=k+2|0;h=h+4|0;a=a+4|0;c=c+8|0;b=b+8|0;f=f+8|0;i=i+8|0;continue}break}if((x|0)==(y|0)){break C}i=K[i>>2];if((F|0)==(H|0)){Oa(o,n,i,0,0,f,b,c);break C}Oa(o,n,i,K[a>>2],K[h>>2],f,b,c)}Ga(K[K[l+24>>2]+44>>2]);a=K[l+24>>2];K[a+44>>2]=g;Ga(K[a+96>>2]);a=K[l+24>>2];K[a+96>>2]=j;Ga(K[a+148>>2]);a=K[l+24>>2];K[a+148>>2]=p;b=K[a+8>>2];K[a+112>>2]=b;K[a+60>>2]=b;b=K[a+12>>2];K[a+116>>2]=b;K[a+64>>2]=b;b=K[a>>2];K[a+104>>2]=b;K[a+52>>2]=b;b=K[a+4>>2];K[a+108>>2]=b;K[a+56>>2]=b;K[l+20>>2]=1;k=0}c=K[m+64>>2];D:{if(d){break D}f=0;while(1){if((f|0)==(v|0)){break D}d=K[c+24>>2]+Q(f,52)|0;a=K[d+24>>2];if((a|0)!=8){E:{if(a>>>0<=7){g=Q(K[d+12>>2],K[d+8>>2]);j=K[d+44>>2];if(K[d+32>>2]){b=1<<a-1;h=0;while(1){if((h|0)==(g|0)){break E}a=j+(h<<2)|0;p=a;a=K[a>>2];i=a>>31<<7|a>>>25;P=p,T=Me(a<<7,i,b,0),K[P>>2]=T;h=h+1|0;continue}}a=-1<<a^-1;h=0;while(1){if((h|0)==(g|0)){break E}b=j+(h<<2)|0;p=Ne(Le(K[b>>2],0,255),ua,a,0);K[b>>2]=p;h=h+1|0;continue}}a=a-8|0;b=Q(K[d+12>>2],K[d+8>>2]);g=K[d+44>>2];h=0;if(K[d+32>>2]){while(1){if((b|0)==(h|0)){break E}j=g+(h<<2)|0;K[j>>2]=K[j>>2]>>a;h=h+1|0;continue}}while(1){if((b|0)==(h|0)){break E}j=g+(h<<2)|0;K[j>>2]=K[j>>2]>>>a;h=h+1|0;continue}}K[d+24>>2]=8}f=f+1|0;continue}}a=Q(K[c+12>>2],K[c+8>>2]);F:{if(!k){if(K[c+20>>2]==2){if(K[c+16>>2]==1){qa(K[K[c+24>>2]+44>>2],a|0);break F}if(!e){break F}b=K[c+24>>2];ha(K[b+44>>2],K[b+96>>2],a|0);break F}b=K[c+24>>2];ga(K[b+44>>2],K[b+96>>2],K[b+148>>2],a|0);break F}G:{switch(v-1|0){case 0:fa(K[K[c+24>>2]+44>>2],a|0);break F;case 2:b=K[c+24>>2];ea(K[b+44>>2],K[b+96>>2],K[b+148>>2],a|0);break F;case 3:break G;default:break F}}b=K[c+24>>2];da(K[b+44>>2],K[b+96>>2],K[b+148>>2],K[b+200>>2],a|0)}Ya(K[m+64>>2]);i=0}ra=m+8320|0;return i|0}function qc(a,b,c,d,e,f,g,h,i){var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,C=0,D=0,F=0,G=0,H=0,I=0,J=0,L=0;j=K[a>>2];a:{if(j>>>0<d>>>0|b>>>0>=d>>>0|b>>>0>=j>>>0){break a}j=K[a+4>>2];if(j>>>0<e>>>0|c>>>0>=e>>>0|c>>>0>=j>>>0){break a}A=(c>>>0)/N[a+12>>2]|0;s=K[a+8>>2];F=(b>>>0)/(s>>>0)|0;I=(Q(s,F)-b|0)+s|0;x=c;while(1){k=K[a+12>>2];j=k;j=(c|0)==(x|0)?j-((c>>>0)%(j>>>0)|0)|0:j;u=e-x|0;r=j>>>0<u>>>0?j:u;y=r&-4;v=r&3;J=r&-8;G=r&7;w=r-1|0;L=(g|0)==2&(r|0)==1;H=Q(k-j|0,s);z=(Q(x-c|0,h)<<2)+f|0;C=F;u=b;while(1){j=(b|0)==(u|0)?I:s;k=d-u|0;q=j>>>0<k>>>0?j:k;k=s-j|0;l=C<<2;j=K[l+(K[a+24>>2]+(Q(K[a+16>>2],A)<<2)|0)>>2];b:{c:{d:{e:{f:{g:{if(i){h:{i:{j:{k:{if(j){l=((H<<2)+j|0)+(k<<2)|0;j=u-b|0;if((g|0)==1){break h}m=(Q(g,j)<<2)+z|0;if((q|0)==1){break i}if(L){break j}if((g|0)!=8|q>>>0<=7){break k}if(!r){break b}o=q&-4;k=0;while(1){j=0;while(1){K[(j<<5)+m>>2]=K[(j<<2)+l>>2];n=j|1;K[(n<<5)+m>>2]=K[(n<<2)+l>>2];n=j|2;K[(n<<5)+m>>2]=K[(n<<2)+l>>2];n=j|3;K[(n<<5)+m>>2]=K[(n<<2)+l>>2];j=j+4|0;if(o>>>0>j>>>0){continue}break}if(j>>>0<q>>>0){while(1){K[(j<<5)+m>>2]=K[(j<<2)+l>>2];j=j+1|0;if((q|0)!=(j|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;k=k+1|0;if((r|0)!=(k|0)){continue}break}break b}if((g|0)!=1){if(!r){break b}p=q&-4;n=q&3;l=(Q(u-b|0,g)<<2)+z|0;o=0;while(1){l:{if(!q){break l}m=0;j=0;k=0;if(q>>>0>=4){while(1){K[(Q(g,j)<<2)+l>>2]=0;K[(Q(j|1,g)<<2)+l>>2]=0;K[(Q(j|2,g)<<2)+l>>2]=0;K[(Q(j|3,g)<<2)+l>>2]=0;j=j+4|0;k=k+4|0;if((p|0)!=(k|0)){continue}break}}if(!n){break l}while(1){K[(Q(g,j)<<2)+l>>2]=0;j=j+1|0;m=m+1|0;if((n|0)!=(m|0)){continue}break}}l=(h<<2)+l|0;o=o+1|0;if((r|0)!=(o|0)){continue}break}break b}if(!r){break b}l=q<<2;k=(u-b<<2)+z|0;o=0;if(w>>>0>=7){break g}break f}if(!r){break b}D=q&-4;p=q&3;n=0;break c}j=0;k=q&-4;if(k){while(1){K[(j<<3)+m>>2]=K[(j<<2)+l>>2];o=j|1;K[(o<<3)+m>>2]=K[(o<<2)+l>>2];o=j|2;K[(o<<3)+m>>2]=K[(o<<2)+l>>2];o=j|3;K[(o<<3)+m>>2]=K[(o<<2)+l>>2];j=j+4|0;if(k>>>0>j>>>0){continue}break}}if(j>>>0>=q>>>0){break b}o=0;k=j;n=q-j&3;if(n){while(1){K[(k<<3)+m>>2]=K[(k<<2)+l>>2];k=k+1|0;o=o+1|0;if((n|0)!=(o|0)){continue}break}}if(j-q>>>0>4294967292){break b}while(1){K[(k<<3)+m>>2]=K[(k<<2)+l>>2];j=k+1|0;K[(j<<3)+m>>2]=K[(j<<2)+l>>2];j=k+2|0;K[(j<<3)+m>>2]=K[(j<<2)+l>>2];j=k+3|0;K[(j<<3)+m>>2]=K[(j<<2)+l>>2];k=k+4|0;if((q|0)!=(k|0)){continue}break}break b}if(!r){break b}k=0;if(w>>>0>=3){while(1){K[m>>2]=K[l>>2];j=h<<2;m=j+m|0;p=l;l=s<<2;o=p+l|0;K[m>>2]=K[o>>2];m=j+m|0;o=l+o|0;K[m>>2]=K[o>>2];m=j+m|0;o=l+o|0;K[m>>2]=K[o>>2];l=l+o|0;m=j+m|0;k=k+4|0;if((y|0)!=(k|0)){continue}break}}j=0;if(!v){break b}while(1){K[m>>2]=K[l>>2];l=(s<<2)+l|0;m=(h<<2)+m|0;j=j+1|0;if((v|0)!=(j|0)){continue}break}break b}j=(j<<2)+z|0;if((q|0)!=4){if(!r){break b}m=q<<2;o=0;if(w>>>0>=3){break e}break d}if(!r){break b}o=0;if(w>>>0>=3){while(1){k=K[l+4>>2];K[j>>2]=K[l>>2];K[j+4>>2]=k;k=K[l+12>>2];K[j+8>>2]=K[l+8>>2];K[j+12>>2]=k;k=l;l=s<<2;k=k+l|0;n=K[k+12>>2];m=h<<2;j=m+j|0;K[j+8>>2]=K[k+8>>2];K[j+12>>2]=n;n=K[k+4>>2];K[j>>2]=K[k>>2];K[j+4>>2]=n;k=l+k|0;n=K[k+12>>2];j=j+m|0;K[j+8>>2]=K[k+8>>2];K[j+12>>2]=n;n=K[k+4>>2];K[j>>2]=K[k>>2];K[j+4>>2]=n;k=l+k|0;n=K[k+12>>2];j=j+m|0;K[j+8>>2]=K[k+8>>2];K[j+12>>2]=n;n=K[k+4>>2];K[j>>2]=K[k>>2];K[j+4>>2]=n;l=l+k|0;j=j+m|0;o=o+4|0;if((y|0)!=(o|0)){continue}break}}m=0;if(!v){break b}while(1){k=K[l+4>>2];K[j>>2]=K[l>>2];K[j+4>>2]=k;k=K[l+12>>2];K[j+8>>2]=K[l+8>>2];K[j+12>>2]=k;l=(s<<2)+l|0;j=(h<<2)+j|0;m=m+1|0;if((v|0)!=(m|0)){continue}break}break b}if(!j){j=Ia(1,Q(K[a+8>>2],K[a+12>>2])<<2);if(!j){return 0}K[l+(K[a+24>>2]+(Q(K[a+16>>2],A)<<2)|0)>>2]=j}l=((H<<2)+j|0)+(k<<2)|0;j=u-b|0;m:{n:{o:{p:{q:{r:{if((g|0)!=1){m=(Q(g,j)<<2)+z|0;if((q|0)==1){break r}if((g|0)!=8|q>>>0<=7){break q}if(!r){break b}o=q&-4;k=0;while(1){j=0;while(1){K[(j<<2)+l>>2]=K[(j<<5)+m>>2];n=j|1;K[(n<<2)+l>>2]=K[(n<<5)+m>>2];n=j|2;K[(n<<2)+l>>2]=K[(n<<5)+m>>2];n=j|3;K[(n<<2)+l>>2]=K[(n<<5)+m>>2];j=j+4|0;if(o>>>0>j>>>0){continue}break}if(j>>>0<q>>>0){while(1){K[(j<<2)+l>>2]=K[(j<<5)+m>>2];j=j+1|0;if((q|0)!=(j|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;k=k+1|0;if((r|0)!=(k|0)){continue}break}break b}j=(j<<2)+z|0;if((q|0)==4){break p}if(!r){break b}m=q<<2;o=0;if(w>>>0>=3){break o}break n}if(!r){break b}o=0;if(w>>>0>=3){while(1){K[l>>2]=K[m>>2];j=s<<2;l=j+l|0;k=h<<2;m=k+m|0;K[l>>2]=K[m>>2];l=j+l|0;m=k+m|0;K[l>>2]=K[m>>2];l=j+l|0;m=k+m|0;K[l>>2]=K[m>>2];l=j+l|0;m=k+m|0;o=o+4|0;if((y|0)!=(o|0)){continue}break}}j=0;if(!v){break b}while(1){K[l>>2]=K[m>>2];l=(s<<2)+l|0;m=(h<<2)+m|0;j=j+1|0;if((v|0)!=(j|0)){continue}break}break b}if(!r){break b}D=q&-4;p=q&3;n=0;break m}if(!r){break b}o=0;if(w>>>0>=3){while(1){k=K[j+4>>2];K[l>>2]=K[j>>2];K[l+4>>2]=k;k=K[j+12>>2];K[l+8>>2]=K[j+8>>2];K[l+12>>2]=k;m=h<<2;j=m+j|0;n=K[j+12>>2];k=l;l=s<<2;k=k+l|0;K[k+8>>2]=K[j+8>>2];K[k+12>>2]=n;n=K[j+4>>2];K[k>>2]=K[j>>2];K[k+4>>2]=n;j=j+m|0;n=K[j+12>>2];k=l+k|0;K[k+8>>2]=K[j+8>>2];K[k+12>>2]=n;n=K[j+4>>2];K[k>>2]=K[j>>2];K[k+4>>2]=n;j=j+m|0;n=K[j+12>>2];k=l+k|0;K[k+8>>2]=K[j+8>>2];K[k+12>>2]=n;n=K[j+4>>2];K[k>>2]=K[j>>2];K[k+4>>2]=n;j=j+m|0;l=l+k|0;o=o+4|0;if((y|0)!=(o|0)){continue}break}}m=0;if(!v){break b}while(1){k=K[j+4>>2];K[l>>2]=K[j>>2];K[l+4>>2]=k;k=K[j+12>>2];K[l+8>>2]=K[j+8>>2];K[l+12>>2]=k;j=(h<<2)+j|0;l=(s<<2)+l|0;m=m+1|0;if((v|0)!=(m|0)){continue}break}break b}while(1){k=!m;if(!k){E(l,j,m)}p=j;j=h<<2;n=p+j|0;p=l;l=s<<2;p=p+l|0;if(!k){E(p,n,m)}n=j+n|0;p=l+p|0;if(!k){E(p,n,m)}n=j+n|0;p=l+p|0;if(!k){E(p,n,m)}j=j+n|0;l=l+p|0;o=o+4|0;if((y|0)!=(o|0)){continue}break}}k=0;if(!v){break b}while(1){if(m){E(l,j,m)}j=(h<<2)+j|0;l=(s<<2)+l|0;k=k+1|0;if((v|0)!=(k|0)){continue}break}break b}while(1){s:{if(!q){break s}k=0;j=0;o=0;if(q>>>0>=4){while(1){K[(j<<2)+l>>2]=K[(Q(g,j)<<2)+m>>2];t=j|1;K[(t<<2)+l>>2]=K[(Q(g,t)<<2)+m>>2];t=j|2;K[(t<<2)+l>>2]=K[(Q(g,t)<<2)+m>>2];t=j|3;K[(t<<2)+l>>2]=K[(Q(g,t)<<2)+m>>2];j=j+4|0;o=o+4|0;if((D|0)!=(o|0)){continue}break}}if(!p){break s}while(1){K[(j<<2)+l>>2]=K[(Q(g,j)<<2)+m>>2];j=j+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;n=n+1|0;if((r|0)!=(n|0)){continue}break}break b}while(1){j=!l;if(!j){B(k,0,l)}p=k;k=h<<2;m=p+k|0;if(!j){B(m,0,l)}m=k+m|0;if(!j){B(m,0,l)}m=k+m|0;if(!j){B(m,0,l)}m=k+m|0;if(!j){B(m,0,l)}m=k+m|0;if(!j){B(m,0,l)}m=k+m|0;if(!j){B(m,0,l)}m=k+m|0;if(!j){B(m,0,l)}k=k+m|0;o=o+8|0;if((J|0)!=(o|0)){continue}break}}j=0;if(!G){break b}while(1){if(l){B(k,0,l)}k=(h<<2)+k|0;j=j+1|0;if((G|0)!=(j|0)){continue}break}break b}while(1){k=!m;if(!k){E(j,l,m)}p=l;l=s<<2;n=p+l|0;p=j;j=h<<2;p=p+j|0;if(!k){E(p,n,m)}n=l+n|0;p=j+p|0;if(!k){E(p,n,m)}n=l+n|0;p=j+p|0;if(!k){E(p,n,m)}l=l+n|0;j=j+p|0;o=o+4|0;if((y|0)!=(o|0)){continue}break}}k=0;if(!v){break b}while(1){if(m){E(j,l,m)}l=(s<<2)+l|0;j=(h<<2)+j|0;k=k+1|0;if((v|0)!=(k|0)){continue}break}break b}while(1){t:{if(!q){break t}k=0;j=0;o=0;if(q>>>0>=4){while(1){K[(Q(g,j)<<2)+m>>2]=K[(j<<2)+l>>2];t=j|1;K[(Q(t,g)<<2)+m>>2]=K[(t<<2)+l>>2];t=j|2;K[(Q(t,g)<<2)+m>>2]=K[(t<<2)+l>>2];t=j|3;K[(Q(t,g)<<2)+m>>2]=K[(t<<2)+l>>2];j=j+4|0;o=o+4|0;if((D|0)!=(o|0)){continue}break}}if(!p){break t}while(1){K[(Q(g,j)<<2)+m>>2]=K[(j<<2)+l>>2];j=j+1|0;k=k+1|0;if((p|0)!=(k|0)){continue}break}}l=(s<<2)+l|0;m=(h<<2)+m|0;n=n+1|0;if((r|0)!=(n|0)){continue}break}}C=C+1|0;u=q+u|0;if(u>>>0<d>>>0){continue}break}A=A+1|0;x=r+x|0;if(x>>>0<e>>>0){continue}break}}return 1}function Uc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0;h=ra-240|0;ra=h;r=1;a:{if(K[K[a>>2]+60>>2]|K[a+128>>2]){break a}b:{k=K[a+116>>2];c:{if(!k){d=K[a+120>>2];break c}f=K[b+16>>2];g=M[k+4>>1];d=K[a+120>>2];if(!(!d|!K[d+12>>2])){f=L[d+18|0]}d:{if(g){k=K[k>>2];while(1){i=k+Q(e,6)|0;j=M[i>>1];if(j>>>0>=f>>>0){K[h+180>>2]=f;K[h+176>>2]=j;Fa(c,1,13678,h+176|0);r=0;break a}e:{i=M[i+4>>1];if(!i|(i|0)==65535){break e}i=i-1|0;if(i>>>0<f>>>0){break e}K[h+164>>2]=f;K[h+160>>2]=i;Fa(c,1,13678,h+160|0);r=0;break a}e=e+1|0;if((g|0)!=(e|0)){continue}break}break d}if(f){break b}break c}while(1){f=f-1|0;e=0;while(1){if(M[k+Q(e,6)>>1]!=(f|0)){e=e+1|0;if((g|0)!=(e|0)){continue}break b}break}if(f){continue}break}}f:{if(!d){break f}k=K[d+12>>2];if(!k){break f}g:{d=L[d+18|0];h:{if(d){e=0;j=1;while(1){g=K[b+16>>2];f=M[k+(e<<2)>>1];if(g>>>0<=f>>>0){K[h+148>>2]=g;K[h+144>>2]=f;Fa(c,1,13678,h+144|0);j=0}e=e+1|0;if((d|0)!=(e|0)){continue}break}g=Ia(d,4);if(!g){break h}e=0;while(1){f=k+(e<<2)|0;i=L[f+2|0];i:{if(i>>>0>=2){K[h+68>>2]=i;K[h+64>>2]=e;Fa(c,1,12057,h- -64|0);j=0;break i}f=L[f+3|0];if(f>>>0>=d>>>0){K[h+128>>2]=f;Fa(c,1,12001,h+128|0);j=0;break i}l=(i|0)!=1;m=(f<<2)+g|0;if(!(l|!K[m>>2])){K[h+80>>2]=f;Fa(c,1,11490,h+80|0);j=0;break i}if(!(i|!f)){K[h+100>>2]=f;K[h+96>>2]=e;Fa(c,1,11864,h+96|0);j=0;break i}if(!(l|(e|0)==(f|0))){K[h+120>>2]=f;K[h+116>>2]=e;K[h+112>>2]=e;Fa(c,1,11900,h+112|0);j=0;break i}K[m>>2]=1}e=e+1|0;if((d|0)!=(e|0)){continue}break}j=!j;e=0;while(1){j:{f=e<<2;if(L[(f+k|0)+2|0]?K[f+g>>2]:1){e=e+1|0;if((d|0)!=(e|0)){continue}if(j&1){break j}if(K[b+16>>2]!=1){break g}e=0;while(1){if(K[(e<<2)+g>>2]){e=e+1|0;if((d|0)!=(e|0)){continue}break g}break}i=0;Fa(c,2,9216,0);e=0;if(d>>>0>=4){j=d&252;f=0;while(1){m=k+(e<<2)|0;I[m+3|0]=e;I[m+2|0]=1;m=e|1;l=k+(m<<2)|0;I[l+3|0]=m;I[l+2|0]=1;m=e|2;l=k+(m<<2)|0;I[l+3|0]=m;I[l+2|0]=1;m=e|3;l=k+(m<<2)|0;I[l+3|0]=m;I[l+2|0]=1;e=e+4|0;f=f+4|0;if((j|0)!=(f|0)){continue}break}}d=d&3;if(!d){break g}while(1){f=k+(e<<2)|0;I[f+3|0]=e;I[f+2|0]=1;e=e+1|0;i=i+1|0;if((d|0)!=(i|0)){continue}break}break g}K[h+48>>2]=e;j=1;Fa(c,1,11064,h+48|0);e=e+1|0;if((d|0)!=(e|0)){continue}}break}Ga(g);r=0;break a}g=Ia(d,4);if(g){break g}}r=0;Fa(c,1,12248,0);break a}Ga(g)}d=K[a+120>>2];k:{if(!d){break k}t=K[d+12>>2];if(!t){Ga(K[d+4>>2]);Ga(K[K[a+120>>2]+8>>2]);Ga(K[K[a+120>>2]>>2]);d=K[a+120>>2];g=K[d+12>>2];if(g){Ga(g);d=K[a+120>>2]}Ga(d);K[a+120>>2]=0;break k}m=K[b+24>>2];l:{k=L[d+18|0];m:{if(k){v=K[d>>2];j=K[d+4>>2];l=K[d+8>>2];e=0;n:{while(1){if(K[(m+Q(M[t+(e<<2)>>1],52)|0)+44>>2]){e=e+1|0;if((k|0)!=(e|0)){continue}break n}break}K[h+32>>2]=e;Fa(c,1,13840,h+32|0);r=0;break a}g=Ja(Q(k,52));if(!g){break m}i=0;while(1){d=t+(i<<2)|0;e=M[d>>1];f=Q(L[d+2|0]?L[d+3|0]:i,52)+g|0;d=m+Q(e,52)|0;e=K[d+4>>2];K[f>>2]=K[d>>2];K[f+4>>2]=e;K[f+48>>2]=K[d+48>>2];e=K[d+44>>2];K[f+40>>2]=K[d+40>>2];K[f+44>>2]=e;e=K[d+36>>2];K[f+32>>2]=K[d+32>>2];K[f+36>>2]=e;e=K[d+28>>2];K[f+24>>2]=K[d+24>>2];K[f+28>>2]=e;e=K[d+20>>2];K[f+16>>2]=K[d+16>>2];K[f+20>>2]=e;e=K[d+12>>2];K[f+8>>2]=K[d+8>>2];K[f+12>>2]=e;f=Q(i,52)+g|0;d=Ma(Q(K[d+8>>2],K[d+12>>2])<<2);K[f+44>>2]=d;if(!d){if(i){a=i&65535;while(1){Ga(K[(Q(a,52)+g|0)-8>>2]);a=a-1|0;if(a){continue}break}}Ga(g);r=0;Fa(c,1,13788,0);break a}K[f+24>>2]=L[i+l|0];K[f+32>>2]=L[i+j|0];i=i+1|0;if((k|0)!=(i|0)){continue}break}u=M[K[a+120>>2]+16>>1];n=u-1|0;while(1){d=Q(o,52)+g|0;i=Q(K[d+12>>2],K[d+8>>2]);f=t+(o<<2)|0;e=K[(m+Q(M[f>>1],52)|0)+44>>2];o:{if(!L[f+2|0]){if(!i){break o}l=K[d+44>>2];j=0;f=0;if(i>>>0>=4){q=i&-4;d=0;while(1){p=f<<2;K[p+l>>2]=K[e+p>>2];s=p|4;K[s+l>>2]=K[e+s>>2];s=p|8;K[s+l>>2]=K[e+s>>2];p=p|12;K[p+l>>2]=K[e+p>>2];f=f+4|0;d=d+4|0;if((q|0)!=(d|0)){continue}break}}d=i&3;if(!d){break o}while(1){i=f<<2;K[i+l>>2]=K[e+i>>2];f=f+1|0;j=j+1|0;if((d|0)!=(j|0)){continue}break}break o}if(!i){break o}d=L[f+3|0];j=(d<<2)+v|0;l=K[(Q(d,52)+g|0)+44>>2];f=0;if((i|0)!=1){s=i&-2;d=0;while(1){q=f<<2;p=K[q+e>>2];K[l+q>>2]=K[j+(Q(k,(p|0)>=0?(p|0)<(u|0)?p:n:0)<<2)>>2];q=q|4;p=K[q+e>>2];K[l+q>>2]=K[j+(Q(k,(p|0)>=0?(p|0)<(u|0)?p:n:0)<<2)>>2];f=f+2|0;d=d+2|0;if((s|0)!=(d|0)){continue}break}}if(!(i&1)){break o}f=f<<2;d=K[f+e>>2];K[f+l>>2]=K[j+(Q(k,(d|0)>=0?(d|0)<(u|0)?d:n:0)<<2)>>2]}o=o+1|0;if((k|0)!=(o|0)){continue}break}break l}g=Ja(Q(k,52));if(g){break l}}r=0;Fa(c,1,13788,0);break a}d=K[b+16>>2];if(d){e=0;while(1){f=K[(m+Q(e,52)|0)+44>>2];if(f){Ga(f)}e=e+1|0;if((d|0)!=(e|0)){continue}break}}Ga(m);K[b+16>>2]=k;K[b+24>>2]=g}e=K[a+116>>2];if(!e){break a}j=K[e>>2];l=M[e+4>>1];if(l){t=j+6|0;e=0;u=l-2&65535;i=1;while(1){d=K[b+16>>2];p=Q(e,6)+j|0;f=M[p>>1];p:{if(d>>>0<=f>>>0){K[h+20>>2]=d;K[h+16>>2]=f;Fa(c,2,7297,h+16|0);break p}g=M[p+4>>1];if((g+1&65535)>>>0<=1){J[(K[b+24>>2]+Q(f,52)|0)+48>>1]=M[p+2>>1];break p}k=g-1|0;m=k&65535;if(m>>>0>=d>>>0){K[h+4>>2]=d;K[h>>2]=m;Fa(c,2,7256,h);break p}q:{if(M[p+2>>1]|(f|0)==(m|0)){break q}g=K[b+24>>2];d=g+Q(f,52)|0;K[h+232>>2]=K[d+48>>2];n=K[d+44>>2];K[h+224>>2]=K[d+40>>2];K[h+228>>2]=n;n=K[d+36>>2];K[h+216>>2]=K[d+32>>2];K[h+220>>2]=n;n=K[d+28>>2];K[h+208>>2]=K[d+24>>2];K[h+212>>2]=n;n=K[d+20>>2];K[h+200>>2]=K[d+16>>2];K[h+204>>2]=n;n=K[d+12>>2];K[h+192>>2]=K[d+8>>2];K[h+196>>2]=n;n=K[d+4>>2];K[h+184>>2]=K[d>>2];K[h+188>>2]=n;n=Q(m,52);g=n+g|0;K[d+48>>2]=K[g+48>>2];o=K[g+44>>2];K[d+40>>2]=K[g+40>>2];K[d+44>>2]=o;o=K[g+36>>2];K[d+32>>2]=K[g+32>>2];K[d+36>>2]=o;o=K[g+28>>2];K[d+24>>2]=K[g+24>>2];K[d+28>>2]=o;o=K[g+20>>2];K[d+16>>2]=K[g+16>>2];K[d+20>>2]=o;o=K[g+12>>2];K[d+8>>2]=K[g+8>>2];K[d+12>>2]=o;o=K[g+4>>2];K[d>>2]=K[g>>2];K[d+4>>2]=o;g=K[h+188>>2];d=n+K[b+24>>2]|0;K[d>>2]=K[h+184>>2];K[d+4>>2]=g;K[d+48>>2]=K[h+232>>2];g=K[h+228>>2];K[d+40>>2]=K[h+224>>2];K[d+44>>2]=g;g=K[h+220>>2];K[d+32>>2]=K[h+216>>2];K[d+36>>2]=g;g=K[h+212>>2];K[d+24>>2]=K[h+208>>2];K[d+28>>2]=g;g=K[h+204>>2];K[d+16>>2]=K[h+200>>2];K[d+20>>2]=g;g=K[h+196>>2];K[d+8>>2]=K[h+192>>2];K[d+12>>2]=g;if(l>>>0<=e+1>>>0){break q}g=i;if(!(e-l&1)){g=k;d=Q(i,6)+j|0;n=M[d>>1];r:{if((n|0)!=(f|0)){g=f;if((n|0)!=(m|0)){break r}}J[d>>1]=g}g=i+1|0}if((u|0)==(e&65535)){break q}while(1){d=k;n=Q(g,6);o=n+j|0;q=M[o>>1];s:{if((q|0)!=(f|0)){d=f;if((m|0)!=(q|0)){break s}}J[o>>1]=d}d=k;n=n+t|0;o=M[n>>1];t:{if((o|0)!=(f|0)){d=f;if((m|0)!=(o|0)){break t}}J[n>>1]=d}g=g+2|0;if((l|0)!=(g&65535)){continue}break}}J[(K[b+24>>2]+Q(f,52)|0)+48>>1]=M[p+2>>1]}i=i+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}e=K[a+116>>2];j=K[e>>2]}if(j){Ga(j);e=K[a+116>>2]}Ga(e);K[a+116>>2]=0;break a}r=0;Fa(c,1,9462,0)}ra=h+240|0;return r}function dd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=R(0);l=ra-48|0;ra=l;K[a+8>>2]=1;a:{b:{d=l+40|0;c:{if((Na(b,d,2,c)|0)!=2){break c}Ha(d,l+44|0,2);if(K[l+44>>2]!=65359){break c}K[a+8>>2]=2;d=K[b+56>>2];e=d-2|0;d=K[b+60>>2]-(d>>>0<2)|0;g=K[a+224>>2];K[g>>2]=e;K[g+4>>2]=d;K[l+16>>2]=e;K[l+20>>2]=d;Fa(c,4,12732,l+16|0);f=K[a+224>>2];j=K[f>>2];e=K[f+24>>2];d=e+1|0;g=K[f+32>>2];if(d>>>0<=g>>>0){g=K[f+28>>2];break b}o=R(R(g>>>0)+R(100));if(o<R(4294967296)&o>=R(0)){d=~~o>>>0}else{d=0}K[f+32>>2]=d;g=La(K[f+28>>2],Q(d,24));if(g){K[f+28>>2]=g;e=K[f+24>>2];d=e+1|0;break b}Ga(K[f+28>>2]);K[f+32>>2]=0;K[f+24>>2]=0;K[f+28>>2]=0;Fa(c,1,3862,0)}Fa(c,1,15619,0);a=0;break a}e=Q(e,24)+g|0;K[e+16>>2]=2;K[e+8>>2]=j;K[e+12>>2]=j>>31;J[e>>1]=65359;K[f+24>>2]=d;if((Na(b,K[a+16>>2],2,c)|0)!=2){Fa(c,1,2435,0);a=0;break a}Ha(K[a+16>>2],l+40|0,2);d:{e:{g=K[l+40>>2];if((g|0)!=65424){while(1){e=24864;if(g>>>0<=65279){K[l>>2]=g;Fa(c,1,2231,l);a=0;break a}while(1){d=e;f=K[d>>2];if(f){e=d+12|0;if((f|0)!=(g|0)){continue}}break}f:{g:{if(f){break g}h=2;Fa(c,2,3810,0);e=2435;h:{i:{if((Na(b,K[a+16>>2],2,c)|0)!=2){break i}while(1){Ha(K[a+16>>2],l+44|0,2);f=24864;g=K[l+44>>2];if(g>>>0>=65280){while(1){d=f;i=K[d>>2];if(i){f=d+12|0;if((g|0)!=(i|0)){continue}}break}if(!(K[d+4>>2]&K[a+8>>2])){e=5360;break i}if(i){if((i|0)==65424){K[l+40>>2]=65424;break f}j=K[b+56>>2];f=K[a+224>>2];g=K[f+24>>2];e=g+1|0;d=K[f+32>>2];if(e>>>0<=d>>>0){d=K[f+28>>2];break h}o=R(R(d>>>0)+R(100));if(o<R(4294967296)&o>=R(0)){d=~~o>>>0}else{d=0}K[f+32>>2]=d;d=La(K[f+28>>2],Q(d,24));if(d){K[f+28>>2]=d;g=K[f+24>>2];e=g+1|0;break h}Ga(K[f+28>>2]);K[f+32>>2]=0;K[f+24>>2]=0;K[f+28>>2]=0;e=3862;break i}h=h+2|0}if((Na(b,K[a+16>>2],2,c)|0)==2){continue}break}}Fa(c,1,e,0);Fa(c,1,9810,0);a=0;break a}d=Q(g,24)+d|0;K[d+16>>2]=h;g=j-h|0;K[d+8>>2]=g;K[d+12>>2]=g>>31;J[d>>1]=0;K[f+24>>2]=e;K[l+40>>2]=i;g=24864;if((i|0)==65424){break f}while(1){d=g;f=K[d>>2];if(!f){break g}g=d+12|0;if((f|0)!=(i|0)){continue}break}}if(!(K[d+4>>2]&K[a+8>>2])){Fa(c,1,5360,0);a=0;break a}if((Na(b,K[a+16>>2],2,c)|0)!=2){Fa(c,1,2435,0);a=0;break a}Ha(K[a+16>>2],l+36|0,2);e=K[l+36>>2];if(e>>>0<=1){Fa(c,1,6037,0);a=0;break a}e=e-2|0;K[l+36>>2]=e;g=K[a+16>>2];if(N[a+20>>2]<e>>>0){g=La(g,e);if(!g){Ga(K[a+16>>2]);K[a+16>>2]=0;K[a+20>>2]=0;Fa(c,1,4936,0);a=0;break a}K[a+16>>2]=g;e=K[l+36>>2];K[a+20>>2]=e}e=Na(b,g,e,c);if((e|0)!=K[l+36>>2]){Fa(c,1,2435,0);a=0;break a}if(!(va[K[d+8>>2]](a,K[a+16>>2],e,c)|0)){Fa(c,1,2453,0);a=0;break a}j=K[b+56>>2];i=K[l+36>>2];d=K[a+224>>2];g=K[d+24>>2];h=g+1|0;e=K[d+32>>2];j:{if(h>>>0<=e>>>0){e=K[d+28>>2];break j}o=R(R(e>>>0)+R(100));if(o<R(4294967296)&o>=R(0)){e=~~o>>>0}else{e=0}K[d+32>>2]=e;e=La(K[d+28>>2],Q(e,24));if(!e){break d}K[d+28>>2]=e;g=K[d+24>>2];h=g+1|0}e=Q(g,24)+e|0;K[e+16>>2]=i+4;g=(j-i|0)-4|0;K[e+8>>2]=g;K[e+12>>2]=g>>31;J[e>>1]=f;K[d+24>>2]=h;if((Na(b,K[a+16>>2],2,c)|0)!=2){Fa(c,1,2435,0);a=0;break a}m=(f|0)==65372?1:m;k=(f|0)==65362?1:k;n=(f|0)==65361?1:n;Ha(K[a+16>>2],l+40|0,2);g=K[l+40>>2];if((g|0)!=65424){continue}}break}if(n){break e}}Fa(c,1,4748,0);a=0;break a}if(!k){Fa(c,1,4794,0);a=0;break a}if(!m){Fa(c,1,4840,0);a=0;break a}d=0;e=0;h=0;j=ra-16|0;ra=j;m=1;k:{if(!(I[a+212|0]&1)){break k}l:{f=K[a+136>>2];if(!f){break l}m:{while(1){g=K[a+140>>2]+(h<<3)|0;k=K[g>>2];if(k){i=K[g+4>>2];g=d-i|0;g=d>>>0>=g>>>0?g:0;if(d>>>0<i>>>0){f=i-d|0;k=d+k|0;while(1){if(f>>>0<4){d=5634;break m}Ha(k,j+12|0,4);d=K[j+12>>2];if((d^-1)>>>0<e>>>0){d=5608;break m}i=f-4|0;n=i>>>0<d>>>0;g=n?d-i|0:g;e=d+e|0;f=i-d|0;k=((n?0:d)+k|0)+4|0;if(d>>>0<i>>>0){continue}break}f=K[a+136>>2]}d=g}h=h+1|0;if(h>>>0<f>>>0){continue}break}if(!d){break l}m=0;Fa(c,1,3030,0);break k}m=0;Fa(c,1,d,0);break k}d=Ja(e);K[a+160>>2]=d;if(!d){m=0;Fa(c,1,4300,0);break k}K[a+148>>2]=e;h=K[a+140>>2];n:{f=K[a+136>>2];if(f){e=0;d=0;g=0;while(1){k=g<<3;n=k+h|0;i=K[n>>2];if(i){h=K[a+160>>2]+d|0;f=K[n+4>>2];o:{if(f>>>0<=e>>>0){if(f){E(h,i,f)}d=d+f|0;e=e-f|0;break o}if(e){E(h,i,e)}d=d+e|0;h=f-e|0;e=e+i|0;while(1){if(h>>>0<4){break n}Ha(e,j+8|0,4);e=e+4|0;i=K[a+160>>2]+d|0;f=h-4|0;h=K[j+8>>2];if(f>>>0<h>>>0){if(f){E(i,e,f)}d=d+f|0;e=K[j+8>>2]-f|0;break o}if(h){E(i,e,h)}h=K[j+8>>2];d=h+d|0;e=e+h|0;h=f-h|0;if(h){continue}break}e=0}Ga(K[k+K[a+140>>2]>>2]);h=K[a+140>>2];f=k+h|0;K[f>>2]=0;K[f+4>>2]=0;f=K[a+136>>2]}g=g+1|0;if(g>>>0<f>>>0){continue}break}e=K[a+148>>2];d=K[a+160>>2]}K[a+168>>2]=e;K[a+144>>2]=d;K[a+136>>2]=0;Ga(h);K[a+140>>2]=0;break k}m=0;Fa(c,1,5634,0)}ra=j+16|0;if(!m){Fa(c,1,8048,0);a=0;break a}Fa(c,4,11717,0);d=K[a+224>>2];e=K[b+56>>2];e=e-2|0;K[d+8>>2]=e;K[d+12>>2]=0;b=0;h=0;i=ra-16|0;ra=i;g=K[a+68>>2];p:{if(!g){K[a+76>>2]=1;break p}if(K[a+76>>2]){break p}d=K[a+72>>2];j=K[a+224>>2];e=K[j+40>>2];if((g|0)!=1){m=g&-2;while(1){k=(b<<3)+d|0;n=M[k>>1];f=e+Q(n,40)|0;K[f>>2]=n;K[f+8>>2]=K[f+8>>2]+1;k=M[k+8>>1];f=e+Q(k,40)|0;K[f>>2]=k;K[f+8>>2]=K[f+8>>2]+1;b=b+2|0;h=h+2|0;if((m|0)!=(h|0)){continue}break}}if(g&1){f=M[(b<<3)+d>>1];b=e+Q(f,40)|0;K[b>>2]=f;K[b+8>>2]=K[b+8>>2]+1}f=K[j+36>>2];q:{if(f){b=0;while(1){if(!K[(e+Q(b,40)|0)+8>>2]){K[i>>2]=b;Fa(c,1,9267,i);break q}b=b+1|0;if((f|0)!=(b|0)){continue}break}}f=K[j+8>>2];b=K[j+12>>2];e=0;while(1){r:{k=e<<3;m=K[K[a+224>>2]+40>>2]+Q(M[k+d>>1],40)|0;h=K[m+16>>2];if(!h){h=Ia(K[m+8>>2],24);K[m+16>>2]=h;if(!h){break r}g=K[a+68>>2];d=K[a+72>>2]}n=h;h=K[m+4>>2];j=n+Q(h,24)|0;K[j>>2]=f;K[j+4>>2]=b;k=K[(d+k|0)+4>>2];f=k+f|0;K[j+16>>2]=f;b=f>>>0<k>>>0?b+1|0:b;K[j+20>>2]=b;K[m+4>>2]=h+1;e=e+1|0;if(g>>>0>e>>>0){continue}break p}break}Fa(c,1,6845,0)}K[a+76>>2]=1;if(!K[a+68>>2]){break p}d=K[K[a+224>>2]+40>>2];b=0;while(1){c=Q(M[K[a+72>>2]+(b<<3)>>1],40);d=c+d|0;K[d+8>>2]=0;Ga(K[d+16>>2]);d=K[K[a+224>>2]+40>>2];K[(c+d|0)+16>>2]=0;b=b+1|0;if(b>>>0<N[a+68>>2]){continue}break}}ra=i+16|0;K[a+8>>2]=8;a=1;break a}Ga(K[d+28>>2]);K[d+32>>2]=0;K[d+24>>2]=0;K[d+28>>2]=0;Fa(c,1,3862,0);a=0}ra=l+48|0;return a|0}function ze(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;f=ra-160|0;ra=f;a:{if(c>>>0<=35){c=0;Fa(d,1,6058,0);break a}c=c-36|0;h=(c>>>0)/3|0;if((Q(h,3)|0)!=(c|0)){c=0;Fa(d,1,6058,0);break a}j=K[a+96>>2];c=f+156|0;Ha(b,c,2);J[a+104>>1]=K[f+156>>2];Ha(b+2|0,j+8|0,4);Ha(b+6|0,j+12|0,4);Ha(b+10|0,j,4);Ha(b+14|0,j+4|0,4);Ha(b+18|0,a+116|0,4);Ha(b+22|0,a+120|0,4);Ha(b+26|0,a+108|0,4);Ha(b+30|0,a+112|0,4);Ha(b+34|0,c,2);b:{c:{d:{c=K[f+156>>2];if(c>>>0<=16384){K[j+16>>2]=c;if((c|0)!=(h|0)){K[f+132>>2]=h;K[f+128>>2]=c;Fa(d,1,14943,f+128|0);c=0;break a}c=K[j+4>>2];g=K[j+12>>2];l=K[j+8>>2];e=K[j>>2];if(!(c>>>0<g>>>0&l>>>0>e>>>0)){K[f+120>>2]=g-c;K[f+124>>2]=0-(c>>>0>g>>>0);K[f+112>>2]=l-e;K[f+116>>2]=0-(e>>>0>l>>>0);Fa(d,1,14505,f+112|0);c=0;break a}i=K[a+116>>2];k=K[a+120>>2];if(!(k?i:0)){K[f+4>>2]=k;K[f>>2]=i;Fa(d,1,15057,f);c=0;break a}e:{n=K[a+108>>2];f:{if(n>>>0>e>>>0){break f}i=i+n|0;if(e>>>0>=(i>>>0<n>>>0?-1:i)>>>0){break f}i=K[a+112>>2];if(i>>>0>c>>>0){break f}k=i+k|0;if(c>>>0<(i>>>0>k>>>0?-1:k)>>>0){break e}}c=0;Fa(d,1,2755,0);break a}g:{if(K[a+248>>2]){break g}i=K[a+240>>2];if(!i){break g}k=K[a+244>>2];if(!k){break g}e=l-e|0;c=g-c|0;if((e|0)==(i|0)&(c|0)==(k|0)){break g}K[f+108>>2]=c;K[f+104>>2]=e;K[f+100>>2]=k;K[f+96>>2]=i;Fa(d,1,13969,f+96|0);c=0;break a}e=Ia(h,52);K[j+24>>2]=e;if(!e){break d}h:{if(!K[j+16>>2]){break h}c=f+152|0;Ha(b+36|0,c,1);h=K[f+152>>2];k=h>>>7|0;K[e+32>>2]=k;n=(h&127)+1|0;K[e+24>>2]=n;l=K[a+248>>2];Ha(b+37|0,c,1);K[e>>2]=K[f+152>>2];Ha(b+38|0,c,1);g=K[f+152>>2];K[e+4>>2]=g;c=0;i=K[e>>2];if(i-256>>>0<4294967041){h=0;break b}h=0;if(g-256>>>0<4294967041){break b}g=K[e+24>>2];if(g>>>0>31){break c}K[e+36>>2]=0;K[e+40>>2]=K[a+184>>2];h=1;if(N[j+16>>2]<=1){break h}k=l?0:k;l=l?0:n;b=b+39|0;while(1){Ha(b,f+152|0,1);i=K[f+152>>2];g=i>>>7|0;K[e+84>>2]=g;i=(i&127)+1|0;K[e+76>>2]=i;if(!(K[a+248>>2]|(L[a+212|0]&4|(i|0)==(l|0)&(g|0)==(k|0)))){K[f+84>>2]=g;K[f+80>>2]=i;K[f+76>>2]=h;K[f+72>>2]=k;K[f+68>>2]=l;K[f+64>>2]=h;Fa(d,2,14741,f- -64|0)}g=f+152|0;Ha(b+1|0,g,1);K[e+52>>2]=K[f+152>>2];Ha(b+2|0,g,1);g=K[f+152>>2];K[e+56>>2]=g;i=K[e+52>>2];if(i-256>>>0<4294967041|g-256>>>0<=4294967040){break b}g=K[e+76>>2];if(g>>>0>=32){break c}b=b+3|0;K[e+88>>2]=0;K[e+92>>2]=K[a+184>>2];e=e+52|0;h=h+1|0;if(h>>>0<N[j+16>>2]){continue}break}}c=0;h=K[a+116>>2];if(!h){break a}g=K[a+120>>2];if(!g){break a}l=0-!h|0;e=l;p=K[a+108>>2];k=K[j+8>>2]-p|0;i=h-1|0;b=k+i|0;e=k>>>0>b>>>0?e+1|0:e;b=Ne(b,e,h,0);K[a+128>>2]=b;n=0-!g|0;e=n;q=K[a+112>>2];o=K[j+12>>2]-q|0;m=o;k=g-1|0;o=o+k|0;e=m>>>0>o>>>0?e+1|0:e;e=Ne(o,e,g,0);K[a+132>>2]=e;i:{if(!(!b|!e)){if(b>>>0<=65535/(e>>>0)>>>0){break i}}K[f+20>>2]=e;K[f+16>>2]=b;Fa(d,1,14083,f+16|0);break a}o=Q(b,e);j:{if(L[a+92|0]&2){K[a+28>>2]=(K[a+28>>2]-p>>>0)/(h>>>0);K[a+32>>2]=(K[a+32>>2]-q>>>0)/(g>>>0);e=l;b=K[a+36>>2]-p|0;m=b;b=b+i|0;e=m>>>0>b>>>0?e+1|0:e;v=a,w=Ne(b,e,h,0),K[v+36>>2]=w;e=n;b=K[a+40>>2]-q|0;m=b;b=b+k|0;e=m>>>0>b>>>0?e+1|0:e;v=a,w=Ne(b,e,g,0),K[v+40>>2]=w;break j}K[a+40>>2]=e;K[a+36>>2]=b;K[a+28>>2]=0;K[a+32>>2]=0}b=Ia(o,5644);K[a+180>>2]=b;if(!b){Fa(d,1,3898,0);break a}b=Ia(K[j+16>>2],1080);K[K[a+12>>2]+5584>>2]=b;if(!K[K[a+12>>2]+5584>>2]){Fa(d,1,3898,0);break a}b=Ia(10,20);K[K[a+12>>2]+5616>>2]=b;b=K[a+12>>2];if(!K[b+5616>>2]){Fa(d,1,3898,0);break a}K[b+5624>>2]=10;b=Ia(10,20);K[K[a+12>>2]+5628>>2]=b;b=K[a+12>>2];if(!K[b+5628>>2]){Fa(d,1,3898,0);break a}K[b+5636>>2]=10;h=K[j+16>>2];k:{if(!h){break k}g=K[j+24>>2];b=0;if((h|0)!=1){l=h&-2;e=0;while(1){i=g+Q(b,52)|0;if(!K[i+32>>2]){K[(K[K[a+12>>2]+5584>>2]+Q(b,1080)|0)+1076>>2]=1<<K[i+24>>2]-1}i=b|1;k=g+Q(i,52)|0;if(!K[k+32>>2]){K[(K[K[a+12>>2]+5584>>2]+Q(i,1080)|0)+1076>>2]=1<<K[k+24>>2]-1}b=b+2|0;e=e+2|0;if((l|0)!=(e|0)){continue}break}}if(!(h&1)){break k}e=g+Q(b,52)|0;if(K[e+32>>2]){break k}K[(K[K[a+12>>2]+5584>>2]+Q(b,1080)|0)+1076>>2]=1<<K[e+24>>2]-1}if(o){b=K[a+180>>2];e=0;while(1){h=Ia(K[j+16>>2],1080);K[b+5584>>2]=h;if(!h){Fa(d,1,3898,0);break a}b=b+5644|0;e=e+1|0;if(o>>>0>e>>>0){continue}break}}b=Q(K[a+132>>2],K[a+128>>2]);K[K[a+224>>2]+36>>2]=b;b=Ia(b,40);d=K[a+224>>2];K[d+40>>2]=b;e=0;l:{if(!b){break l}e=1;if(!K[d+36>>2]){break l}d=0;while(1){m:{e=0;g=Q(d,40);b=g+b|0;K[b+20>>2]=0;K[b+28>>2]=100;h=Ia(100,24);l=K[a+224>>2];b=K[l+40>>2];K[(g+b|0)+24>>2]=h;if(!h){break m}e=1;d=d+1|0;if(d>>>0<N[l+36>>2]){continue}}break}}if(!e){break a}K[a+8>>2]=4;r=K[j+16>>2];if(r){b=K[a+112>>2];d=K[a+120>>2];c=b+Q(d,K[a+132>>2]-1|0)|0;d=c+d|0;c=c>>>0>d>>>0?-1:d;d=K[j+12>>2];c=c>>>0<d>>>0?c:d;l=c-1|0;k=0-!c|0;c=K[a+108>>2];d=K[a+116>>2];a=c+Q(d,K[a+128>>2]-1|0)|0;d=a+d|0;a=a>>>0>d>>>0?-1:d;d=K[j+8>>2];a=a>>>0<d>>>0?a:d;i=a-1|0;n=0-!a|0;a=K[j+4>>2];b=a>>>0<b>>>0?b:a;o=b-1|0;p=0-!b|0;a=K[j>>2];b=a>>>0<c>>>0?c:a;q=b-1|0;u=0-!b|0;a=K[j+24>>2];b=0;while(1){e=p;d=K[a+4>>2];c=d+o|0;j=Ne(c,c>>>0<d>>>0?e+1|0:e,d,0);K[a+20>>2]=j;e=u;h=K[a>>2];c=h+q|0;s=Ne(c,c>>>0<h>>>0?e+1|0:e,h,0);K[a+16>>2]=s;c=K[a+40>>2];g=c&31;if((c&63)>>>0>=32){e=-1<<g;m=0}else{m=-1<<g;e=m|(1<<g)-1&-1>>>32-g}g=m^-1;e=e^-1;m=e;e=k;t=d+l|0;e=t>>>0<l>>>0?e+1|0:e;e=Ne(t,e,d,0)-j|0;d=m;j=e;e=e+g|0;d=j>>>0>e>>>0?d+1|0:d;j=e;e=c&31;if((c&63)>>>0>=32){d=d>>>e|0}else{d=((1<<e)-1&d)<<32-e|j>>>e}K[a+12>>2]=d;e=n;d=h+i|0;e=d>>>0<i>>>0?e+1|0:e;d=Ne(d,e,h,0)-s|0;e=m;d=d+g|0;e=d>>>0<g>>>0?e+1|0:e;h=d;d=c&31;if((c&63)>>>0>=32){c=e>>>d|0}else{c=((1<<d)-1&e)<<32-d|h>>>d}K[a+8>>2]=c;a=a+52|0;b=b+1|0;if((r|0)!=(b|0)){continue}break}}c=1;break a}K[f+144>>2]=c;Fa(d,1,7895,f+144|0);c=0;break a}c=0;K[j+16>>2]=0;Fa(d,1,3898,0);break a}K[f+52>>2]=g;K[f+48>>2]=h;Fa(d,1,15365,f+48|0);break a}K[f+40>>2]=g;K[f+36>>2]=i;K[f+32>>2]=h;Fa(d,1,14303,f+32|0)}ra=f+160|0;return c|0}function Jc(a,b,c,d,e,f,g){var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0;j=ra+-64|0;ra=j;K[j+60>>2]=b;z=j+39|0;t=j+40|0;a:{b:{c:{d:{e:while(1){h=0;f:while(1){k=b;if((o^2147483647)<(h|0)){break d}o=h+o|0;g:{h:{i:{j:{h=b;i=L[h|0];if(i){while(1){k:{b=i&255;l:{if(!b){b=h;break l}if((b|0)!=37){break k}i=h;while(1){if(L[i+1|0]!=37){b=i;break l}h=h+1|0;n=L[i+2|0];b=i+2|0;i=b;if((n|0)==37){continue}break}}h=h-k|0;y=o^2147483647;if((h|0)>(y|0)){break d}if(a){Pa(a,k,h)}if(h){continue f}K[j+60>>2]=b;h=b+1|0;q=-1;i=I[b+1|0]-48|0;if(!(L[b+2|0]!=36|i>>>0>9)){x=1;q=i;h=b+3|0}K[j+60>>2]=h;l=0;i=I[h|0];b=i-32|0;m:{if(b>>>0>31){n=h;break m}n=h;b=1<<b;if(!(b&75913)){break m}while(1){n=h+1|0;K[j+60>>2]=n;l=b|l;i=I[h+1|0];b=i-32|0;if(b>>>0>=32){break m}h=n;b=1<<b;if(b&75913){continue}break}}n:{if((i|0)==42){b=I[n+1|0]-48|0;o:{if(!(L[n+2|0]!=36|b>>>0>9)){p:{if(!a){K[(b<<2)+e>>2]=10;b=0;break p}b=K[(b<<3)+d>>2]}p=b;b=n+3|0;i=1;break o}if(x){break j}b=n+1|0;if(!a){K[j+60>>2]=b;x=0;p=0;break n}h=K[c>>2];K[c>>2]=h+4;p=K[h>>2];i=0}x=i;K[j+60>>2]=b;if((p|0)>=0){break n}p=0-p|0;l=l|8192;break n}p=Ic(j+60|0);if((p|0)<0){break d}b=K[j+60>>2]}h=0;m=-1;u=0;q:{if(L[b|0]!=46){break q}if(L[b+1|0]==42){i=I[b+2|0]-48|0;r:{if(!(L[b+3|0]!=36|i>>>0>9)){b=b+4|0;s:{if(!a){K[(i<<2)+e>>2]=10;m=0;break s}m=K[(i<<3)+d>>2]}break r}if(x){break j}b=b+2|0;m=0;if(!a){break r}i=K[c>>2];K[c>>2]=i+4;m=K[i>>2]}K[j+60>>2]=b;u=(m|0)>=0;break q}K[j+60>>2]=b+1;m=Ic(j+60|0);b=K[j+60>>2];u=1}while(1){v=h;n=28;r=b;i=I[b|0];if(i-123>>>0<4294967238){break c}b=b+1|0;h=L[(i+Q(h,58)|0)+25215|0];if((h-1&255)>>>0<8){continue}break}K[j+60>>2]=b;t:{if((h|0)!=27){if(!h){break c}if((q|0)>=0){if(!a){K[(q<<2)+e>>2]=h;continue e}h=(q<<3)+d|0;i=K[h+4>>2];K[j+48>>2]=K[h>>2];K[j+52>>2]=i;break t}if(!a){break g}Hc(j+48|0,h,c,g);break t}if((q|0)>=0){break c}h=0;if(!a){continue f}}if(L[a|0]&32){break b}i=l&-65537;l=l&8192?i:l;q=0;w=1072;n=t;u:{v:{w:{x:{y:{z:{A:{B:{C:{D:{E:{F:{G:{H:{I:{J:{K:{r=L[r|0];h=r<<24>>24;h=v?(r&15)==3?h&-45:h:h;switch(h-88|0){case 0:case 32:break G;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 10:case 16:case 18:case 19:case 20:case 21:case 25:case 26:case 28:case 30:case 31:break h;case 9:case 13:case 14:case 15:break u;case 11:break B;case 12:case 17:break E;case 22:break I;case 23:break F;case 24:break H;case 27:break A;case 29:break J;default:break K}}L:{switch(h-65|0){case 1:case 3:break h;case 0:case 4:case 5:case 6:break u;case 2:break z;default:break L}}if((h|0)==83){break y}break h}i=K[j+48>>2];r=K[j+52>>2];w=1072;break D}h=0;M:{switch(v|0){case 0:K[K[j+48>>2]>>2]=o;continue f;case 1:K[K[j+48>>2]>>2]=o;continue f;case 2:k=K[j+48>>2];K[k>>2]=o;K[k+4>>2]=o>>31;continue f;case 3:J[K[j+48>>2]>>1]=o;continue f;case 4:I[K[j+48>>2]]=o;continue f;case 6:K[K[j+48>>2]>>2]=o;continue f;case 7:break M;default:continue f}}k=K[j+48>>2];K[k>>2]=o;K[k+4>>2]=o>>31;continue f}m=m>>>0<=8?8:m;l=l|8;h=120}b=t;k=K[j+52>>2];r=k;i=K[j+48>>2];s=i;if(i|k){A=h&32;while(1){b=b-1|0;I[b|0]=A|L[(s&15)+25744|0];v=!k&s>>>0>15|(k|0)!=0;s=(k&15)<<28|s>>>4;k=k>>>4|0;if(v){continue}break}}k=b;if(!(l&8)|!(i|r)){break C}w=(h>>>4|0)+1072|0;q=2;break C}b=t;k=K[j+52>>2];r=k;i=K[j+48>>2];s=i;if(i|k){while(1){b=b-1|0;I[b|0]=s&7|48;v=!k&s>>>0>7|(k|0)!=0;s=(k&7)<<29|s>>>3;k=k>>>3|0;if(v){continue}break}}k=b;if(!(l&8)){break C}b=t-b|0;m=(b|0)<(m|0)?m:b+1|0;break C}i=K[j+48>>2];b=K[j+52>>2];r=b;if((b|0)<0){h=0-(b+((i|0)!=0)|0)|0;r=h;i=0-i|0;K[j+48>>2]=i;K[j+52>>2]=h;q=1;w=1072;break D}if(l&2048){q=1;w=1073;break D}q=l&1;w=q?1074:1072}k=fb(i,r,t)}if((m|0)<0&u){break d}l=u?l&-65537:l;if(!((i|r)!=0|m)){k=t;m=0;break h}b=!(i|r)+(t-k|0)|0;m=(b|0)<(m|0)?m:b;break h}h=L[j+48|0];break i}h=m>>>0>=2147483647?2147483647:m;l=h;n=(h|0)!=0;b=K[j+48>>2];k=b?b:1649;b=k;N:{O:{P:{Q:{if(!(b&3)|!h){break Q}while(1){if(!L[b|0]){break P}l=l-1|0;n=(l|0)!=0;b=b+1|0;if(!(b&3)){break Q}if(l){continue}break}}if(!n){break O}if(!(!L[b|0]|l>>>0<4)){while(1){n=K[b>>2];if(((16843008-n|n)&-2139062144)!=-2139062144){break P}b=b+4|0;l=l-4|0;if(l>>>0>3){continue}break}}if(!l){break O}}while(1){if(!L[b|0]){break N}b=b+1|0;l=l-1|0;if(l){continue}break}}b=0}b=b?b-k|0:h;n=b+k|0;if((m|0)>=0){l=i;m=b;break h}l=i;m=b;if(L[n|0]){break d}break h}h=K[j+48>>2];if(h|K[j+52>>2]){break x}h=0;break i}if(m){i=K[j+48>>2];break w}h=0;Ra(a,32,p,0,l);break v}K[j+12>>2]=0;K[j+8>>2]=h;i=j+8|0;K[j+48>>2]=i;m=-1}h=0;while(1){R:{k=K[i>>2];if(!k){break R}k=Gc(j+4|0,k);if((k|0)<0){break b}if(k>>>0>m-h>>>0){break R}i=i+4|0;h=h+k|0;if(m>>>0>h>>>0){continue}}break}n=61;if((h|0)<0){break c}Ra(a,32,p,h,l);if(!h){h=0;break v}n=0;i=K[j+48>>2];while(1){k=K[i>>2];if(!k){break v}m=j+4|0;k=Gc(m,k);n=k+n|0;if(n>>>0>h>>>0){break v}Pa(a,m,k);i=i+4|0;if(h>>>0>n>>>0){continue}break}}Ra(a,32,p,h,l^8192);h=(h|0)<(p|0)?p:h;continue f}if((m|0)<0&u){break d}n=61;h=va[f|0](a,P[j+48>>3],p,m,l,h)|0;if((h|0)>=0){continue f}break c}i=L[h+1|0];h=h+1|0;continue}}if(a){break a}if(!x){break g}h=1;while(1){a=K[(h<<2)+e>>2];if(a){Hc((h<<3)+d|0,a,c,g);o=1;h=h+1|0;if((h|0)!=10){continue}break a}break}if(h>>>0>=10){o=1;break a}while(1){if(K[(h<<2)+e>>2]){break j}o=1;h=h+1|0;if((h|0)!=10){continue}break}break a}n=28;break c}I[j+39|0]=h;m=1;k=z;l=i}i=n-k|0;m=(i|0)<(m|0)?m:i;if((m|0)>(q^2147483647)){break d}n=61;b=m+q|0;h=(b|0)<(p|0)?p:b;if((y|0)<(h|0)){break c}Ra(a,32,h,b,l);Pa(a,w,q);Ra(a,48,h,b,l^65536);Ra(a,48,m,i,0);Pa(a,k,i);Ra(a,32,h,b,l^8192);b=K[j+60>>2];continue}break}break}o=0;break a}n=61}K[6585]=n}o=-1}ra=j- -64|0;return o}function ud(a,b,c,d,e,f){a=a|0;b=+b;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0;n=ra-560|0;ra=n;K[n+44>>2]=0;A(+b);h=v(1)|0;v(0)|0;a:{if((h|0)<0){t=1;y=1082;b=-b;A(+b);h=v(1)|0;v(0)|0;break a}if(e&2048){t=1;y=1085;break a}t=e&1;y=t?1088:1083;z=!t}b:{if((h&2146435072)==2146435072){h=t+3|0;Ra(a,32,c,h,e&-65537);Pa(a,y,t);d=f&32;Pa(a,b!=b?d?1170:1398:d?1275:1439,3);Ra(a,32,c,h,e^8192);m=(c|0)>(h|0)?c:h;break b}w=n+16|0;c:{d:{e:{b=Fc(b,n+44|0);b=b+b;if(b!=0){h=K[n+44>>2];K[n+44>>2]=h-1;x=f|32;if((x|0)!=97){break e}break c}x=f|32;if((x|0)==97){break c}l=K[n+44>>2];break d}l=h-29|0;K[n+44>>2]=l;b=b*268435456}k=(d|0)<0?6:d;r=(n+48|0)+((l|0)>=0?288:0)|0;h=r;while(1){d=b<4294967295&b>=0?~~b>>>0:0;K[h>>2]=d;h=h+4|0;b=(b-+(d>>>0))*1e9;if(b!=0){continue}break}f:{if((l|0)<=0){i=l;g=h;j=r;break f}j=r;i=l;while(1){o=i>>>0>=29?29:i;g=h-4|0;g:{if(j>>>0>g>>>0){break g}p=0;while(1){q=0;d=K[g>>2];i=o&31;m=p;if((o&63)>>>0>=32){p=d<<i;d=0}else{p=(1<<i)-1&d>>>32-i;d=d<<i}i=m+d|0;q=p+q|0;q=d>>>0>i>>>0?q+1|0:q;p=Ne(i,q,1e9,0);m=Le(p,ua,-1e9);d=q;q=i+m|0;K[g>>2]=q;g=g-4|0;if(j>>>0<=g>>>0){continue}break}if(!d&i>>>0<1e9){break g}j=j-4|0;K[j>>2]=p}while(1){g=h;if(j>>>0<g>>>0){h=g-4|0;if(!K[h>>2]){continue}}break}i=K[n+44>>2]-o|0;K[n+44>>2]=i;h=g;if((i|0)>0){continue}break}}if((i|0)<0){u=((k+25>>>0)/9|0)+1|0;p=(x|0)==102;while(1){d=0-i|0;m=d>>>0>=9?9:d;h:{if(g>>>0<=j>>>0){h=K[j>>2]?0:4;break h}q=1e9>>>m|0;o=-1<<m^-1;i=0;h=j;while(1){d=K[h>>2];K[h>>2]=(d>>>m|0)+i;i=Q(q,d&o);h=h+4|0;if(h>>>0<g>>>0){continue}break}h=K[j>>2]?0:4;if(!i){break h}K[g>>2]=i;g=g+4|0}i=m+K[n+44>>2]|0;K[n+44>>2]=i;j=h+j|0;d=p?r:j;g=g-d>>2>(u|0)?d+(u<<2)|0:g;if((i|0)<0){continue}break}}i=0;i:{if(g>>>0<=j>>>0){break i}i=Q(r-j>>2,9);h=10;d=K[j>>2];if(d>>>0<10){break i}while(1){i=i+1|0;h=Q(h,10);if(d>>>0>=h>>>0){continue}break}}d=(k-((x|0)!=102?i:0)|0)-((x|0)==103&(k|0)!=0)|0;if((d|0)<(Q(g-r>>2,9)-9|0)){h=(n+48|0)+((l|0)<0?-4092:-3804)|0;l=d+9216|0;d=(l|0)/9|0;m=h+(d<<2)|0;h=10;d=l+Q(d,-9)|0;if((d|0)<=7){while(1){h=Q(h,10);d=d+1|0;if((d|0)!=8){continue}break}}l=K[m>>2];u=(l>>>0)/(h>>>0)|0;o=Q(u,h);d=m+4|0;j:{if((l|0)==(o|0)&(d|0)==(g|0)){break j}l=l-o|0;k:{if(!(u&1)){b=9007199254740992;if(!(I[m-4|0]&1)|((h|0)!=1e9|j>>>0>=m>>>0)){break k}}b=9007199254740994}s=(d|0)==(g|0)?1:1.5;d=h>>>1|0;s=d>>>0>l>>>0?.5:(d|0)==(l|0)?s:1.5;if(!(L[y|0]!=45|z)){s=-s;b=-b}K[m>>2]=o;if(b+s==b){break j}d=h+o|0;K[m>>2]=d;if(d>>>0>=1e9){while(1){K[m>>2]=0;m=m-4|0;if(m>>>0<j>>>0){j=j-4|0;K[j>>2]=0}d=K[m>>2]+1|0;K[m>>2]=d;if(d>>>0>999999999){continue}break}}i=Q(r-j>>2,9);h=10;d=K[j>>2];if(d>>>0<10){break j}while(1){i=i+1|0;h=Q(h,10);if(d>>>0>=h>>>0){continue}break}}d=m+4|0;g=d>>>0<g>>>0?d:g}while(1){l=g;o=g>>>0<=j>>>0;if(!o){g=g-4|0;if(!K[g>>2]){continue}}break}l:{if((x|0)!=103){p=e&8;break l}h=k?k:1;d=(h|0)>(i|0)&(i|0)>-5;k=(d?i^-1:-1)+h|0;f=(d?-1:-2)+f|0;p=e&8;if(p){break l}g=-9;m:{if(o){break m}o=K[l-4>>2];if(!o){break m}d=10;g=0;if((o>>>0)%10|0){break m}while(1){h=g;g=g+1|0;d=Q(d,10);if(!((o>>>0)%(d>>>0)|0)){continue}break}g=h^-1}d=Q(l-r>>2,9);if((f&-33)==70){p=0;d=(d+g|0)-9|0;d=(d|0)>0?d:0;k=(d|0)>(k|0)?k:d;break l}p=0;d=((d+i|0)+g|0)-9|0;d=(d|0)>0?d:0;k=(d|0)>(k|0)?k:d}m=-1;o=k|p;if(((o?2147483645:2147483646)|0)<(k|0)){break b}q=(((o|0)!=0)+k|0)+1|0;h=f&-33;n:{if((h|0)==70){if((q^2147483647)<(i|0)){break b}g=(i|0)>0?i:0;break n}d=i>>31;g=fb((d^i)-d|0,0,w);if((w-g|0)<=1){while(1){g=g-1|0;I[g|0]=48;if((w-g|0)<2){continue}break}}u=g-2|0;I[u|0]=f;I[g-1|0]=(i|0)<0?45:43;g=w-u|0;if((g|0)>(q^2147483647)){break b}}d=g+q|0;if((d|0)>(t^2147483647)){break b}i=d+t|0;Ra(a,32,c,i,e);Pa(a,y,t);Ra(a,48,c,i,e^65536);o:{p:{q:{if((h|0)==70){h=n+16|9;f=j>>>0>r>>>0?r:j;j=f;while(1){g=fb(K[j>>2],0,h);r:{if((f|0)!=(j|0)){if(n+16>>>0>=g>>>0){break r}while(1){g=g-1|0;I[g|0]=48;if(n+16>>>0<g>>>0){continue}break}break r}if((g|0)!=(h|0)){break r}g=g-1|0;I[g|0]=48}Pa(a,g,h-g|0);j=j+4|0;if(r>>>0>=j>>>0){continue}break}if(o){Pa(a,1647,1)}if((k|0)<=0|j>>>0>=l>>>0){break q}while(1){g=fb(K[j>>2],0,h);if(g>>>0>n+16>>>0){while(1){g=g-1|0;I[g|0]=48;if(n+16>>>0<g>>>0){continue}break}}Pa(a,g,(k|0)>=9?9:k);g=k-9|0;j=j+4|0;if(l>>>0<=j>>>0){break p}d=(k|0)>9;k=g;if(d){continue}break}break p}s:{if((k|0)<0){break s}f=j>>>0<l>>>0?l:j+4|0;l=n+16|9;h=j;while(1){g=fb(K[h>>2],0,l);if((l|0)==(g|0)){g=g-1|0;I[g|0]=48}t:{if((h|0)!=(j|0)){if(n+16>>>0>=g>>>0){break t}while(1){g=g-1|0;I[g|0]=48;if(n+16>>>0<g>>>0){continue}break}break t}Pa(a,g,1);g=g+1|0;if(!(k|p)){break t}Pa(a,1647,1)}d=l-g|0;Pa(a,g,(d|0)<(k|0)?d:k);k=k-d|0;h=h+4|0;if(f>>>0<=h>>>0){break s}if((k|0)>=0){continue}break}}Ra(a,48,k+18|0,18,0);Pa(a,u,w-u|0);break o}g=k}Ra(a,48,g+9|0,9,0)}Ra(a,32,c,i,e^8192);m=(c|0)>(i|0)?c:i;break b}i=(f<<26>>31&9)+y|0;u:{if(d>>>0>11){break u}g=12-d|0;s=16;while(1){s=s*16;g=g-1|0;if(g){continue}break}if(L[i|0]==45){b=-(s+(-b-s));break u}b=b+s-s}k=K[n+44>>2];h=k>>31;g=fb((h^k)-h|0,0,w);if((w|0)==(g|0)){g=g-1|0;I[g|0]=48}r=t|2;j=f&32;l=g-2|0;I[l|0]=f+15;I[g-1|0]=(k|0)<0?45:43;g=!(e&8)&(d|0)<=0;h=n+16|0;while(1){f=h;k=S(b)<2147483647?~~b:-2147483648;I[h|0]=j|L[k+25744|0];b=(b-+(k|0))*16;h=h+1|0;if(!(g&b==0|(h-(n+16|0)|0)!=1)){I[f+1|0]=46;h=f+2|0}if(b!=0){continue}break}m=-1;g=w-l|0;f=g+r|0;if((2147483645-f|0)<(d|0)){break b}k=f;f=n+16|0;j=h-f|0;d=d?(j-2|0)<(d|0)?d+2|0:j:j;h=k+d|0;Ra(a,32,c,h,e);Pa(a,i,r);Ra(a,48,c,h,e^65536);Pa(a,f,j);Ra(a,48,d-j|0,0,0);Pa(a,l,g);Ra(a,32,c,h,e^8192);m=(c|0)>(h|0)?c:h}ra=n+560|0;return m|0}function bd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;j=ra-80|0;ra=j;K[j+76>>2]=1;a:{b:{if(K[a+128>>2]!=1|K[a+132>>2]!=1|(K[a+108>>2]|K[a+112>>2])){break b}k=K[a+100>>2];if(K[k>>2]|K[k+4>>2]|(K[k+8>>2]!=K[a+116>>2]|K[k+12>>2]!=K[a+120>>2])){break b}if(!ab(a,j+72|0,0,j+68|0,j- -64|0,j+60|0,j+56|0,j+52|0,j+76|0,b,c)){break a}c:{d:{if(!K[j+76>>2]){break d}if(!jb(a,K[j+72>>2],0,0,b,c)){break d}b=K[a+100>>2];if(K[b+16>>2]){break c}d=1;break a}Fa(c,1,8739,0);break a}e=K[b+24>>2];while(1){b=Q(h,52);Ga(K[(b+e|0)+44>>2]);c=K[a+100>>2];e=K[c+24>>2];k=b+e|0;d=K[a+232>>2];m=K[K[K[d+20>>2]>>2]+20>>2]+Q(h,76)|0;K[k+44>>2]=K[m+36>>2];K[k+36>>2]=K[(b+K[K[d+24>>2]+24>>2]|0)+36>>2];K[m+36>>2]=0;d=1;h=h+1|0;if(h>>>0<N[c+16>>2]){continue}break}break a}K[a+80>>2]=0;K[a+84>>2]=0;Ga(K[a+88>>2]);K[a+88>>2]=0;e:{if(!(K[a+28>>2]|K[a+32>>2]|K[a+36>>2]!=K[a+128>>2])){k=2;if(K[a+40>>2]==K[a+132>>2]){break e}}k=2;if(K[a+76>>2]){break e}if(!Ib(b)){break e}q=K[a+128>>2];k=Q(q,K[a+132>>2]);if(k){i=k&1;g=K[K[a+224>>2]+40>>2];f:{if((k|0)==1){k=0;break f}o=k&-2;k=0;while(1){f=g+Q(d,40)|0;l=K[f+4>>2];if(l){l=(K[f+16>>2]+Q(l,24)|0)-8|0;f=K[l>>2];n=f;p=f>>>0>k>>>0;f=K[l+4>>2];l=p&(f|0)>=(m|0)|(f|0)>(m|0);k=l?n:k;m=l?f:m}f=g+Q(d|1,40)|0;l=K[f+4>>2];if(l){l=(K[f+16>>2]+Q(l,24)|0)-8|0;f=K[l>>2];n=f;p=f>>>0>k>>>0;f=K[l+4>>2];l=p&(f|0)>=(m|0)|(f|0)>(m|0);k=l?n:k;m=l?f:m}d=d+2|0;e=e+2|0;if((o|0)!=(e|0)){continue}break}}g:{if(!i){break g}d=g+Q(d,40)|0;g=K[d+4>>2];if(!g){break g}g=(K[d+16>>2]+Q(g,24)|0)-8|0;d=K[g>>2];f=d;n=d>>>0>k>>>0;d=K[g+4>>2];g=n&(d|0)>=(m|0)|(d|0)>(m|0);k=g?f:k;m=g?d:m}k=k+2|0;m=k>>>0<2?m+1|0:m}else{k=2;m=0}f=K[a+32>>2];t=K[a+40>>2];h:{if(f>>>0>=t>>>0){break h}g=K[a+28>>2];i=K[a+36>>2];if(g>>>0>=i>>>0){break h}o=i-g&3;r=K[K[a+224>>2]+40>>2];n=g-i>>>0>4294967292;while(1){l=r+Q(Q(f,q),40)|0;d=g;e=0;if(o){while(1){h=K[(l+Q(d,40)|0)+4>>2]+h|0;d=d+1|0;e=e+1|0;if((o|0)!=(e|0)){continue}break}}if(!n){while(1){e=l+Q(d,40)|0;h=K[e+124>>2]+(K[e+84>>2]+(K[e+44>>2]+(K[e+4>>2]+h|0)|0)|0)|0;d=d+4|0;if((i|0)!=(d|0)){continue}break}}f=f+1|0;if((t|0)!=(f|0)){continue}break}}f=Ja(h<<3);K[a+88>>2]=f;if(!h|!f){break e}h=0;d=K[a+40>>2];i=K[a+32>>2];i:{if(d>>>0<=i>>>0){break i}e=K[a+36>>2];if(e>>>0<=N[a+28>>2]){break i}while(1){f=K[a+28>>2];if(f>>>0<e>>>0){t=K[K[a+224>>2]+40>>2]+Q(Q(K[a+128>>2],i),40)|0;while(1){g=t+Q(f,40)|0;d=K[g+4>>2];if(d){o=d&3;g=K[g+16>>2];l=0;j:{if(d>>>0<4){d=0;break j}r=d&-4;d=0;q=0;while(1){p=g+Q(d,24)|0;s=K[p+4>>2];e=h<<3;n=e+K[a+88>>2]|0;K[n>>2]=K[p>>2];K[n+4>>2]=s;p=g+Q(d|1,24)|0;s=K[p+4>>2];n=e+K[a+88>>2]|0;K[n+8>>2]=K[p>>2];K[n+12>>2]=s;p=g+Q(d|2,24)|0;s=K[p+4>>2];n=e+K[a+88>>2]|0;K[n+16>>2]=K[p>>2];K[n+20>>2]=s;n=g+Q(d|3,24)|0;p=K[n+4>>2];e=e+K[a+88>>2]|0;K[e+24>>2]=K[n>>2];K[e+28>>2]=p;d=d+4|0;h=h+4|0;q=q+4|0;if((r|0)!=(q|0)){continue}break}}if(o){while(1){q=g+Q(d,24)|0;r=K[q+4>>2];e=K[a+88>>2]+(h<<3)|0;K[e>>2]=K[q>>2];K[e+4>>2]=r;d=d+1|0;h=h+1|0;l=l+1|0;if((o|0)!=(l|0)){continue}break}}e=K[a+36>>2]}f=f+1|0;if(f>>>0<e>>>0){continue}break}d=K[a+40>>2]}i=i+1|0;if(i>>>0<d>>>0){continue}break}f=K[a+88>>2]}K[a+84>>2]=h;e=ra-208|0;ra=e;K[e+8>>2]=1;K[e+12>>2]=0;o=h<<3;k:{if(!o){break k}K[e+16>>2]=8;K[e+20>>2]=8;d=8;h=8;i=2;while(1){g=d;d=(h+8|0)+d|0;K[(e+16|0)+(i<<2)>>2]=d;i=i+1|0;h=g;if(d>>>0<o>>>0){continue}break}g=(f+o|0)-8|0;l:{if(g>>>0<=f>>>0){i=1;d=1;g=0;break l}i=1;d=1;while(1){m:{if((i&3)==3){Jb(f,d,e+16|0);yb(e+8|0,2);d=d+2|0;break m}o=e+16|0;h=d-1|0;n:{if(N[o+(h<<2)>>2]>=g-f>>>0){xb(f,i,K[e+12>>2],d,0,o);break n}Jb(f,d,e+16|0)}if((d|0)==1){wb(e+8|0,1);d=0;break m}wb(e+8|0,h);d=1}i=K[e+8>>2]|1;K[e+8>>2]=i;f=f+8|0;if(g>>>0>f>>>0){continue}break}g=K[e+12>>2]}xb(f,i,g,d,0,e+16|0);h=K[e+12>>2];i=K[e+8>>2];if(!(h|((d|0)!=1|(i|0)!=1))){break k}while(1){o:{if((d|0)<=1){g=Nc(i,h);yb(e+8|0,g);d=d+g|0;break o}h=e+8|0;wb(h,2);K[e+8>>2]=K[e+8>>2]^7;yb(h,1);o=f-8|0;i=e+16|0;g=d-2|0;xb(o-K[i+(g<<2)>>2]|0,K[e+8>>2],K[e+12>>2],d-1|0,1,i);wb(h,1);d=K[e+8>>2]|1;K[e+8>>2]=d;xb(o,d,K[e+12>>2],g,1,i);d=g}f=f-8|0;h=K[e+12>>2];i=K[e+8>>2];if(h|((d|0)!=1|(i|0)!=1)){continue}break}}ra=e+208|0}d=K[a+128>>2];e=0;p:{while(1){q:{if(!(!K[K[a+180>>2]+5596>>2]|((d|0)!=1|K[a+132>>2]!=1))){K[j+72>>2]=0;K[a+228>>2]=0;K[a+8>>2]=K[a+8>>2]|128;d=0;break q}d=0;if(!ab(a,j+72|0,0,j+68|0,j- -64|0,j+60|0,j+56|0,j+52|0,j+76|0,b,c)){break a}if(!K[j+76>>2]){break p}d=K[j+72>>2]}g=d+1|0;f=jb(a,d,0,0,b,c);h=Q(K[a+128>>2],K[a+132>>2]);if(!f){K[j+4>>2]=h;K[j>>2]=g;Fa(c,1,7500,j);d=0;break a}K[j+36>>2]=h;K[j+32>>2]=g;Fa(c,4,11758,j+32|0);if(!Wc(K[a+232>>2],K[K[a+100>>2]+24>>2])){d=0;break a}r:{if(!(K[a+128>>2]!=1|K[a+132>>2]!=1)){h=K[a+100>>2];f=K[a+96>>2];if(K[h>>2]!=K[f>>2]|K[h+4>>2]!=K[f+4>>2]|(K[h+8>>2]!=K[f+8>>2]|K[h+12>>2]!=K[f+12>>2])){break r}}d=K[a+180>>2]+Q(d,5644)|0;h=K[d+5596>>2];if(!h){break r}Ga(h);K[d+5596>>2]=0;K[d+5600>>2]=0}K[j+16>>2]=g;Fa(c,4,16564,j+16|0);if(!(Va(b)|ua)&K[a+8>>2]==64){break p}e=e+1|0;d=K[a+128>>2];if((e|0)==(Q(d,K[a+132>>2])|0)){break p}g=K[a+84>>2];if(!g|(g|0)!=K[a+80>>2]){continue}break}Dc(b,k,m,c)}d=Vc(a,c)}ra=j+80|0;return d|0}function cb(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;d=K[a+32>>2];a:{if(d){break a}b:{h=K[a+16>>2];if((h|0)>=6){b=K[a+8>>2];f=K[a+12>>2];d=h;break b}b=K[a+20>>2];c:{d:{if((b|0)>=5){c=K[a>>2];d=K[c>>2];K[a>>2]=c+4;g=b-4|0;break d}if((b|0)<=0){d=-1;break c}c=K[a>>2];e:{if((b|0)==1){e=-1;b=0;break e}e=-1;f=b-1|0;k=f&1;f:{if((b|0)==2){d=0;i=b;break f}j=f&-2;d=0;f=c;i=b;while(1){K[a>>2]=f+1;l=L[f|0];c=f+2|0;K[a>>2]=c;K[a+20>>2]=i-1;f=L[f+1|0];i=i-2|0;K[a+20>>2]=i;e=((255<<d^-1)&e|l<<d)&(65280<<d^-1)|f<<(d|8);d=d+16|0;f=c;m=m+2|0;if((j|0)!=(m|0)){continue}break}}if(k){f=c+1|0;K[a>>2]=f;c=L[c|0];K[a+20>>2]=i-1;e=(255<<d^-1)&e|c<<d;c=f}b=(b<<3)-8|0}K[a>>2]=c+1;d=(255<<b^-1)&e|(L[c|0]|15)<<b}K[a+20>>2]=g}b=K[a+24>>2];c=d>>>24|0;K[a+24>>2]=(c|0)==255;g=d>>>16&255;k=(g|0)==255;f=d&255;e=(f|0)==255;j=b+e|0;b=d>>>8&255;i=(b|0)==255;j=k+(j+i|0)|0;d=(h-j|0)+32|0;K[a+16>>2]=d;l=K[a+12>>2];b=c|(g|(b|f<<(e?7:8))<<(i?7:8))<<(k?7:8);f=(j-h|0)+32|0;c=f&31;if((f&63)>>>0>=32){i=b<<c;g=0}else{i=(1<<c)-1&b>>>32-c;g=b<<c}b=g|K[a+8>>2];c=i|l;f=c;K[a+8>>2]=b;K[a+12>>2]=c;if((d|0)>=6){break b}d=0;break a}e=K[a+28>>2];i=K[(e<<2)+20704>>2];g:{if((f|0)<0){d=d-1|0;c=(-1<<i^-1)<<1;i=1;e=((e|0)>=11?11:e)+1|0;break g}g=b;h=63-i|0;c=h&31;if((h&63)>>>0>=32){g=f>>>c|0}else{g=((1<<c)-1&f)<<32-c|g>>>c}c=(g&(-1<<i^-1))<<1|1;i=i+1|0;d=d-i|0;e=((e|0)<=1?1:e)-1|0}K[a+16>>2]=d;K[a+28>>2]=e;g=b;h=i&31;if((i&63)>>>0>=32){b=b<<h;g=0}else{b=(1<<h)-1&g>>>32-h|f<<h;g=g<<h}f=b;K[a+8>>2]=g;K[a+12>>2]=b;i=K[a+44>>2]|c>>31;j=K[a+40>>2]&-64|c;K[a+40>>2]=j;K[a+44>>2]=i;if((d|0)<6){d=1;break a}b=K[(e<<2)+20704>>2];h:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((e|0)>=11?11:e)+1|0;break h}k=g;h=63-b|0;c=h&31;if((h&63)>>>0>=32){k=f>>>c|0}else{k=((1<<c)-1&f)<<32-c|k>>>c}c=(k&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((e|0)<=1?1:e)-1|0}K[a+16>>2]=d;K[a+28>>2]=e;k=g;h=b&31;if((b&63)>>>0>=32){b=g<<h;k=0}else{b=(1<<h)-1&k>>>32-h|f<<h;k=k<<h}f=b;K[a+8>>2]=k;K[a+12>>2]=b;b=c>>31<<7|c>>>25|i;h=b;j=j&-8065|c<<7;K[a+40>>2]=j;K[a+44>>2]=b;if((d|0)<6){d=2;break a}b=K[(e<<2)+20704>>2];i:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((e|0)>=11?11:e)+1|0;break i}g=k;i=63-b|0;c=i&31;if((i&63)>>>0>=32){g=f>>>c|0}else{g=((1<<c)-1&f)<<32-c|g>>>c}c=(g&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((e|0)<=1?1:e)-1|0}K[a+16>>2]=d;K[a+28>>2]=e;l=k;g=b&31;if((b&63)>>>0>=32){i=k<<g;g=0}else{i=(1<<g)-1&l>>>32-g|f<<g;g=l<<g}K[a+8>>2]=g;f=i;K[a+12>>2]=f;b=c>>31<<14|c>>>18|h;i=b;k=j&-1032193|c<<14;K[a+40>>2]=k;K[a+44>>2]=b;if((d|0)<6){d=3;break a}b=K[(e<<2)+20704>>2];j:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((e|0)>=11?11:e)+1|0;break j}j=g;h=63-b|0;c=h&31;if((h&63)>>>0>=32){j=f>>>c|0}else{j=((1<<c)-1&f)<<32-c|j>>>c}c=(j&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((e|0)<=1?1:e)-1|0}K[a+16>>2]=d;K[a+28>>2]=e;j=g;h=b&31;if((b&63)>>>0>=32){b=g<<h;g=0}else{b=(1<<h)-1&j>>>32-h|f<<h;g=j<<h}f=b;K[a+8>>2]=g;K[a+12>>2]=b;b=c>>31<<21|c>>>11|i;j=b;k=k&-132120577|c<<21;K[a+40>>2]=k;K[a+44>>2]=b;if((d|0)<6){d=4;break a}b=K[(e<<2)+20704>>2];k:{if((f|0)<0){c=(-1<<b^-1)<<1;b=1;h=((e|0)>=11?11:e)+1|0;d=d-1|0;break k}h=g;i=63-b|0;c=i&31;if((i&63)>>>0>=32){i=f>>>c|0}else{i=((1<<c)-1&f)<<32-c|h>>>c}c=(i&(-1<<b^-1))<<1|1;h=((e|0)<=1?1:e)-1|0;b=b+1|0;d=d-b|0}K[a+16>>2]=d;K[a+28>>2]=h;i=g;e=b&31;if((b&63)>>>0>=32){b=g<<e;g=0}else{b=(1<<e)-1&i>>>32-e|f<<e;g=i<<e}K[a+8>>2]=g;f=b;K[a+12>>2]=b;b=j&-4|(c>>31<<28|c>>>4);j=b;k=k&268435455|c<<28;K[a+40>>2]=k;K[a+44>>2]=b;if((d|0)<6){d=5;break a}b=K[(h<<2)+20704>>2];l:{if((f|0)<0){e=(-1<<b^-1)<<1;b=1;h=((h|0)>=11?11:h)+1|0;i=d-1|0;break l}i=g;e=63-b|0;c=e&31;if((e&63)>>>0>=32){i=f>>>c|0}else{i=((1<<c)-1&f)<<32-c|i>>>c}e=(i&(-1<<b^-1))<<1|1;h=((h|0)<=1?1:h)-1|0;b=b+1|0;i=d-b|0}K[a+16>>2]=i;K[a+28>>2]=h;d=g;c=b&31;if((b&63)>>>0>=32){b=d<<c;g=0}else{b=(1<<c)-1&d>>>32-c|f<<c;g=d<<c}c=b;K[a+8>>2]=g;K[a+12>>2]=b;b=j&-505|e<<3;l=b;K[a+40>>2]=k;K[a+44>>2]=b;d=6;if((i|0)<6){break a}b=K[(h<<2)+20704>>2];m:{if((c|0)<0){e=(-1<<b^-1)<<1;b=1;h=((h|0)>=11?11:h)+1|0;d=i-1|0;break m}d=g;e=63-b|0;f=e&31;if((e&63)>>>0>=32){f=c>>>f|0}else{f=((1<<f)-1&c)<<32-f|d>>>f}e=(f&(-1<<b^-1))<<1|1;h=((h|0)<=1?1:h)-1|0;b=b+1|0;d=i-b|0}K[a+16>>2]=d;K[a+28>>2]=h;j=g;f=b&31;if((b&63)>>>0>=32){i=g<<f;g=0}else{i=(1<<f)-1&j>>>32-f|c<<f;g=j<<f}K[a+8>>2]=g;f=i;K[a+12>>2]=f;i=k;b=l&-64513|e<<10;k=b;K[a+40>>2]=i;K[a+44>>2]=b;if((d|0)<6){d=7;break a}b=K[(h<<2)+20704>>2];n:{if((f|0)<0){d=d-1|0;c=(-1<<b^-1)<<1;b=1;e=((h|0)>=11?11:h)+1|0;break n}j=g;e=63-b|0;c=e&31;if((e&63)>>>0>=32){j=f>>>c|0}else{j=((1<<c)-1&f)<<32-c|j>>>c}c=(j&(-1<<b^-1))<<1|1;b=b+1|0;d=d-b|0;e=((h|0)<=1?1:h)-1|0}K[a+16>>2]=d;K[a+28>>2]=e;d=g;e=b&31;if((b&63)>>>0>=32){b=d<<e;g=0}else{b=(1<<e)-1&d>>>32-e|f<<e;g=d<<e}K[a+8>>2]=g;K[a+12>>2]=b;K[a+40>>2]=i;K[a+44>>2]=k&-8257537|c<<17;d=8}K[a+32>>2]=d-1;f=K[a+44>>2];b=f>>>7|0;c=K[a+40>>2];K[a+40>>2]=(f&127)<<25|c>>>7;K[a+44>>2]=b;return c&127}function ic(a,b,c,d,e,f,g,h,i){var j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;p=ra-32|0;ra=p;K[p+24>>2]=f;r=K[(Q(K[d+28>>2],76)+b|0)+28>>2]+Q(K[d+32>>2],152)|0;a:{if(!(K[d+40>>2]|!K[r+24>>2])){k=r+28|0;while(1){b:{if(ec(k)){break b}b=K[d+36>>2];if(b>>>0>=N[k+24>>2]/40>>>0){Fa(i,1,2799,0);break a}b=K[k+20>>2]+Q(b,40)|0;vc(K[b+32>>2]);vc(K[b+36>>2]);o=Q(K[b+20>>2],K[b+16>>2]);if(!o){break b}b=K[b+24>>2];if(o>>>0>=8){q=o&-8;j=0;while(1){K[b+516>>2]=0;K[b+520>>2]=0;K[b+448>>2]=0;K[b+452>>2]=0;K[b+380>>2]=0;K[b+384>>2]=0;K[b+312>>2]=0;K[b+316>>2]=0;K[b+244>>2]=0;K[b+248>>2]=0;K[b+176>>2]=0;K[b+180>>2]=0;K[b+108>>2]=0;K[b+112>>2]=0;K[b+40>>2]=0;K[b+44>>2]=0;b=b+544|0;j=j+8|0;if((q|0)!=(j|0)){continue}break}}j=0;o=o&7;if(!o){break b}while(1){K[b+40>>2]=0;K[b+44>>2]=0;b=b+68|0;j=j+1|0;if((o|0)!=(j|0)){continue}break}}k=k+36|0;n=n+1|0;if(n>>>0<N[r+24>>2]){continue}break}}q=f;c:{if(!(L[c|0]&2)){break c}if(h>>>0<=5){Fa(i,2,4159,0);break c}if(!(L[f|0]==255&L[f+1|0]==145)){Fa(i,2,4201,0);break c}q=f+6|0;K[p+24>>2]=q}l=Ja(20);if(!l){break a}d:{if(I[a+108|0]&1){q=K[a+40>>2];o=a+44|0;h=a+40|0;break d}if(L[c+5640|0]&2){q=K[c+5168>>2];o=c+5180|0;h=c+5168|0;break d}K[p+28>>2]=(f+h|0)-q;o=p+28|0;h=p+24|0}a=K[o>>2];K[l+12>>2]=0;K[l+16>>2]=0;K[l+8>>2]=q;K[l>>2]=q;K[l+4>>2]=a+q;if(!Wa(l,1)){xc(l);a=yc(l);kb(l);a=a+q|0;b=K[h>>2];d=K[o>>2];if(L[c|0]&4){if(b+(d-a|0)>>>0<=1){Fa(i,1,4385,0);break a}if(!(L[a|0]==255&L[a+1|0]==146)){Fa(i,1,4364,0);break a}a=a+2|0}a=a-b|0;K[o>>2]=d-a;K[h>>2]=a+b;K[e>>2]=0;K[g>>2]=K[p+24>>2]-f;x=1;break a}if(K[r+24>>2]){t=r+28|0;while(1){a=K[d+36>>2];b=K[t+20>>2];e:{if(ec(t)){break e}u=b+Q(a,40)|0;y=Q(K[u+20>>2],K[u+16>>2]);if(!y){break e}k=K[u+24>>2];v=0;while(1){f:{g:{if(!K[k+40>>2]){a=tc(l,K[u+32>>2],v,K[d+40>>2]+1|0);break g}a=Wa(l,1)}if(!a){K[k+36>>2]=0;break f}if(!K[k+40>>2]){b=0;while(1){a=b;b=b+1|0;if(!tc(l,K[u+36>>2],v,a)){continue}break}b=K[t+28>>2];K[k+32>>2]=3;K[k+24>>2]=b;K[k+28>>2]=(b-a|0)+1}a=1;h:{if(!Wa(l,1)){break h}a=2;if(!Wa(l,1)){break h}a=Wa(l,2);if((a|0)!=3){a=a+3|0;break h}a=Wa(l,5);if((a|0)!=31){a=a+6|0;break h}a=Wa(l,7)+37|0}K[k+36>>2]=a;b=0;while(1){a=b;b=b+1|0;if(Wa(l,1)){continue}break}K[k+32>>2]=a+K[k+32>>2];i:{a=K[k+40>>2];j:{k:{if(!a){a=K[(K[c+5584>>2]+Q(K[d+28>>2],1080)|0)+16>>2];if(!K[k+48>>2]){b=La(K[k>>2],240);if(!b){break i}K[k>>2]=b;B(b+Q(K[k+48>>2],24)|0,0,240);K[k+48>>2]=10}j=K[k>>2];ob(j);b=a&4?1:a&1?10:109;a=0;break k}b=K[k>>2];n=a-1|0;j=b+Q(n,24)|0;if(K[j+4>>2]!=K[j+12>>2]){break j}n=K[(K[c+5584>>2]+Q(K[d+28>>2],1080)|0)+16>>2];j=K[k+48>>2];if(j>>>0<a+1>>>0){j=j+10|0;b=La(b,Q(j,24));if(!b){break i}K[k>>2]=b;B(b+Q(K[k+48>>2],24)|0,0,240);K[k+48>>2]=j;b=K[k>>2]}j=Q(a,24)+b|0;ob(j);b=1;l:{if(n&4){break l}b=109;if(!(n&1)){break l}b=K[j-12>>2];b=(b|0)==1?2:(b|0)==10?2:1}}n=a;K[j+12>>2]=b}a=K[k+36>>2];if(L[(K[c+5584>>2]+Q(K[d+28>>2],1080)|0)+16|0]&64){while(1){m=Q(n,24);s=n?a:1;K[(m+K[k>>2]|0)+16>>2]=s;w=K[k+32>>2];j=0;b=a;if(s>>>0>=2){while(1){j=j+1|0;s=b>>>0>3;b=b>>>1|0;if(s){continue}break}}b=j+w|0;if(b>>>0>=33){K[p+16>>2]=b;Fa(i,1,15498,p+16|0);break i}j=Wa(l,b);b=K[k>>2];m=m+b|0;K[m+20>>2]=j;a=a-K[m+16>>2]|0;if((a|0)<=0){break f}j=K[(K[c+5584>>2]+Q(K[d+28>>2],1080)|0)+16>>2];m=K[k+48>>2];if(m>>>0<n+2>>>0){m=m+10|0;b=La(b,Q(m,24));if(!b){break i}K[k>>2]=b;B(b+Q(K[k+48>>2],24)|0,0,240);K[k+48>>2]=m;b=K[k>>2]}n=n+1|0;b=b+Q(n,24)|0;ob(b);if(j&4){K[b+12>>2]=1;continue}if(j&1){j=b;b=K[b-12>>2];K[j+12>>2]=(b|0)==1?2:(b|0)==10?2:1}else{K[b+12>>2]=109}continue}}while(1){m=Q(n,24);j=m+K[k>>2]|0;b=K[j+12>>2]-K[j+4>>2]|0;b=(a|0)>(b|0)?b:a;K[j+16>>2]=b;s=K[k+32>>2];j=0;if(b>>>0>=2){while(1){j=j+1|0;w=b>>>0>3;b=b>>>1|0;if(w){continue}break}}b=j+s|0;if(b>>>0>=33){K[p>>2]=b;Fa(i,1,15498,p);break i}j=Wa(l,b);b=K[k>>2];m=m+b|0;K[m+20>>2]=j;a=a-K[m+16>>2]|0;if((a|0)<=0){break f}j=K[(K[c+5584>>2]+Q(K[d+28>>2],1080)|0)+16>>2];m=K[k+48>>2];if(m>>>0<n+2>>>0){m=m+10|0;b=La(b,Q(m,24));if(!b){break i}K[k>>2]=b;B(b+Q(K[k+48>>2],24)|0,0,240);K[k+48>>2]=m;b=K[k>>2]}n=n+1|0;b=b+Q(n,24)|0;ob(b);if(j&4){K[b+12>>2]=1;continue}if(j&1){j=b;b=K[b-12>>2];K[j+12>>2]=(b|0)==1?2:(b|0)==10?2:1}else{K[b+12>>2]=109}continue}}kb(l);break a}k=k+68|0;v=v+1|0;if((y|0)!=(v|0)){continue}break}}t=t+36|0;z=z+1|0;if(z>>>0<N[r+24>>2]){continue}break}}if(!xc(l)){kb(l);break a}a=yc(l);kb(l);b=a+q|0;a=K[h>>2];if(L[c|0]&4){if(a+(K[o>>2]-b|0)>>>0<=1){Fa(i,1,4385,0);break a}if(!(L[b|0]==255&L[b+1|0]==146)){Fa(i,1,4364,0);break a}b=b+2|0}if((a|0)==(b|0)){break a}K[o>>2]=K[o>>2]+(a-b|0);K[h>>2]=b;x=1;K[e>>2]=1;K[g>>2]=K[p+24>>2]-f}ra=p+32|0;return x}function Hb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;o=Q(c,5);j=(c<<2)+b|0;e=K[a>>2];f=K[a+12>>2]<<5;h=e+f|0;l=e-f|0;e=K[a+16>>2];k=K[a+28>>2];i=K[a+20>>2];q=K[a+8>>2];a:{b:{if(h&15|(b&15|d>>>0<8)){if(e>>>0>=i>>>0){break a}c:{switch(d-1|0){case 1:f=e+1|0;if(i-e&1){g=h+(e<<6)|0;e=(e<<2)+b|0;O[g>>2]=O[e>>2];O[g+4>>2]=O[e+(c<<2)>>2];e=f}if((f|0)==(i|0)){break a}while(1){f=h+(e<<6)|0;g=(e<<2)+b|0;O[f>>2]=O[g>>2];r=f;f=c<<2;O[r+4>>2]=O[f+g>>2];g=e+1|0;j=h+(g<<6)|0;g=(g<<2)+b|0;O[j>>2]=O[g>>2];O[j+4>>2]=O[f+g>>2];e=e+2|0;if((i|0)!=(e|0)){continue}break};break a;case 0:break c;default:break b}}f=e;j=i-e&3;if(j){while(1){O[h+(f<<6)>>2]=O[(f<<2)+b>>2];f=f+1|0;g=g+1|0;if((j|0)!=(g|0)){continue}break}}if(e-i>>>0>4294967292){break a}while(1){O[h+(f<<6)>>2]=O[(f<<2)+b>>2];e=f+1|0;O[h+(e<<6)>>2]=O[(e<<2)+b>>2];e=f+2|0;O[h+(e<<6)>>2]=O[(e<<2)+b>>2];e=f+3|0;O[h+(e<<6)>>2]=O[(e<<2)+b>>2];f=f+4|0;if((i|0)!=(f|0)){continue}break}break a}if(e>>>0>=i>>>0){break a}n=c<<4;m=Q(c,12);s=c<<3;while(1){f=h+(e<<6)|0;g=(e<<2)+b|0;O[f>>2]=O[g>>2];p=c<<2;O[f+4>>2]=O[p+g>>2];O[f+8>>2]=O[g+s>>2];O[f+12>>2]=O[g+m>>2];O[f+16>>2]=O[g+n>>2];g=e+o<<2;O[f+20>>2]=O[g+b>>2];g=g+j|0;O[f+24>>2]=O[g>>2];O[f+28>>2]=O[g+p>>2];e=e+1|0;if((i|0)!=(e|0)){continue}break}break a}n=c<<4;m=Q(c,12);s=c<<3;p=(d|0)==5;r=(d|0)==7;while(1){f=h+(e<<6)|0;g=(e<<2)+b|0;O[f>>2]=O[g>>2];t=c<<2;O[f+4>>2]=O[g+t>>2];O[f+8>>2]=O[g+s>>2];d:{if((d|0)==3){break d}O[f+12>>2]=O[g+m>>2];if((d|0)==4){break d}O[f+16>>2]=O[g+n>>2];if(p){break d}g=e+o<<2;O[f+20>>2]=O[g+b>>2];if((d|0)==6){break d}g=g+j|0;O[f+24>>2]=O[g>>2];if(r){break d}O[f+28>>2]=O[g+t>>2]}e=e+1|0;if((i|0)!=(e|0)){continue}break}}b=(q<<2)+b|0;i=b+(c<<2)|0;e=K[a+24>>2];h=l+32|0;e:{if(h&15|(b&15|d>>>0<8)){if(e>>>0>=k>>>0){break e}f:{switch(d-1|0){case 1:a=e+1|0;if(k-e&1){d=h+(e<<6)|0;e=b+(e<<2)|0;O[d>>2]=O[e>>2];O[d+4>>2]=O[e+(c<<2)>>2];e=a}if((a|0)==(k|0)){break e}while(1){a=h+(e<<6)|0;d=b+(e<<2)|0;O[a>>2]=O[d>>2];f=a;a=c<<2;O[f+4>>2]=O[a+d>>2];d=e+1|0;f=h+(d<<6)|0;d=b+(d<<2)|0;O[f>>2]=O[d>>2];O[f+4>>2]=O[a+d>>2];e=e+2|0;if((k|0)!=(e|0)){continue}break};break e;case 0:f=e;a=k-e&3;if(a){g=0;while(1){O[h+(f<<6)>>2]=O[b+(f<<2)>>2];f=f+1|0;g=g+1|0;if((a|0)!=(g|0)){continue}break}}if(e-k>>>0>4294967292){break e}while(1){O[h+(f<<6)>>2]=O[b+(f<<2)>>2];a=f+1|0;O[h+(a<<6)>>2]=O[b+(a<<2)>>2];a=f+2|0;O[h+(a<<6)>>2]=O[b+(a<<2)>>2];a=f+3|0;O[h+(a<<6)>>2]=O[b+(a<<2)>>2];f=f+4|0;if((k|0)!=(f|0)){continue}break};break e;default:break f}}g=c<<4;j=Q(c,12);l=c<<3;q=(d|0)==5;n=(d|0)==7;while(1){a=h+(e<<6)|0;f=b+(e<<2)|0;O[a>>2]=O[f>>2];m=c<<2;O[a+4>>2]=O[m+f>>2];O[a+8>>2]=O[f+l>>2];g:{if((d|0)==3){break g}O[a+12>>2]=O[f+j>>2];if((d|0)==4){break g}O[a+16>>2]=O[f+g>>2];if(q){break g}f=e+o<<2;O[a+20>>2]=O[f+b>>2];if((d|0)==6){break g}f=f+i|0;O[a+24>>2]=O[f>>2];if(n){break g}O[a+28>>2]=O[f+m>>2]}e=e+1|0;if((k|0)!=(e|0)){continue}break}break e}if(e>>>0>=k>>>0){break e}f=c<<4;g=Q(c,12);j=c<<3;while(1){a=h+(e<<6)|0;d=b+(e<<2)|0;O[a>>2]=O[d>>2];l=c<<2;O[a+4>>2]=O[l+d>>2];O[a+8>>2]=O[d+j>>2];O[a+12>>2]=O[d+g>>2];O[a+16>>2]=O[d+f>>2];d=e+o<<2;O[a+20>>2]=O[d+b>>2];d=d+i|0;O[a+24>>2]=O[d>>2];O[a+28>>2]=O[d+l>>2];e=e+1|0;if((k|0)!=(e|0)){continue}break}}}function Xb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;d=ra-176|0;ra=d;a:{if(b&384){Sa(5906,11,c);break a}b:{if(!(b&1)){break b}e=K[a+96>>2];if(!e){break b}f=ra-80|0;ra=f;Sa(1755,13,c);I[f+79|0]=0;I[f+78|0]=9;g=K[e+4>>2];K[f+68>>2]=K[e>>2];K[f+72>>2]=g;j=f+78|0;K[f+64>>2]=j;Ka(c,7483,f- -64|0);g=K[e+12>>2];K[f+52>>2]=K[e+8>>2];K[f+56>>2]=g;K[f+48>>2]=j;Ka(c,7466,f+48|0);K[f+36>>2]=K[e+16>>2];K[f+32>>2]=j;Ka(c,7240,f+32|0);if(!(!K[e+24>>2]|!K[e+16>>2])){while(1){l=f+78|0;K[f+16>>2]=l;K[f+20>>2]=m;Ka(c,1787,f+16|0);j=K[e+24>>2];g=ra-48|0;ra=g;I[g+46|0]=9;I[g+47|0]=0;I[g+45|0]=9;n=Q(m,52)+j|0;j=K[n+4>>2];K[g+36>>2]=K[n>>2];K[g+40>>2]=j;j=g+45|0;K[g+32>>2]=j;Ka(c,7172,g+32|0);K[g+20>>2]=K[n+24>>2];K[g+16>>2]=j;Ka(c,7418,g+16|0);K[g+4>>2]=K[n+32>>2];K[g>>2]=j;Ka(c,7391,g);ra=g+48|0;K[f>>2]=l;Ka(c,1665,f);m=m+1|0;if(m>>>0<N[e+16>>2]){continue}break}}Sa(1673,2,c);ra=f+80|0}if(!(!(b&2)|!K[a+96>>2])){Sa(1894,36,c);e=K[a+112>>2];K[d+160>>2]=K[a+108>>2];K[d+164>>2]=e;Ka(c,2388,d+160|0);e=K[a+120>>2];K[d+144>>2]=K[a+116>>2];K[d+148>>2]=e;Ka(c,2354,d+144|0);e=K[a+132>>2];K[d+128>>2]=K[a+128>>2];K[d+132>>2]=e;Ka(c,2372,d+128|0);Wb(K[a+12>>2],K[K[a+96>>2]+16>>2],c);Sa(1673,2,c)}c:{if(!(b&8)|!K[a+96>>2]){break c}e=Q(K[a+128>>2],K[a+132>>2]);if(!e){break c}h=K[a+180>>2];while(1){Wb(h,K[K[a+96>>2]+16>>2],c);h=h+5644|0;k=k+1|0;if((e|0)!=(k|0)){continue}break}}if(!(b&16)){break a}i=K[a+224>>2];Sa(1856,37,c);e=K[i>>2];b=K[i+4>>2];a=K[i+12>>2];K[d+120>>2]=K[i+8>>2];K[d+124>>2]=a;K[d+112>>2]=e;K[d+116>>2]=b;Ka(c,5693,d+112|0);Sa(1838,17,c);if(!(!K[i+28>>2]|!K[i+24>>2])){h=0;while(1){a=K[i+28>>2]+Q(h,24)|0;g=M[a>>1];e=K[a+8>>2];b=K[a+12>>2];K[d+96>>2]=K[a+16>>2];K[d+88>>2]=e;K[d+92>>2]=b;K[d+80>>2]=g;Ka(c,7360,d+80|0);h=h+1|0;if(h>>>0<N[i+24>>2]){continue}break}}Sa(1671,4,c);j=K[i+40>>2];d:{if(!j){break d}g=K[i+36>>2];if(!g){break d}k=0;h=0;while(1){a=j+Q(h,40)|0;e=K[a+4>>2];e:{if(!e){break e}l=K[a+16>>2];if(!l){break e}b=K[l>>2];a=K[l+4>>2];if((a|0)<0){a=1}else{a=!b&(a|0)<=0}if(a|(K[l+8>>2]|K[l+12>>2])){break e}if(Oc(1402)){break d}}k=e+k|0;h=h+1|0;if((g|0)!=(h|0)){continue}break}if(!k){break d}Sa(1821,16,c);if(K[i+36>>2]){k=K[i+40>>2];n=0;while(1){f=Q(n,40);l=K[(f+k|0)+4>>2];K[d+68>>2]=l;K[d+64>>2]=n;Ka(c,7430,d- -64|0);k=K[i+40>>2];f:{if(!l){break f}h=0;if(!K[(f+k|0)+16>>2]){break f}while(1){m=K[(f+K[i+40>>2]|0)+16>>2]+Q(h,24)|0;j=K[m>>2];g=K[m+4>>2];e=K[m+8>>2];b=K[m+12>>2];a=K[m+20>>2];K[d+56>>2]=K[m+16>>2];K[d+60>>2]=a;K[d+48>>2]=e;K[d+52>>2]=b;K[d+40>>2]=j;K[d+44>>2]=g;K[d+32>>2]=h;Ka(c,10901,d+32|0);h=h+1|0;if((l|0)!=(h|0)){continue}break}k=K[i+40>>2]}a=f+k|0;g:{if(!K[a+24>>2]){break g}h=0;if(!K[a+20>>2]){break g}while(1){a=K[(f+k|0)+24>>2]+Q(h,24)|0;g=M[a>>1];e=K[a+8>>2];b=K[a+12>>2];K[d+16>>2]=K[a+16>>2];K[d+8>>2]=e;K[d+12>>2]=b;K[d>>2]=g;Ka(c,7360,d);h=h+1|0;k=K[i+40>>2];if(h>>>0<N[(f+k|0)+20>>2]){continue}break}}n=n+1|0;if(n>>>0<N[i+36>>2]){continue}break}}Sa(1671,4,c)}Sa(1673,2,c)}ra=d+176|0}function He(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=ra-128|0;ra=e;K[e+120>>2]=0;a:{if((c|0)!=8){Fa(d,1,4010,0);Fa(d,1,4010,0);break a}Ha(b,a+228|0,2);Ha(b+2|0,e+124|0,4);Ha(b+6|0,e+116|0,1);Ha(b+7|0,e+120|0,1);c=K[a+228>>2];i=K[a+128>>2];if(c>>>0>=Q(i,K[a+132>>2])>>>0){K[e+112>>2]=c;Fa(d,1,7806,e+112|0);break a}h=K[a+180>>2]+Q(c,5644)|0;j=(c>>>0)/(i>>>0)|0;b=K[e+116>>2];b:{f=K[a+44>>2];if((f|0)>=0&(c|0)!=(f|0)){break b}f=K[h+5588>>2]+1|0;if((f|0)==(b|0)){break b}K[e+104>>2]=f;K[e+100>>2]=b;K[e+96>>2]=c;Fa(d,1,7830,e+96|0);f=0;break a}K[h+5588>>2]=b;c:{b=K[e+124>>2];if(b-1>>>0<=12){if((b|0)!=12){break c}K[e+64>>2]=12;Fa(d,2,11827,e- -64|0);b=K[e+124>>2]}if(!b){Fa(d,4,10658,0);K[a+56>>2]=1}d:{e:{f:{g:{g=K[h+5592>>2];if(g){b=K[e+116>>2];if(b>>>0<g>>>0){break g}K[e+52>>2]=g;K[e+48>>2]=b;Fa(d,1,5113,e+48|0);K[a+56>>2]=1;f=0;break a}f=K[e+120>>2];if(f){break f}break d}f=K[e+120>>2];if(!f){break e}}g=(L[a+92|0]>>>4&1)+f|0;K[e+120>>2]=g;b=K[e+116>>2];f=K[h+5592>>2];if(b>>>0>f-1>>>0){K[e+20>>2]=f;K[e+16>>2]=b;Fa(d,1,5014,e+16|0);K[a+56>>2]=1;f=0;break a}if(b>>>0>=g>>>0){K[e+36>>2]=g;K[e+32>>2]=b;Fa(d,1,5213,e+32|0);K[a+56>>2]=1;f=0;break a}K[h+5592>>2]=g}if((K[e+116>>2]+1|0)!=(g|0)){break d}I[a+92|0]=L[a+92|0]|1}b=K[e+124>>2];K[a+8>>2]=16;K[a+24>>2]=K[a+56>>2]?0:b-12|0;f=K[a+44>>2];h:{if((f|0)==-1){f=4;b=c-Q(j,i)|0;if(!(b>>>0<N[a+28>>2]|b>>>0>=N[a+36>>2]|j>>>0<N[a+32>>2])){f=j>>>0>=N[a+40>>2]?4:0}I[a+92|0]=L[a+92|0]&251|f;b=K[a+228>>2];break h}b=K[a+228>>2];I[a+92|0]=L[a+92|0]&251|((f|0)!=(b|0)?4:0)}c=K[K[a+224>>2]+40>>2]+Q(b,40)|0;K[c>>2]=b;K[c+12>>2]=K[e+116>>2];f=K[e+120>>2];if(!K[a+76>>2]){if(N[c+4>>2]>=f>>>0){f=1;break a}K[e>>2]=b;Fa(d,2,1575,e);K[a+76>>2]=1;f=K[e+120>>2]}b=K[a+228>>2];c=K[K[a+224>>2]+40>>2];if(f){b=Q(b,40)+c|0;K[b+4>>2]=f;c=K[e+120>>2];K[b+8>>2]=c;b=K[b+16>>2];if(!b){b=Ia(c,24);K[(K[K[a+224>>2]+40>>2]+Q(K[a+228>>2],40)|0)+16>>2]=b;if(b){f=1;break a}f=0;Fa(d,1,6910,0);break a}b=La(b,Q(c,24));c=K[K[a+224>>2]+40>>2]+Q(K[a+228>>2],40)|0;if(!b){Ga(K[c+16>>2]);f=0;K[(K[K[a+224>>2]+40>>2]+Q(K[a+228>>2],40)|0)+16>>2]=0;Fa(d,1,6910,0);break a}K[c+16>>2]=b;f=1;break a}i:{f=Q(b,40)+c|0;g=K[f+16>>2];if(g){break i}K[f+8>>2]=10;g=Ia(10,24);c=K[K[a+224>>2]+40>>2];b=K[a+228>>2];K[(c+Q(b,40)|0)+16>>2]=g;if(g){break i}f=0;K[(Q(b,40)+c|0)+8>>2]=0;Fa(d,1,6910,0);break a}b=Q(b,40)+c|0;c=K[e+116>>2];if(N[b+8>>2]>c>>>0){f=1;break a}f=1;h=b;b=c+1|0;K[h+8>>2]=b;b=La(g,Q(b,24));c=K[K[a+224>>2]+40>>2]+Q(K[a+228>>2],40)|0;if(!b){Ga(K[c+16>>2]);f=0;a=K[K[a+224>>2]+40>>2]+Q(K[a+228>>2],40)|0;K[a+8>>2]=0;K[a+16>>2]=0;Fa(d,1,6910,0);break a}K[c+16>>2]=b;break a}K[e+80>>2]=b;Fa(d,1,12096,e+80|0);f=0}ra=e+128|0;return f|0}function rb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;g=K[a+8>>2];e=g+K[a+4>>2]|0;a:{if(!K[a+12>>2]){if((e|0)<2|(d|0)<=0){break a}q=e&2147483644;m=e&3;r=e&1;s=g+1|0;h=K[a>>2];o=h+(e<<2)|0;t=e-4>>>1|0;a=e-1|0;u=h+(a<<2)|0;v=Q(c,g)<<2;l=e>>>0<4;w=Q(a>>>1|0,c)<<2;while(1){g=K[b+v>>2];e=K[b>>2]-(g+1>>1)|0;i=0;a=0;if(!l){while(1){j=a+1|0;x=K[(Q(j,c)<<2)+b>>2];f=K[(Q(a+s|0,c)<<2)+b>>2];p=h+(i<<2)|0;K[p>>2]=e;k=e;e=x-((g+f|0)+2>>2)|0;K[p+4>>2]=(k+e>>1)+g;i=i+2|0;k=(a|0)!=(t|0);g=f;a=j;if(k){continue}break}}K[h+(i<<2)>>2]=e;if(r){a=K[b+w>>2]-(g+1>>1)|0;K[u>>2]=a;e=a+e>>1;a=-8}else{a=-4}K[a+o>>2]=e+g;e=0;a=0;g=0;if(!l){while(1){K[(Q(a,c)<<2)+b>>2]=K[h+(a<<2)>>2];f=a|1;K[(Q(f,c)<<2)+b>>2]=K[h+(f<<2)>>2];f=a|2;K[(Q(f,c)<<2)+b>>2]=K[h+(f<<2)>>2];f=a|3;K[(Q(f,c)<<2)+b>>2]=K[h+(f<<2)>>2];a=a+4|0;g=g+4|0;if((q|0)!=(g|0)){continue}break}}if(m){while(1){K[(Q(a,c)<<2)+b>>2]=K[h+(a<<2)>>2];a=a+1|0;e=e+1|0;if((m|0)!=(e|0)){continue}break}}b=b+4|0;n=n+1|0;if((n|0)!=(d|0)){continue}break}break a}b:{switch(e-1|0){case 0:if((d|0)<=0){break a}if(d>>>0>=4){c=d&2147483644;a=0;while(1){K[b>>2]=K[b>>2]/2;K[b+4>>2]=K[b+4>>2]/2;K[b+8>>2]=K[b+8>>2]/2;K[b+12>>2]=K[b+12>>2]/2;b=b+16|0;a=a+4|0;if((c|0)!=(a|0)){continue}break}}c=d&3;if(!c){break a}a=0;while(1){K[b>>2]=K[b>>2]/2;b=b+4|0;a=a+1|0;if((c|0)!=(a|0)){continue}break};break a;case 1:if((d|0)<=0){break a}a=K[a>>2];e=0;g=Q(c,g)<<2;while(1){f=b+g|0;j=K[b>>2]-(K[f>>2]+1>>1)|0;K[a+4>>2]=j;f=j+K[f>>2]|0;K[a>>2]=f;K[b>>2]=f;K[(c<<2)+b>>2]=K[a+4>>2];b=b+4|0;e=e+1|0;if((e|0)!=(d|0)){continue}break};break a;default:break b}}if((e|0)<3|(d|0)<=0){break a}q=e&2147483644;m=e&3;h=K[a>>2];r=(h+(e<<2)|0)-4|0;a=e-2|0;s=h+(a<<2)|0;o=e&1;f=!o;t=((e-f|0)-4>>>1|0)+1|0;u=Q(c,g)<<2;v=a-f>>>0<2;w=Q((e>>>1|0)-1|0,c)<<2;x=e-1>>>0<3;while(1){l=b+u|0;g=K[l+(c<<2)>>2];a=K[l>>2];e=K[b>>2]-((g+a|0)+2>>2)|0;K[h>>2]=e+a;i=1;a=1;if(!v){while(1){p=K[(Q(a,c)<<2)+b>>2];j=a+1|0;f=K[l+(Q(j,c)<<2)>>2];y=h+(i<<2)|0;K[y>>2]=e;k=e;e=p-((g+f|0)+2>>2)|0;K[y+4>>2]=(k+e>>1)+g;i=i+2|0;k=(a|0)!=(t|0);a=j;g=f;if(k){continue}break}}K[h+(i<<2)>>2]=e;c:{if(!o){a=K[b+w>>2]-(g+1>>1)|0;K[s>>2]=(e+a>>1)+g;break c}a=e+g|0}K[r>>2]=a;e=0;a=0;g=0;if(!x){while(1){K[(Q(a,c)<<2)+b>>2]=K[h+(a<<2)>>2];f=a|1;K[(Q(f,c)<<2)+b>>2]=K[h+(f<<2)>>2];f=a|2;K[(Q(f,c)<<2)+b>>2]=K[h+(f<<2)>>2];f=a|3;K[(Q(f,c)<<2)+b>>2]=K[h+(f<<2)>>2];a=a+4|0;g=g+4|0;if((q|0)!=(g|0)){continue}break}}if(m){while(1){K[(Q(a,c)<<2)+b>>2]=K[h+(a<<2)>>2];a=a+1|0;e=e+1|0;if((m|0)!=(e|0)){continue}break}}b=b+4|0;n=n+1|0;if((n|0)!=(d|0)){continue}break}}}function Rb(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0;r=ra-16|0;ra=r;a:{if(!c){Fa(d,1,11592,0);break a}t=K[c+16>>2];i=K[a+96>>2];if(t>>>0<N[i+16>>2]){Fa(d,1,10533,0);break a}f=K[a+128>>2];g=Q(f,K[a+132>>2]);if(g>>>0<=e>>>0){K[r>>2]=e;K[r+4>>2]=g-1;Fa(d,1,16325,r);g=0;break a}j=(e>>>0)/(f>>>0)|0;f=e-Q(j,f)|0;h=K[a+108>>2]+Q(f,K[a+116>>2])|0;K[c>>2]=h;g=K[i>>2];l=g>>>0<h>>>0?h:g;K[c>>2]=l;f=K[a+108>>2]+Q(K[a+116>>2],f+1|0)|0;K[c+8>>2]=f;g=K[K[a+96>>2]+8>>2];f=f>>>0<g>>>0?f:g;K[c+8>>2]=f;i=K[a+112>>2]+Q(j,K[a+120>>2])|0;K[c+4>>2]=i;g=K[K[a+96>>2]+4>>2];h=g>>>0<i>>>0?i:g;K[c+4>>2]=h;i=K[a+112>>2]+Q(K[a+120>>2],j+1|0)|0;K[c+12>>2]=i;g=K[K[a+96>>2]+12>>2];g=g>>>0>i>>>0?i:g;K[c+12>>2]=g;i=K[a+96>>2];m=K[i+16>>2];if(m){u=g-1|0;v=(g>>31)-!g|0;w=f-1|0;x=(f>>31)-!f|0;y=h-1|0;z=0-!h|0;A=l-1|0;B=0-!l|0;C=K[i+24>>2];g=K[c+24>>2];while(1){i=K[(C+Q(q,52)|0)+40>>2];K[g+40>>2]=i;f=B;l=K[g>>2];h=l+A|0;f=l>>>0>h>>>0?f+1|0:f;n=Ne(h,f,l,0);K[g+16>>2]=n;f=z;h=K[g+4>>2];j=h+y|0;f=h>>>0>j>>>0?f+1|0:f;f=Ne(j,f,h,0);K[g+20>>2]=f;j=f;p=i;f=i&31;if((i&63)>>>0>=32){k=-1<<f;f=0}else{o=(1<<f)-1&-1>>>32-f;f=-1<<f;k=o|f}i=f;o=i-j|0;f=k;k=f-((j>>31)+(i>>>0<j>>>0)|0)|0;j=o;o=p&31;if((p&63)>>>0>=32){o=k>>o}else{o=((1<<o)-1&k)<<32-o|j>>>o}k=h>>31;s=k+v|0;j=h+u|0;s=j>>>0<h>>>0?s+1|0:s;j=Me(j,s,h,k);h=i-j|0;j=f-((j>>31)+(i>>>0<j>>>0)|0)|0;k=p&31;if((p&63)>>>0>=32){j=j>>k}else{j=((1<<k)-1&j)<<32-k|h>>>k}K[g+12>>2]=o-j;j=f-((n>>31)+(i>>>0<n>>>0)|0)|0;h=i-n|0;n=p&31;if((p&63)>>>0>=32){n=j>>n}else{n=((1<<n)-1&j)<<32-n|h>>>n}j=l>>31;k=j+x|0;h=l+w|0;k=h>>>0<l>>>0?k+1|0:k;l=Me(h,k,l,j);h=i-l|0;i=f-((l>>31)+(i>>>0<l>>>0)|0)|0;f=h;h=p&31;if((p&63)>>>0>=32){f=i>>h}else{f=((1<<h)-1&i)<<32-h|f>>>h}K[g+8>>2]=n-f;g=g+52|0;q=q+1|0;if((q|0)!=(m|0)){continue}break}}if(m>>>0<t>>>0){g=K[c+24>>2];while(1){f=Q(m,52);Ga(K[(f+g|0)+44>>2]);g=K[c+24>>2];K[(f+g|0)+44>>2]=0;m=m+1|0;if(m>>>0<N[c+16>>2]){continue}break}K[c+16>>2]=K[K[a+96>>2]+16>>2]}g=K[a+100>>2];if(g){Ya(g)}f=Bb();K[a+100>>2]=f;g=0;if(!f){break a}Ob(c,f);K[a+44>>2]=e;if(!$a(K[a+216>>2],24,d)){break a}h=K[a+216>>2];e=K[h>>2];m=K[h+8>>2];b:{if(e){g=1;i=e&1;if((e|0)==1){e=0}else{f=e&-2;q=0;while(1){e=0;c:{if(!g){break c}e=0;if(!(va[K[m>>2]](a,b,d)|0)){break c}e=(va[K[m+4>>2]](a,b,d)|0)!=0}g=e;m=m+8|0;q=q+2|0;if((f|0)!=(q|0)){continue}break}e=!g}g=i?0:g;if(!(e|!i)){g=(va[K[m>>2]](a,b,d)|0)!=0}Ta(h);if(g){break b}Ya(K[a+96>>2]);g=0;K[a+96>>2]=0;break a}Ta(h)}g=Sb(a,c)}ra=r+16|0;return g|0}function lc(a,b,c,d,e,f,g){var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a:{n=Q(e,3);h=K[b>>2]>>>n|0;if(h&2097168){break a}h=h&495;if(!h){break a}o=a+28|0;l=o+(L[h+K[a+108>>2]|0]<<2)|0;K[a+104>>2]=l;k=K[l>>2];i=K[k>>2];h=K[a+4>>2]-i|0;K[a+4>>2]=h;j=K[a>>2];b:{if(j>>>16>>>0<i>>>0){m=K[k+4>>2];K[a+4>>2]=i;h=h>>>0<i>>>0;K[l>>2]=K[k+(h?8:12)>>2];k=h?m:!m;h=K[a+8>>2];while(1){c:{if(h){break c}h=K[a+16>>2];m=h+1|0;l=L[h+1|0];if(L[h|0]==255){if(l>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;j=j+65280|0;h=8;break c}K[a+16>>2]=m;j=(l<<9)+j|0;h=7;break c}K[a+16>>2]=m;h=8;j=(l<<8)+j|0}h=h-1|0;K[a+8>>2]=h;j=j<<1;K[a>>2]=j;i=i<<1;K[a+4>>2]=i;if(i>>>0<32768){continue}break}h=i;break b}j=j-(i<<16)|0;K[a>>2]=j;if(!(h&32768)){m=K[k+4>>2];i=h>>>0<i>>>0;K[l>>2]=K[k+(i?12:8)>>2];k=i?!m:m;i=K[a+8>>2];while(1){d:{if(i){break d}i=K[a+16>>2];m=i+1|0;l=L[i+1|0];if(L[i|0]==255){if(l>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;j=j+65280|0;i=8;break d}K[a+16>>2]=m;j=(l<<9)+j|0;i=7;break d}K[a+16>>2]=m;i=8;j=(l<<8)+j|0}i=i-1|0;K[a+8>>2]=i;j=j<<1;K[a>>2]=j;h=h<<1;K[a+4>>2]=h;if(h>>>0<32768){continue}break}break b}k=K[k+4>>2]}e:{if(!k){break e}p=b-4|0;i=K[b>>2];k=K[b+4>>2]>>>n+17&4|(K[p>>2]>>>n+19&1|(i>>>n+16&64|i>>>n&170|i>>>(e?n+12|0:14)&16));m=o+(L[k+24336|0]<<2)|0;K[a+104>>2]=m;l=K[m>>2];i=K[l>>2];h=h-i|0;K[a+4>>2]=h;o=L[k+24592|0];f:{if(j>>>16>>>0<i>>>0){k=K[l+4>>2];K[a+4>>2]=i;h=h>>>0<i>>>0;K[m>>2]=K[l+(h?8:12)>>2];l=h?k:!k;h=K[a+8>>2];while(1){g:{if(h){break g}h=K[a+16>>2];m=h+1|0;k=L[h+1|0];if(L[h|0]==255){if(k>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;j=j+65280|0;h=8;break g}K[a+16>>2]=m;j=(k<<9)+j|0;h=7;break g}K[a+16>>2]=m;h=8;j=(k<<8)+j|0}h=h-1|0;K[a+8>>2]=h;j=j<<1;K[a>>2]=j;i=i<<1;K[a+4>>2]=i;if(i>>>0<32768){continue}break}break f}k=j-(i<<16)|0;K[a>>2]=k;if(!(h&32768)){j=K[l+4>>2];i=h>>>0<i>>>0;K[m>>2]=K[l+(i?12:8)>>2];l=i?!j:j;j=K[a+8>>2];while(1){h:{if(j){break h}j=K[a+16>>2];m=j+1|0;i=L[j+1|0];if(L[j|0]==255){if(i>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;k=k+65280|0;j=8;break h}K[a+16>>2]=m;k=(i<<9)+k|0;j=7;break h}K[a+16>>2]=m;j=8;k=(i<<8)+k|0}j=j-1|0;K[a+8>>2]=j;k=k<<1;K[a>>2]=k;h=h<<1;K[a+4>>2]=h;if(h>>>0<32768){continue}break}break f}l=K[l+4>>2]}K[c>>2]=(l|0)==(o|0)?d:0-d|0;K[p>>2]=K[p>>2]|32<<n;c=l^o;K[b>>2]=K[b>>2]|(c<<19|16)<<n;K[b+4>>2]=K[b+4>>2]|8<<n;if(!(e|g)){a=b-(f<<2)|0;K[a+4>>2]=K[a+4>>2]|32768;K[a>>2]=K[a>>2]|c<<31|65536;a=a-4|0;K[a>>2]=K[a>>2]|131072}if((e|0)!=3){break e}a=(f<<2)+b|0;K[a+4>>2]=K[a+4>>2]|1;K[a>>2]=K[a>>2]|c<<18|2;a=a-4|0;K[a>>2]=K[a>>2]|4}K[b>>2]=K[b>>2]|2097152<<n}}function _b(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;var h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;h=ra-208|0;ra=h;j=K[a+96>>2];a:{if(!(!(K[K[a+180>>2]+5596>>2]?K[a+128>>2]!=1|K[a+132>>2]!=1:1)|K[a+8>>2]==8)){Fa(g,1,10577,0);break a}m=K[b+16>>2];b:{if(!m){break b}k=K[a+184>>2];l=K[b+24>>2];if(m>>>0>=8){p=m&-8;while(1){K[(Q(i,52)+l|0)+40>>2]=k;K[(Q(i|1,52)+l|0)+40>>2]=k;K[(Q(i|2,52)+l|0)+40>>2]=k;K[(Q(i|3,52)+l|0)+40>>2]=k;K[(Q(i|4,52)+l|0)+40>>2]=k;K[(Q(i|5,52)+l|0)+40>>2]=k;K[(Q(i|6,52)+l|0)+40>>2]=k;K[(Q(i|7,52)+l|0)+40>>2]=k;i=i+8|0;n=n+8|0;if((p|0)!=(n|0)){continue}break}}m=m&7;if(!m){break b}while(1){K[(Q(i,52)+l|0)+40>>2]=k;i=i+1|0;o=o+1|0;if((m|0)!=(o|0)){continue}break}}if(!(c|d|e|f)){Fa(g,4,6307,0);K[a+28>>2]=0;K[a+32>>2]=0;c=K[a+132>>2];K[a+36>>2]=K[a+128>>2];K[a+40>>2]=c;K[b>>2]=K[j>>2];K[b+4>>2]=K[j+4>>2];K[b+8>>2]=K[j+8>>2];K[b+12>>2]=K[j+12>>2];i=Db(b,g);break a}if((c|0)<0){K[h>>2]=c;Fa(g,1,12565,h);i=0;break a}i=K[j+8>>2];if(i>>>0<c>>>0){K[h+20>>2]=i;K[h+16>>2]=c;Fa(g,1,13033,h+16|0);i=0;break a}i=K[j>>2];c:{if(i>>>0>c>>>0){K[h+196>>2]=i;K[h+192>>2]=c;Fa(g,2,13385,h+192|0);K[a+28>>2]=0;c=K[j>>2];break c}K[a+28>>2]=(c-K[a+108>>2]>>>0)/N[a+116>>2]}K[b>>2]=c;if((d|0)<0){K[h+32>>2]=d;Fa(g,1,12501,h+32|0);i=0;break a}c=K[j+12>>2];if(c>>>0<d>>>0){K[h+52>>2]=c;K[h+48>>2]=d;Fa(g,1,12860,h+48|0);i=0;break a}c=K[j+4>>2];d:{if(c>>>0>d>>>0){K[h+180>>2]=c;K[h+176>>2]=d;Fa(g,2,13210,h+176|0);K[a+32>>2]=0;d=K[j+4>>2];break d}K[a+32>>2]=(d-K[a+112>>2]>>>0)/N[a+120>>2]}K[b+4>>2]=d;i=0;if((e|0)<=0){K[h+64>>2]=e;Fa(g,1,12435,h- -64|0);break a}c=K[j>>2];if(c>>>0>e>>>0){K[h+84>>2]=c;K[h+80>>2]=e;Fa(g,1,13296,h+80|0);break a}c=K[j+8>>2];e:{if(c>>>0<e>>>0){K[h+164>>2]=c;K[h+160>>2]=e;Fa(g,2,12945,h+160|0);K[a+36>>2]=K[a+128>>2];e=K[j+8>>2];break e}k=0;d=e-K[a+108>>2]|0;l=d;c=K[a+116>>2];d=d+c|0;k=l>>>0>d>>>0?1:k;q=a,r=Ne(d-1|0,k-!d|0,c,0),K[q+36>>2]=r}K[b+8>>2]=e;if((f|0)<=0){K[h+96>>2]=f;Fa(g,1,12368,h+96|0);break a}c=K[j+4>>2];if(c>>>0>f>>>0){K[h+116>>2]=c;K[h+112>>2]=f;Fa(g,1,13120,h+112|0);break a}c=K[j+12>>2];f:{if(c>>>0<f>>>0){K[h+148>>2]=c;K[h+144>>2]=f;Fa(g,2,12771,h+144|0);K[a+40>>2]=K[a+132>>2];f=K[j+12>>2];break f}e=0;d=f-K[a+112>>2]|0;l=d;c=K[a+120>>2];d=d+c|0;e=l>>>0>d>>>0?1:e;q=a,r=Ne(d-1|0,e-!d|0,c,0),K[q+40>>2]=r}K[b+12>>2]=f;I[a+92|0]=L[a+92|0]|2;if(!Db(b,g)){break a}a=K[b>>2];c=K[b+4>>2];d=K[b+12>>2];K[h+136>>2]=K[b+8>>2];K[h+140>>2]=d;K[h+128>>2]=a;K[h+132>>2]=c;Fa(g,4,7529,h+128|0);i=1}ra=h+208|0;return i|0}function kc(a,b,c,d,e,f){var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;a:{m=Q(e,3);g=K[b>>2]>>>m|0;if(g&2097168){break a}n=a+28|0;k=n+(L[K[a+108>>2]+(g&495)|0]<<2)|0;K[a+104>>2]=k;j=K[k>>2];h=K[j>>2];g=K[a+4>>2]-h|0;K[a+4>>2]=g;i=K[a>>2];b:{if(i>>>16>>>0<h>>>0){l=K[j+4>>2];K[a+4>>2]=h;g=g>>>0<h>>>0;K[k>>2]=K[j+(g?8:12)>>2];j=g?l:!l;g=K[a+8>>2];while(1){c:{if(g){break c}g=K[a+16>>2];l=g+1|0;k=L[g+1|0];if(L[g|0]==255){if(k>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;i=i+65280|0;g=8;break c}K[a+16>>2]=l;i=(k<<9)+i|0;g=7;break c}K[a+16>>2]=l;g=8;i=(k<<8)+i|0}g=g-1|0;K[a+8>>2]=g;i=i<<1;K[a>>2]=i;h=h<<1;K[a+4>>2]=h;if(h>>>0<32768){continue}break}g=h;break b}i=i-(h<<16)|0;K[a>>2]=i;if(!(g&32768)){l=K[j+4>>2];h=g>>>0<h>>>0;K[k>>2]=K[j+(h?12:8)>>2];j=h?!l:l;h=K[a+8>>2];while(1){d:{if(h){break d}h=K[a+16>>2];l=h+1|0;k=L[h+1|0];if(L[h|0]==255){if(k>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;i=i+65280|0;h=8;break d}K[a+16>>2]=l;i=(k<<9)+i|0;h=7;break d}K[a+16>>2]=l;h=8;i=(k<<8)+i|0}h=h-1|0;K[a+8>>2]=h;i=i<<1;K[a>>2]=i;g=g<<1;K[a+4>>2]=g;if(g>>>0<32768){continue}break}break b}j=K[j+4>>2]}if(!j){break a}j=n;n=b-4|0;h=K[b>>2];o=K[b+4>>2]>>>m+17&4|(K[n>>2]>>>m+19&1|(h>>>m+16&64|h>>>m&170|h>>>(e?m+12|0:14)&16));l=j+(L[o+24336|0]<<2)|0;K[a+104>>2]=l;k=K[l>>2];h=K[k>>2];g=g-h|0;K[a+4>>2]=g;e:{if(i>>>16>>>0<h>>>0){j=K[k+4>>2];K[a+4>>2]=h;g=g>>>0<h>>>0;K[l>>2]=K[k+(g?8:12)>>2];k=g?j:!j;g=K[a+8>>2];while(1){f:{if(g){break f}g=K[a+16>>2];l=g+1|0;j=L[g+1|0];if(L[g|0]==255){if(j>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;i=i+65280|0;g=8;break f}K[a+16>>2]=l;i=(j<<9)+i|0;g=7;break f}K[a+16>>2]=l;g=8;i=(j<<8)+i|0}g=g-1|0;K[a+8>>2]=g;i=i<<1;K[a>>2]=i;h=h<<1;K[a+4>>2]=h;if(h>>>0<32768){continue}break}break e}j=i-(h<<16)|0;K[a>>2]=j;if(!(g&32768)){i=K[k+4>>2];h=g>>>0<h>>>0;K[l>>2]=K[k+(h?12:8)>>2];k=h?!i:i;i=K[a+8>>2];while(1){g:{if(i){break g}i=K[a+16>>2];l=i+1|0;h=L[i+1|0];if(L[i|0]==255){if(h>>>0>=144){K[a+12>>2]=K[a+12>>2]+1;j=j+65280|0;i=8;break g}K[a+16>>2]=l;j=(h<<9)+j|0;i=7;break g}K[a+16>>2]=l;i=8;j=(h<<8)+j|0}i=i-1|0;K[a+8>>2]=i;j=j<<1;K[a>>2]=j;g=g<<1;K[a+4>>2]=g;if(g>>>0<32768){continue}break}break e}k=K[k+4>>2]}g=c;c=L[o+24592|0];K[g>>2]=(c|0)==(k|0)?d:0-d|0;K[n>>2]=K[n>>2]|32<<m;d=c^k;K[b>>2]=K[b>>2]|(d<<19|16)<<m;K[b+4>>2]=K[b+4>>2]|8<<m;if(!(e|f)){c=(-2-K[a+124>>2]<<2)+b|0;K[c+4>>2]=K[c+4>>2]|32768;K[c>>2]=K[c>>2]|d<<31|65536;c=c-4|0;K[c>>2]=K[c>>2]|131072}if((e|0)!=3){break a}a=(K[a+124>>2]<<2)+b|0;K[a+4>>2]=K[a+4>>2]|4;K[a+12>>2]=K[a+12>>2]|1;K[a+8>>2]=K[a+8>>2]|d<<18|2}}function be(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;e=ra-112|0;ra=e;j=1024;a:{b:{h=Ia(1,1024);if(h){l=e+92|0;k=e+108|0;while(1){c:{d:{d=e+104|0;e:{if((Na(b,d,8,c)|0)!=8){break e}Ha(d,e+88|0,4);Ha(k,l,4);f=8;f:{g:{h:{i:{switch(K[e+88>>2]){case 0:d=Va(b);g=ua;if((g|0)<0){g=1}else{g=d>>>0<4294967288&(g|0)<=0}if(g){break h}Fa(c,1,8412,0);break e;case 1:break i;default:break f}}d=e+104|0;if((Na(b,d,8,c)|0)!=8){break e}Ha(d,e+100|0,4);if(!K[e+100>>2]){break g}Fa(c,1,8412,0);break e}K[e+88>>2]=d+8;break f}Ha(k,e+88|0,4);f=16}d=K[e+92>>2];if((d|0)==1785737827){b=K[a+100>>2];if(b&4){K[a+100>>2]=b|8;break e}Fa(c,1,5665,0);Ga(h);a=0;break a}i=K[e+88>>2];if(!i){Fa(c,1,3231,0);Ga(h);a=0;break a}if(f>>>0>i>>>0){K[e+4>>2]=d;K[e>>2]=i;Fa(c,1,13896,e);break b}j:{k:{l:{m:{n:{o:{p:{q:{r:{s:{if((d|0)<=1668246641){if((d|0)==1651532643){break r}if((d|0)==1667523942){break p}if((d|0)!=1668112752){break s}g=25248;break n}if((d|0)<=1783635999){if((d|0)==1668246642){break o}g=25216;if((d|0)==1768449138){break n}if((d|0)!=1718909296){break s}g=25192;break l}if((d|0)==1885564018){break q}if((d|0)==1783636e3){break m}g=25200;if((d|0)==1785737832){break l}}d=K[a+100>>2];if(d&1){break j}Fa(c,1,2025,0);Ga(h);a=0;break a}g=25232;break n}g=25240;break n}g=25256;break n}g=25224}K[e+76>>2]=d&255;K[e+64>>2]=d>>>24;K[e+72>>2]=d>>>8&255;K[e+68>>2]=d>>>16&255;Fa(c,2,1974,e- -64|0);f=i-f|0;if(L[a+100|0]&4){break k}d=K[e+92>>2];K[e+48>>2]=d>>>24;K[e+60>>2]=d&255;K[e+52>>2]=d>>>16&255;K[e+56>>2]=d>>>8&255;Fa(c,2,6734,e+48|0);K[a+100>>2]=K[a+100>>2]|2147483647;d=vb(b,f,c);if(!ua&(d|0)==(f|0)){continue}Fa(c,1,3711,0);Ga(h);a=0;break a}g=25184}f=i-f|0}d=f;f=Va(b);i=ua;if((i|0)<0){f=1}else{f=(i|0)<=0&d>>>0>f>>>0}if(f){f=K[e+88>>2];a=K[e+92>>2];m=e,n=Va(b),K[m+40>>2]=n;K[e+36>>2]=d;K[e+32>>2]=a&255;K[e+20>>2]=a>>>24;K[e+16>>2]=f;K[e+28>>2]=a>>>8&255;K[e+24>>2]=a>>>16&255;Fa(c,1,15643,e+16|0);break b}if(d>>>0<=j>>>0){f=h;break c}j=d;f=La(h,d);if(f){break c}Ga(h);Fa(c,1,2156,0);a=0;break a}if(!(d&2)){Fa(c,1,2095,0);Ga(h);a=0;break a}K[a+100>>2]=d|2147483647;d=i-f|0;f=vb(b,d,c);if(!ua&(d|0)==(f|0)){continue}if(!(L[a+100|0]&8)){break d}Fa(c,2,3711,0)}Ga(h);a=1;break a}Fa(c,1,3711,0);Ga(h);a=0;break a}if((Na(b,f,d,c)|0)!=(d|0)){Fa(c,1,3761,0);Ga(f);a=0;break a}h=f;if(va[K[g+4>>2]](a,f,d,c)|0){continue}break}Ga(f);a=0;break a}Fa(c,1,4886,0);a=0;break a}Ga(h);a=0}ra=e+112|0;return a|0}function pe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;g=ra-16|0;ra=g;if(K[a+8>>2]==16){h=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{h=K[a+12>>2]}a:{if(c>>>0<=1){Fa(d,1,4684,0);a=0;break a}Ha(b,g+12|0,2);if(K[g+12>>2]){Fa(d,2,5860,0);a=1;break a}if(c>>>0<=6){Fa(d,1,4684,0);a=0;break a}Ha(b+2|0,g+8|0,1);j=K[h+5628>>2];a=j;b:{c:{d:{e=K[h+5632>>2];if(!e){break d}i=K[g+8>>2];while(1){if((i|0)==K[a>>2]){break d}a=a+20|0;f=f+1|0;if((e|0)!=(f|0)){continue}break}break c}if((e|0)!=(f|0)){break b}}if((e|0)==K[h+5636>>2]){a=e+10|0;K[h+5636>>2]=a;a=La(j,Q(a,20));if(!a){Ga(K[h+5628>>2]);K[h+5636>>2]=0;K[h+5628>>2]=0;K[h+5632>>2]=0;Fa(d,1,4710,0);a=0;break a}K[h+5628>>2]=a;e=K[h+5632>>2];f=Q(K[h+5636>>2]-e|0,20);if(f){B(a+Q(e,20)|0,0,f)}j=K[h+5628>>2];e=K[h+5632>>2]}a=Q(e,20)+j|0;n=1}K[a>>2]=K[g+8>>2];Ha(b+3|0,g+12|0,2);if(K[g+12>>2]){Fa(d,2,5860,0);a=1;break a}Ha(b+5|0,g+4|0,2);f=K[g+4>>2];if(f>>>0>=2){Fa(d,2,3093,0);a=1;break a}e=c-7|0;if(f){c=b+7|0;j=0;while(1){if(e>>>0<=2){Fa(d,1,4684,0);a=0;break a}Ha(c,g+12|0,1);if(K[g+12>>2]!=1){Fa(d,2,5542,0);a=1;break a}Ha(c+1|0,g,2);f=K[g>>2];b=f&32767;K[a+4>>2]=b;i=e-3|0;e=(f>>>15|0)+1|0;k=Q(e,b)+2|0;if(i>>>0<k>>>0){Fa(d,1,4684,0);a=0;break a}c=c+3|0;f=0;if(b){while(1){Ha(c,g+12|0,e);if(K[g+12>>2]!=(f|0)){Fa(d,2,6222,0);a=1;break a}c=c+e|0;f=f+1|0;if(f>>>0<N[a+4>>2]){continue}break}}Ha(c,g,2);e=K[g>>2];b=e&32767;K[g>>2]=b;if((b|0)!=K[a+4>>2]){Fa(d,2,3269,0);a=1;break a}e=(e>>>15|0)+1|0;l=Q(e,b)+3|0;k=i-k|0;if(l>>>0>k>>>0){Fa(d,1,4684,0);a=0;break a}c=c+2|0;f=0;if(b){while(1){Ha(c,g+12|0,e);if(K[g+12>>2]!=(f|0)){Fa(d,2,6222,0);a=1;break a}c=c+e|0;f=f+1|0;if(f>>>0<N[a+4>>2]){continue}break}}Ha(c,g+12|0,3);e=K[g+12>>2];K[a+8>>2]=0;K[a+12>>2]=0;I[a+16|0]=!(e&65536)|L[a+16|0]&254;i=e&255;K[g+8>>2]=i;e:{if(!i){break e}m=K[h+5620>>2];if(m){f=K[h+5616>>2];b=0;while(1){if((i|0)==K[f+8>>2]){K[a+8>>2]=f;break e}f=f+20|0;b=b+1|0;if((m|0)!=(b|0)){continue}break}}Fa(d,1,4684,0);a=0;break a}e=e>>>8&255;K[g+8>>2]=e;f:{if(!e){break f}i=K[h+5620>>2];if(i){f=K[h+5616>>2];b=0;while(1){if((e|0)==K[f+8>>2]){K[a+12>>2]=f;break f}f=f+20|0;b=b+1|0;if((i|0)!=(b|0)){continue}break}}Fa(d,1,4684,0);a=0;break a}e=k-l|0;c=c+3|0;j=j+1|0;if(j>>>0<N[g+4>>2]){continue}break}}if(e){Fa(d,1,4684,0);a=0;break a}a=1;if(!n){break a}K[h+5632>>2]=K[h+5632>>2]+1;a=1}ra=g+16|0;return a|0}function kd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if(N[a+44>>2]>=8){i=K[a+40>>2];l=8;while(1){k=K[a+12>>2]<<5;e=K[a>>2];g=K[a+36>>2];b=K[a+16>>2];h=K[a+20>>2];a:{if(b>>>0>=h>>>0){break a}j=e+k|0;d=b+1|0;if(h-b&1){c=j+(b<<6)|0;b=(Q(b,g)<<2)+i|0;f=K[b+4>>2];K[c>>2]=K[b>>2];K[c+4>>2]=f;f=K[b+28>>2];K[c+24>>2]=K[b+24>>2];K[c+28>>2]=f;f=K[b+20>>2];K[c+16>>2]=K[b+16>>2];K[c+20>>2]=f;f=K[b+12>>2];K[c+8>>2]=K[b+8>>2];K[c+12>>2]=f;b=d}if((d|0)==(h|0)){break a}while(1){d=(Q(b,g)<<2)+i|0;f=K[d+4>>2];c=j+(b<<6)|0;K[c>>2]=K[d>>2];K[c+4>>2]=f;f=K[d+28>>2];K[c+24>>2]=K[d+24>>2];K[c+28>>2]=f;f=K[d+20>>2];K[c+16>>2]=K[d+16>>2];K[c+20>>2]=f;f=K[d+12>>2];K[c+8>>2]=K[d+8>>2];K[c+12>>2]=f;d=b+1|0;c=j+(d<<6)|0;d=(Q(d,g)<<2)+i|0;f=K[d+28>>2];K[c+24>>2]=K[d+24>>2];K[c+28>>2]=f;f=K[d+20>>2];K[c+16>>2]=K[d+16>>2];K[c+20>>2]=f;f=K[d+12>>2];K[c+8>>2]=K[d+8>>2];K[c+12>>2]=f;f=K[d+4>>2];K[c>>2]=K[d>>2];K[c+4>>2]=f;b=b+2|0;if((h|0)!=(b|0)){continue}break}}b=K[a+24>>2];h=K[a+28>>2];b:{if(b>>>0>=h>>>0){break b}j=(e-k|0)+32|0;k=(Q(g,K[a+8>>2])<<2)+i|0;d=b+1|0;if(h-b&1){c=j+(b<<6)|0;b=k+(Q(b,g)<<2)|0;e=K[b+4>>2];K[c>>2]=K[b>>2];K[c+4>>2]=e;e=K[b+28>>2];K[c+24>>2]=K[b+24>>2];K[c+28>>2]=e;e=K[b+20>>2];K[c+16>>2]=K[b+16>>2];K[c+20>>2]=e;e=K[b+12>>2];K[c+8>>2]=K[b+8>>2];K[c+12>>2]=e;b=d}if((d|0)==(h|0)){break b}while(1){d=k+(Q(b,g)<<2)|0;e=K[d+4>>2];c=j+(b<<6)|0;K[c>>2]=K[d>>2];K[c+4>>2]=e;e=K[d+28>>2];K[c+24>>2]=K[d+24>>2];K[c+28>>2]=e;e=K[d+20>>2];K[c+16>>2]=K[d+16>>2];K[c+20>>2]=e;e=K[d+12>>2];K[c+8>>2]=K[d+8>>2];K[c+12>>2]=e;d=b+1|0;c=j+(d<<6)|0;d=k+(Q(d,g)<<2)|0;e=K[d+28>>2];K[c+24>>2]=K[d+24>>2];K[c+28>>2]=e;e=K[d+20>>2];K[c+16>>2]=K[d+16>>2];K[c+20>>2]=e;e=K[d+12>>2];K[c+8>>2]=K[d+8>>2];K[c+12>>2]=e;e=K[d+4>>2];K[c>>2]=K[d>>2];K[c+4>>2]=e;b=b+2|0;if((h|0)!=(b|0)){continue}break}}Za(a);b=0;if(K[a+32>>2]){while(1){d=K[a>>2]+(b<<5)|0;c=K[d+4>>2];g=(Q(K[a+36>>2],b)<<2)+i|0;K[g>>2]=K[d>>2];K[g+4>>2]=c;c=K[d+28>>2];K[g+24>>2]=K[d+24>>2];K[g+28>>2]=c;c=K[d+20>>2];K[g+16>>2]=K[d+16>>2];K[g+20>>2]=c;c=K[d+12>>2];K[g+8>>2]=K[d+8>>2];K[g+12>>2]=c;b=b+1|0;if(b>>>0<N[a+32>>2]){continue}break}}i=i+32|0;l=l+8|0;if(l>>>0<=N[a+44>>2]){continue}break}}Ga(K[a>>2]);Ga(a)}function td(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;c=K[b>>2]+7&-8;K[b>>2]=c+16;q=a;b=K[c>>2];a=K[c+4>>2];d=K[c+8>>2];c=K[c+12>>2];r=c;g=ra-32|0;ra=g;f=c&65535;e=d;d=0;c=c>>>16&32767;o=c;a:{if(c-15361>>>0<=2045){c=f<<4|e>>>28;d=e<<4|a>>>28;f=o-15360|0;a=a&268435455;b:{if((a|0)==134217728&(b|0)!=0|a>>>0>134217728){d=d+1|0;c=d?c:c+1|0;break b}if(b|(a|0)!=134217728){break b}a=d;d=d+(d&1)|0;c=a>>>0>d>>>0?c+1|0:c}a=d;d=c>>>0>1048575;b=d?0:a;a=d?0:c;c=0;e=f;f=d+f|0;e=e>>>0>f>>>0?1:c;break a}if(!(!(b|e|(a|f))|((c|0)!=32767|(d|0)!=0))){b=e;e=f<<4|b>>>28;b=b<<4|a>>>28;a=e|524288;f=2047;e=0;break a}if(o>>>0>17406){b=0;a=0;f=2047;e=0;break a}j=!(c|d);p=j?15360:15361;k=p-o|0;if((k|0)>112){b=0;a=0;f=0;e=0;break a}d=b;c=a;l=e;e=j?f:f|65536;f=e;h=l;m=128-k|0;c:{if(m&64){e=d;c=m+-64|0;d=c&31;if((c&63)>>>0>=32){c=b<<d;h=0}else{c=(1<<d)-1&e>>>32-d|a<<d;h=e<<d}e=c;d=0;c=0;break c}if(!m){break c}n=h;i=m&31;if((m&63)>>>0>=32){j=h<<i;n=0}else{j=(1<<i)-1&n>>>32-i|e<<i;n=n<<i}i=d;e=64-m|0;h=e&31;if((e&63)>>>0>=32){e=0;h=c>>>h|0}else{e=c>>>h|0;h=((1<<h)-1&c)<<32-h|i>>>h}h=n|h;e=e|j;n=d;i=m&31;if((m&63)>>>0>=32){j=d<<i;d=0}else{j=(1<<i)-1&n>>>32-i|c<<i;d=n<<i}c=j}K[g+16>>2]=d;K[g+20>>2]=c;K[g+24>>2]=h;K[g+28>>2]=e;d:{if(k&64){c=l;b=k+-64|0;a=b&31;if((b&63)>>>0>=32){e=0;b=f>>>a|0}else{e=f>>>a|0;b=((1<<a)-1&f)<<32-a|c>>>a}a=e;l=0;f=0;break d}if(!k){break d}e=l;c=64-k|0;d=c&31;if((c&63)>>>0>=32){c=e<<d;l=0}else{c=(1<<d)-1&e>>>32-d|f<<d;l=e<<d}d=b;b=k&31;if((k&63)>>>0>=32){j=0;a=a>>>b|0}else{j=a>>>b|0;a=((1<<b)-1&a)<<32-b|d>>>b}b=l|a;a=c|j;d=k&31;if((k&63)>>>0>=32){c=0;l=f>>>d|0}else{c=f>>>d|0;l=((1<<d)-1&f)<<32-d|e>>>d}f=c}K[g>>2]=b;K[g+4>>2]=a;K[g+8>>2]=l;K[g+12>>2]=f;a=K[g+8>>2];d=a<<4;a=K[g+12>>2]<<4|a>>>28;f=K[g>>2];b=K[g+4>>2];e=b;b=b>>>28|d;c=e&268435455;f=f|(o|0)!=(p|0)&(K[g+16>>2]|K[g+24>>2]|(K[g+20>>2]|K[g+28>>2]))!=0;e:{if((c|0)==134217728&(f|0)!=0|c>>>0>134217728){b=b+1|0;a=b?a:a+1|0;break e}if(f|(c|0)!=134217728){break e}c=a;a=b;b=b+(b&1)|0;a=a>>>0>b>>>0?c+1|0:c}f=a>>>0>1048575;a=f?a^1048576:a;e=0}ra=g+32|0;x(0,b|0);x(1,a|(r&-2147483648|f<<20));s=q,t=+z(),P[s>>3]=t}function Wc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0;q=K[a+24>>2];if(!K[q+16>>2]){return 1}r=K[q+24>>2];o=K[K[K[a+20>>2]>>2]+20>>2];while(1){e=K[r+36>>2];K[b+36>>2]=e;c=Q(e,152);e=K[o+28>>2];d=c+e|0;u=K[a+64>>2];a:{if(u){e=e+Q(K[o+24>>2],152)|0;p=K[e-144>>2]-K[e-152>>2]|0;c=d+12|0;f=d+4|0;e=K[d+8>>2];h=K[d>>2];g=36;break a}c=d+148|0;f=d+140|0;e=K[d+144>>2];h=K[d+136>>2];p=e-h|0;g=52}v=K[g+o>>2];b:{c:{if(!v){break c}l=K[f>>2];n=K[c>>2];i=e-h|0;f=K[b+40>>2];c=f&31;if((f&63)>>>0>=32){d=-1<<c;c=0}else{g=(1<<c)-1&-1>>>32-c;c=-1<<c;d=g|c}m=c^-1;j=K[b+20>>2];k=m+j|0;g=d^-1;c=g;c=k>>>0<j>>>0?c+1|0:c;d=f&31;if((f&63)>>>0>=32){k=c>>>d|0}else{k=((1<<d)-1&c)<<32-d|k>>>d}d=K[b+8>>2];j=K[b+16>>2];m=j+m|0;c=g;c=m>>>0<j>>>0?c+1|0:c;g=f&31;if((f&63)>>>0>=32){f=c>>>g|0}else{f=((1<<g)-1&c)<<32-g|m>>>g}c=f+d|0;d:{if(f>>>0<h>>>0){s=h-f|0;g=0;if(c>>>0>=e>>>0){m=0;e=i;break d}e=c-h|0;m=i-e|0;break d}g=f-h|0;if(c>>>0>=e>>>0){e=i-g|0;s=0;m=0;break d}m=e-c|0;s=0;e=d}c=n-l|0;f=K[b+12>>2];i=f+k|0;e:{if(k>>>0<l>>>0){t=l-k|0;k=0;j=0;if(i>>>0>=n>>>0){break e}j=c;c=i-l|0;j=j-c|0;break e}k=k-l|0;if(i>>>0>=n>>>0){c=c-k|0;t=0;j=0;break e}t=0;c=f;j=n-i|0}h=0;if((g|k|(m|j)|(c|e))<0){break b}i=Q(k,p)+g|0;g=K[b+44>>2];l=Q(d,t)+s|0;f:{g:{if(!(i|g|(l|(d|0)!=(p|0))|(d|0)!=(e|0))){if((c|0)!=(f|0)){break g}e=(u?36:52)+o|0;K[b+44>>2]=K[e>>2];K[e>>2]=0;break c}if(g){break f}}Le(f,0,d);if(ua|!f){break b}d=Q(d,f);if(d>>>0>1073741823){break b}d=Ma(d<<2);K[b+44>>2]=d;if(!d){break b}f=K[b+8>>2];g=K[b+12>>2];if((f|0)==(e|0)&(g|0)==(c|0)){break f}f=Q(f,g)<<2;if(!f){break f}B(d,0,f)}if(!c){break c}g=c&1;e=e<<2;h=K[b+44>>2]+(l<<2)|0;d=(i<<2)+v|0;if((c|0)!=1){i=c&2147483646;c=0;while(1){l=!e;if(!l){E(h,d,e)}n=p<<2;d=n+d|0;f=(K[b+8>>2]<<2)+h|0;if(!l){E(f,d,e)}d=d+n|0;h=f+(K[b+8>>2]<<2)|0;c=c+2|0;if((i|0)!=(c|0)){continue}break}}if(!g|!e){break c}E(h,d,e)}o=o+76|0;r=r+52|0;b=b+52|0;h=1;w=w+1|0;if(w>>>0<N[q+16>>2]){continue}}break}return h}function Eb(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0;if(a){a:{if(K[a>>2]){b=K[a+12>>2];if(b){nb(b);Ga(K[a+12>>2]);K[a+12>>2]=0}b=K[a+16>>2];if(b){Ga(b);K[a+16>>2]=0;K[a+20>>2]=0}Ga(K[a+64>>2]);K[a+60>>2]=0;K[a+64>>2]=0;Ga(K[a+72>>2]);K[a+72>>2]=0;Ga(K[a+88>>2]);K[a+88>>2]=0;break a}b=K[a+44>>2];if(b){Ga(b);K[a+44>>2]=0}b=K[a+32>>2];if(b){Ga(b);K[a+32>>2]=0;K[a+36>>2]=0}b=K[a+52>>2];if(!b){break a}Ga(b);K[a+52>>2]=0;K[a+56>>2]=0}hc(K[a+232>>2]);b=K[a+180>>2];if(b){e=Q(K[a+128>>2],K[a+132>>2]);if(e){while(1){nb(b);b=b+5644|0;c=c+1|0;if((e|0)!=(c|0)){continue}break}b=K[a+180>>2]}Ga(b);K[a+180>>2]=0}b=K[a+140>>2];if(b){c=K[a+136>>2];if(c){b=0;while(1){e=K[K[a+140>>2]+(b<<3)>>2];if(e){Ga(e);c=K[a+136>>2]}b=b+1|0;if(c>>>0>b>>>0){continue}break}b=K[a+140>>2]}K[a+136>>2]=0;Ga(b);K[a+140>>2]=0}Ga(K[a+160>>2]);K[a+144>>2]=0;K[a+160>>2]=0;Ga(K[a+124>>2]);K[a+124>>2]=0;if(!(L[a+212|0]&2)){Ga(K[a+192>>2])}B(a+104|0,0,112);tb(K[a+216>>2]);K[a+216>>2]=0;tb(K[a+220>>2]);K[a+216>>2]=0;d=K[a+224>>2];if(d){b=K[d+28>>2];if(b){Ga(b);K[d+28>>2]=0}c=K[d+40>>2];if(c){if(K[d+36>>2]){while(1){e=Q(g,40);b=K[(e+c|0)+36>>2];if(b){Ga(b);c=K[d+40>>2];K[(e+c|0)+36>>2]=0}b=K[(c+e|0)+16>>2];if(b){Ga(b);c=K[d+40>>2];K[(e+c|0)+16>>2]=0}b=K[(c+e|0)+24>>2];if(b){Ga(b);c=K[d+40>>2];K[(e+c|0)+24>>2]=0}g=g+1|0;if(g>>>0<N[d+36>>2]){continue}break}}Ga(c);K[d+40>>2]=0}Ga(d)}K[a+224>>2]=0;Ya(K[a+96>>2]);K[a+96>>2]=0;Ya(K[a+100>>2]);K[a+100>>2]=0;f=K[a+236>>2];if(f){b:{if(!K[f+8>>2]){break b}if(K[f+12>>2]){K[f+40>>2]=0;while(1){if(K[f+24>>2]>0){continue}break}}K[f+16>>2]=1;Ga(K[f>>2]);c=K[f+28>>2];if(!c){break b}while(1){b=K[c+4>>2];Ga(c);K[f+28>>2]=b;c=b;if(b){continue}break}}d=K[f+36>>2];if(d){g=K[d+4>>2];if((g|0)>0){b=0;while(1){e=K[d>>2]+Q(b,12)|0;c=K[e+8>>2];if(c){va[c|0](K[e+4>>2]);g=K[d+4>>2]}b=b+1|0;if((g|0)>(b|0)){continue}break}}Ga(K[d>>2]);Ga(d)}Ga(f)}K[a+236>>2]=0;Ga(a)}}function oe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;g=ra-16|0;ra=g;if(K[a+8>>2]==16){h=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{h=K[a+12>>2]}a:{if(!c){Fa(d,1,4222,0);break a}i=K[a+96>>2];e=1;Ha(b,g+8|0,1);f=K[g+8>>2];if(f>>>0>=2){Fa(d,2,9755,0);break a}if((f+1|0)!=(c|0)){e=0;Fa(d,2,4222,0);break a}d=K[i+16>>2];b:{if(!d){break b}e=K[h+5584>>2];if(d>>>0>=8){i=d&-8;c=0;while(1){K[e+8636>>2]=0;K[e+7556>>2]=0;K[e+6476>>2]=0;K[e+5396>>2]=0;K[e+4316>>2]=0;K[e+3236>>2]=0;K[e+2156>>2]=0;K[e+1076>>2]=0;e=e+8640|0;c=c+8|0;if((i|0)!=(c|0)){continue}break}}d=d&7;if(!d){break b}c=0;while(1){K[e+1076>>2]=0;e=e+1080|0;c=c+1|0;if((d|0)!=(c|0)){continue}break}}c=K[h+5608>>2];if(c){Ga(c);K[h+5608>>2]=0;f=K[g+8>>2]}if(!f){e=1;break a}i=0;while(1){b=b+1|0;Ha(b,g+12|0,1);c:{if(!K[h+5632>>2]){break c}d=K[h+5628>>2];if(K[d>>2]!=K[g+12>>2]){break c}f=K[d+4>>2];j=K[a+96>>2];if((f|0)!=K[j+16>>2]){break c}c=K[d+8>>2];if(c){e=0;f=Q(f,f);if(K[c+16>>2]!=(Q(f,K[(K[c>>2]<<2)+24848>>2])|0)){break a}k=Ja(f<<2);K[h+5608>>2]=k;if(!k){break a}va[K[(K[c>>2]<<2)+25152>>2]](K[c+12>>2],k,f)}c=K[d+12>>2];if(!c){break c}e=0;d=K[j+16>>2];if(K[c+16>>2]!=(Q(d,K[(K[c>>2]<<2)+24848>>2])|0)){break a}f=Ja(d<<2);if(!f){break a}va[K[(K[c>>2]<<2)+25168>>2]](K[c+12>>2],f,d);c=K[j+16>>2];d:{if(!c){break d}j=c&7;e=K[h+5584>>2];e:{if(c>>>0<8){c=f;break e}k=c&-8;d=0;c=f;while(1){K[e+1076>>2]=K[c>>2];K[e+2156>>2]=K[c+4>>2];K[e+3236>>2]=K[c+8>>2];K[e+4316>>2]=K[c+12>>2];K[e+5396>>2]=K[c+16>>2];K[e+6476>>2]=K[c+20>>2];K[e+7556>>2]=K[c+24>>2];K[e+8636>>2]=K[c+28>>2];e=e+8640|0;c=c+32|0;d=d+8|0;if((k|0)!=(d|0)){continue}break}}d=0;if(!j){break d}while(1){K[e+1076>>2]=K[c>>2];e=e+1080|0;c=c+4|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}}Ga(f)}e=1;i=i+1|0;if(i>>>0<N[g+8>>2]){continue}break}}ra=g+16|0;return e|0}function Fb(a,b,c,d,e,f,g,h){var i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;m=K[K[a+24>>2]+24>>2]+Q(b,52)|0;l=K[m+4>>2];k=l-1|0;o=K[a+60>>2];j=k+o|0;p=0-!l|0;i=p;r=K[K[K[a+20>>2]>>2]+20>>2]+Q(b,76)|0;n=K[r+12>>2];i=Ne(j,j>>>0<o>>>0?i+1|0:i,l,0);q=i>>>0>n>>>0?n:i;j=K[m>>2];m=j-1|0;s=K[a+56>>2];n=m+s|0;o=0-!j|0;i=o;t=K[r+8>>2];i=Ne(n,n>>>0<s>>>0?i+1|0:i,j,0);n=i>>>0>t>>>0?t:i;i=p;t=K[r+4>>2];s=K[a+52>>2];k=s+k|0;i=Ne(k,k>>>0<s>>>0?i+1|0:i,l,0);k=i>>>0<t>>>0?t:i;i=o;p=K[r>>2];l=m;m=K[a+48>>2];l=l+m|0;i=Ne(l,l>>>0<m>>>0?i+1|0:i,j,0);i=i>>>0<p>>>0?p:i;l=0;p=K[(K[K[a+32>>2]+5584>>2]+Q(b,1080)|0)+20>>2];c=K[r+20>>2]+(c?0-c|0:-1)|0;a:{if(!c){a=n;l=i;b=k;break a}m=c-1|0;j=(d&1)<<m;if(j>>>0<i>>>0){a=c&31;l=i-j|0;if((c&63)>>>0>=32){i=-1<<a;a=0}else{b=(1<<a)-1&-1>>>32-a;a=-1<<a;i=b|a}b=a^-1;a=l+b|0;i=i^-1;i=a>>>0<b>>>0?i+1|0:i;b=a;a=c&31;if((c&63)>>>0>=32){l=i>>>a|0}else{l=((1<<a)-1&i)<<32-a|b>>>a}}a=0;b=0;d=d>>>1<<m;if(d>>>0<k>>>0){b=c&31;o=k-d|0;if((c&63)>>>0>=32){i=-1<<b;b=0}else{i=(1<<b)-1&-1>>>32-b;b=-1<<b;i=i|b}k=b^-1;b=o+k|0;i=i^-1;i=b>>>0<k>>>0?i+1|0:i;k=b;b=c&31;if((c&63)>>>0>=32){b=i>>>b|0}else{b=((1<<b)-1&i)<<32-b|k>>>b}}if(j>>>0<n>>>0){a=c&31;k=n-j|0;if((c&63)>>>0>=32){i=-1<<a;a=0}else{i=(1<<a)-1&-1>>>32-a;a=-1<<a;i=i|a}j=a^-1;a=k+j|0;i=i^-1;i=a>>>0<j>>>0?i+1|0:i;j=a;a=c&31;if((c&63)>>>0>=32){a=i>>>a|0}else{a=((1<<a)-1&i)<<32-a|j>>>a}}if(d>>>0>=q>>>0){q=0;break a}k=q-d|0;d=c&31;if((c&63)>>>0>=32){i=-1<<d;d=0}else{i=(1<<d)-1&-1>>>32-d;d=-1<<d;i=i|d}j=d^-1;d=k+j|0;i=i^-1;i=d>>>0<j>>>0?i+1|0:i;j=d;d=c&31;if((c&63)>>>0>=32){q=i>>>d|0}else{q=((1<<d)-1&i)<<32-d|j>>>d}}c=(p|0)==1?2:3;d=c+a|0;d=(a>>>0>d>>>0?-1:d)>>>0>e>>>0;a=c+q|0;d=d&(a>>>0<q>>>0?-1:a)>>>0>f>>>0;a=l-c|0;d=d&(a>>>0<=l>>>0?a:0)>>>0<g>>>0;a=b-c|0;return d&(a>>>0<=b>>>0?a:0)>>>0<h>>>0}function Ie(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;e=ra-80|0;ra=e;K[e+76>>2]=1;k=K[a+44>>2];d=K[K[a+224>>2]+40>>2];a:{b:{if(!d|!K[d+16>>2]){break b}c:{d=d+Q(k,40)|0;if(!K[d+4>>2]){d=K[a+52>>2];f=K[a+48>>2]+2|0;d=f>>>0<2?d+1|0:d;if(ib(b,f,d,c)){break c}Fa(c,1,5403,0);break a}d=K[d+16>>2];if(!ib(b,K[d>>2],K[d+4>>2],c)){Fa(c,1,5403,0);break a}if((Na(b,K[a+16>>2],2,c)|0)!=2){Fa(c,1,2435,0);break a}Ha(K[a+16>>2],e+72|0,2);if(K[e+72>>2]==65424){break c}Fa(c,1,4036,0);break a}if(K[a+8>>2]!=256){break b}K[a+8>>2]=8}h=Q(K[a+132>>2],K[a+128>>2]);d:{if(!h){break d}f=K[a+180>>2];d=0;if(h>>>0>=8){i=h&-8;while(1){K[(f+Q(d,5644)|0)+5588>>2]=-1;K[(f+Q(d|1,5644)|0)+5588>>2]=-1;K[(f+Q(d|2,5644)|0)+5588>>2]=-1;K[(f+Q(d|3,5644)|0)+5588>>2]=-1;K[(f+Q(d|4,5644)|0)+5588>>2]=-1;K[(f+Q(d|5,5644)|0)+5588>>2]=-1;K[(f+Q(d|6,5644)|0)+5588>>2]=-1;K[(f+Q(d|7,5644)|0)+5588>>2]=-1;d=d+8|0;j=j+8|0;if((i|0)!=(j|0)){continue}break}}h=h&7;if(!h){break d}while(1){K[(f+Q(d,5644)|0)+5588>>2]=-1;d=d+1|0;g=g+1|0;if((h|0)!=(g|0)){continue}break}}g=0;if(!ab(a,e+72|0,0,e+68|0,e- -64|0,e+60|0,e+56|0,e+52|0,e+76|0,b,c)){break a}h=k+1|0;while(1){e:{if(!K[e+76>>2]){break e}d=K[e+72>>2];if(!jb(a,d,0,0,b,c)){break a}i=K[a+128>>2];j=K[a+132>>2];f=d+1|0;K[e+32>>2]=f;K[e+36>>2]=Q(i,j);Fa(c,4,11758,e+32|0);if(!Wc(K[a+232>>2],K[K[a+100>>2]+24>>2])){break a}g=K[a+180>>2]+Q(d,5644)|0;i=K[g+5596>>2];if(i){Ga(i);K[g+5596>>2]=0;K[g+5600>>2]=0}K[e+16>>2]=f;Fa(c,4,16564,e+16|0);if((d|0)==(k|0)){d=K[a+224>>2];f=K[d+8>>2];d=K[d+12>>2];f=f+2|0;d=f>>>0<2?d+1|0:d;if(ib(b,f,d,c)){break e}g=0;Fa(c,1,5403,0);break a}K[e+4>>2]=h;K[e>>2]=f;Fa(c,2,13611,e);g=0;if(ab(a,e+72|0,0,e+68|0,e- -64|0,e+60|0,e+56|0,e+52|0,e+76|0,b,c)){continue}break a}break}g=Vc(a,c)}ra=e+80|0;return g|0}function uc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;j=ra-256|0;ra=j;a:{if(!a){a=0;break a}if(!(K[a>>2]==(b|0)&K[a+4>>2]==(c|0))){K[a+4>>2]=c;K[a>>2]=b;K[j>>2]=c;K[j+128>>2]=b;e=c;g=b;while(1){o=i;i=i+1|0;h=i<<2;n=(e+1|0)/2|0;K[h+j>>2]=n;k=h+(j+128|0)|0;h=(g+1|0)/2|0;K[k>>2]=h;m=Q(e,g);f=m+f|0;e=n;g=h;if(m>>>0>1){continue}break}K[a+8>>2]=f;b:{c:{d:{if(!f){b=K[a+12>>2];if(!b){break d}Ga(b);K[a+12>>2]=0;break d}e=f<<4;if(e>>>0<=N[a+16>>2]){break b}f=La(K[a+12>>2],e);if(f){break c}Fa(d,1,6414,0);b=K[a+12>>2];if(!b){break d}Ga(b);K[a+12>>2]=0}Ga(a);a=0;break a}K[a+12>>2]=f;c=K[a+16>>2];b=e-c|0;if(b){B(c+f|0,0,b)}K[a+16>>2]=e;c=K[a+4>>2];b=K[a>>2]}g=K[a+12>>2];if(o){d=0;e=(Q(b,c)<<4)+g|0;f=e;while(1){b=d<<2;k=K[b+j>>2];e:{if((k|0)<=0){break e}m=k-1|0;l=0;f:{g:{c=K[b+(j+128|0)>>2];if((c|0)<=0){n=k&1;i=0;if((k|0)!=1){break g}b=f;break f}while(1){b=f;f=c;while(1){h:{K[g>>2]=e;if((f|0)==1){g=g+16|0;e=e+16|0;break h}K[g+16>>2]=e;e=e+16|0;g=g+32|0;h=(f|0)>2;f=f-2|0;if(h){continue}}break}h=((l|0)==(m|0)|l)&1;f=h?e:b+(c<<4)|0;e=h?e:b;l=l+1|0;if((k|0)!=(l|0)){continue}break}break e}h=k&2147483646;while(1){b=(i|0)==(m|0);i=i+2|0;e=b?e:f;f=e;b=e;l=l+2|0;if((h|0)!=(l|0)){continue}break}}if(!n){f=e;break e}f=(c<<4)+b|0;c=((i|0)==(m|0)|i)&1;f=c?e:f;e=c?e:b}d=d+1|0;if((o|0)!=(d|0)){continue}break}}K[g>>2]=0}c=K[a+8>>2];if(!c){break a}e=K[a+12>>2];if(c>>>0>=4){b=c&-4;g=0;while(1){K[e+60>>2]=0;K[e+52>>2]=999;K[e+56>>2]=0;K[e+44>>2]=0;K[e+36>>2]=999;K[e+40>>2]=0;K[e+28>>2]=0;K[e+20>>2]=999;K[e+24>>2]=0;K[e+12>>2]=0;K[e+4>>2]=999;K[e+8>>2]=0;e=e- -64|0;g=g+4|0;if((b|0)!=(g|0)){continue}break}}b=c&3;if(!b){break a}g=0;while(1){K[e+12>>2]=0;K[e+4>>2]=999;K[e+8>>2]=0;e=e+16|0;g=g+1|0;if((b|0)!=(g|0)){continue}break}}ra=j+256|0;return a}function pb(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;g=K[a+16>>2];if(g>>>0>=32){return K[a+8>>2]}d=K[a+20>>2];a:{if((d|0)>=4){b=K[a>>2];c=K[b-3>>2];d=d-4|0;K[a+20>>2]=d;K[a>>2]=b-4;break a}if((d|0)<=0){break a}k=d&1;b=K[a>>2];b:{if((d|0)==1){e=24;break b}j=d&2147483646;e=24;while(1){h=b-1|0;K[a>>2]=h;i=L[b|0];b=b-2|0;K[a>>2]=b;K[a+20>>2]=d-1;h=L[h|0];d=d-2|0;K[a+20>>2]=d;c=i<<e|c|h<<e-8;e=e-16|0;f=f+2|0;if((j|0)!=(f|0)){continue}break}}if(k){K[a>>2]=b-1;b=L[b|0];K[a+20>>2]=d-1;c=b<<e|c}d=0}b=K[a+24>>2];j=c&255;K[a+24>>2]=j>>>0>143;b=b?(c&2130706432)==2130706432?7:8:8;h=b+(c>>>0<=2415919103?8:(c&8323072)==8323072?7:8)|0;f=c>>>16&255;i=h+(f>>>0<=143?8:(c&32512)==32512?7:8)|0;e=c>>>8&255;k=i+(g+(e>>>0<=143?8:(c&127)==127?7:8)|0)|0;K[a+16>>2]=k;l=K[a+12>>2];b=f<<b|c>>>24|e<<h|j<<i;c=g&31;if((g&63)>>>0>=32){e=b<<c;b=0}else{e=(1<<c)-1&b>>>32-c;b=b<<c}g=b|K[a+8>>2];b=e|l;h=b;K[a+8>>2]=g;K[a+12>>2]=b;if(k>>>0<=31){c:{if((d|0)>=4){b=K[a>>2];c=K[b-3>>2];K[a+20>>2]=d-4;K[a>>2]=b-4;break c}if((d|0)<=0){c=0;break c}i=d&1;b=K[a>>2];d:{if((d|0)==1){e=24;c=0;break d}l=d&2147483646;e=24;c=0;f=0;while(1){m=b-1|0;K[a>>2]=m;n=L[b|0];b=b-2|0;K[a>>2]=b;K[a+20>>2]=d-1;m=L[m|0];d=d-2|0;K[a+20>>2]=d;c=n<<e|c|m<<e-8;e=e-16|0;f=f+2|0;if((l|0)!=(f|0)){continue}break}}if(!i){break c}K[a>>2]=b-1;b=L[b|0];K[a+20>>2]=d-1;c=b<<e|c}d=c&255;K[a+24>>2]=d>>>0>143;j=j>>>0<=143?8:(c&2130706432)==2130706432?7:8;i=j+(c>>>0<=2415919103?8:(c&8323072)==8323072?7:8)|0;f=c>>>16&255;l=i+(f>>>0<=143?8:(c&32512)==32512?7:8)|0;e=c>>>8&255;K[a+16>>2]=l+((e>>>0<=143?8:(c&127)==127?7:8)+k|0);b=a;a=f<<j|c>>>24|e<<i|d<<l;c=k&31;if((k&63)>>>0>=32){d=a<<c;a=0}else{d=(1<<c)-1&a>>>32-c;a=a<<c}g=a|g;K[b+8>>2]=g;K[b+12>>2]=d|h}return g}function cd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;j=K[a+96>>2];l=Q(K[a+128>>2],K[a+132>>2]);a:{if(l){b=K[j+16>>2];m=Q(b,1080);k=Q(b,b)<<2;e=K[a+12>>2];b=K[a+180>>2];while(1){n=K[b+5584>>2];E(b,e,5644);K[b+5608>>2]=0;K[b+5588>>2]=-1;K[b+5168>>2]=0;K[b+5636>>2]=0;K[b+5616>>2]=0;K[b+5624>>2]=0;K[b+5628>>2]=0;K[b+5584>>2]=n;I[b+5640|0]=L[b+5640|0]&252;b:{if(!K[e+5608>>2]){break b}d=Ja(k);K[b+5608>>2]=d;if(!d){return 0}if(!k){break b}E(d,K[e+5608>>2],k)}d=Q(K[e+5624>>2],20);f=Ja(d);K[b+5616>>2]=f;i=0;if(!f){break a}if(d){E(f,K[e+5616>>2],d)}g=K[e+5620>>2];if(g){d=K[e+5616>>2];f=K[b+5616>>2];h=0;while(1){if(K[d+12>>2]){g=Ja(K[d+16>>2]);K[f+12>>2]=g;if(!g){return 0}o=K[d+16>>2];if(o){E(g,K[d+12>>2],o)}g=K[e+5620>>2]}K[b+5624>>2]=K[b+5624>>2]+1;f=f+20|0;d=d+20|0;h=h+1|0;if(h>>>0<g>>>0){continue}break}}d=Q(K[e+5636>>2],20);f=Ja(d);K[b+5628>>2]=f;if(!f){break a}if(d){E(f,K[e+5628>>2],d)}i=K[e+5636>>2];K[b+5636>>2]=i;if(i){d=K[e+5628>>2];f=K[b+5628>>2];h=0;while(1){g=K[d+8>>2];if(g){K[f+8>>2]=K[b+5616>>2]+(g-K[e+5616>>2]|0)}g=K[d+12>>2];if(g){K[f+12>>2]=K[b+5616>>2]+(g-K[e+5616>>2]|0)}f=f+20|0;d=d+20|0;h=h+1|0;if((i|0)!=(h|0)){continue}break}}if(m){E(n,K[e+5584>>2],m)}b=b+5644|0;p=p+1|0;if((p|0)!=(l|0)){continue}break}}i=1;e=Ia(1,72);b=0;c:{if(!e){break c}I[e+40|0]=L[e+40|0]&254|1;d=Ia(1,4);K[e+20>>2]=d;b=e;if(d){break c}Ga(b);b=0}K[a+232>>2]=b;if(!b){return 0}f=K[a+236>>2];e=0;K[b+28>>2]=a+104;K[b+24>>2]=j;d=Ia(1,848);K[K[b+20>>2]>>2]=d;d:{if(!d){break d}d=Ia(K[j+16>>2],76);h=K[K[b+20>>2]>>2];K[h+20>>2]=d;if(!d){break d}K[h+16>>2]=K[j+16>>2];e=K[a+188>>2];K[b+44>>2]=f;K[b>>2]=e;e=1}if(e){break a}hc(K[a+232>>2]);i=0;K[a+232>>2]=0;Fa(c,1,3631,0)}return i|0}function Qa(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;h=K[a+16>>2];if(h>>>0>=32){return K[a+8>>2]}d=K[a+24>>2];a:{if((d|0)>=4){b=K[a>>2];c=K[b>>2];g=d-4|0;K[a+24>>2]=g;K[a>>2]=b+4;break a}c=K[a+28>>2]?-1:0;if((d|0)<=0){g=d;break a}j=d&1;b=K[a>>2];b:{if((d|0)==1){f=b;break b}i=d&2147483646;while(1){K[a>>2]=b+1;k=L[b|0];f=b+2|0;K[a>>2]=f;K[a+24>>2]=d-1;b=L[b+1|0];d=d-2|0;K[a+24>>2]=d;c=((255<<e^-1)&c|k<<e)&(65280<<e^-1)|b<<(e|8);e=e+16|0;b=f;g=g+2|0;if((i|0)!=(g|0)){continue}break}}g=0;if(!j){break a}K[a>>2]=f+1;b=L[f|0];K[a+24>>2]=d-1;c=(255<<e^-1)&c|b<<e}b=K[a+20>>2];i=c>>>24|0;K[a+20>>2]=(i|0)==255;f=c>>>16&255;d=c>>>8&255;b=b?7:8;c=c&255;e=b+((c|0)==255?7:8)|0;k=((d|0)==255?7:8)+e|0;j=(h+((f|0)==255?7:8)|0)+k|0;K[a+16>>2]=j;l=K[a+12>>2];b=c|(d<<b|f<<e|i<<k);c=h&31;if((h&63)>>>0>=32){f=b<<c;b=0}else{f=(1<<c)-1&b>>>32-c;b=b<<c}h=b|K[a+8>>2];b=f|l;k=b;K[a+8>>2]=h;K[a+12>>2]=b;if(j>>>0<=31){c:{if((g|0)>=4){b=K[a>>2];d=K[b>>2];K[a+24>>2]=g-4;K[a>>2]=b+4;break c}e=0;d=K[a+28>>2]?-1:0;if((g|0)<=0){break c}l=g&1;b=K[a>>2];d:{if((g|0)==1){c=b;break d}m=g&2147483646;f=0;while(1){K[a>>2]=b+1;n=L[b|0];c=b+2|0;K[a>>2]=c;K[a+24>>2]=g-1;b=L[b+1|0];g=g-2|0;K[a+24>>2]=g;d=((255<<e^-1)&d|n<<e)&(65280<<e^-1)|b<<(e|8);e=e+16|0;b=c;f=f+2|0;if((m|0)!=(f|0)){continue}break}}if(!l){break c}K[a>>2]=c+1;b=L[c|0];K[a+24>>2]=g-1;d=(255<<e^-1)&d|b<<e}c=d>>>24|0;K[a+20>>2]=(c|0)==255;f=d>>>16&255;g=d>>>8&255;e=(i|0)==255?7:8;d=d&255;i=e+((d|0)==255?7:8)|0;l=((g|0)==255?7:8)+i|0;K[a+16>>2]=(((f|0)==255?7:8)+j|0)+l;b=a;a=d|(g<<e|f<<i|c<<l);c=j&31;if((j&63)>>>0>=32){f=a<<c;a=0}else{f=(1<<c)-1&a>>>32-c;a=a<<c}h=a|h;K[b+8>>2]=h;K[b+12>>2]=f|k}return h}function _c(a,b,c,d,e){var f=0,g=0,h=0,i=0,j=0,k=0;i=ra-32|0;ra=i;if(K[a+8>>2]==16){f=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{f=K[a+12>>2]}a:{if(N[d>>2]<=4){a=0;Fa(e,1,2570,0);break a}f=K[f+5584>>2]+Q(b,1080)|0;Ha(c,f+4|0,1);h=K[f+4>>2]+1|0;K[f+4>>2]=h;if(h>>>0>=34){K[i+4>>2]=33;K[i>>2]=h;Fa(e,1,7598,i);a=0;break a}g=K[a+184>>2];if(g>>>0>=h>>>0){K[i+24>>2]=h;K[i+20>>2]=g;K[i+16>>2]=b;Fa(e,1,16386,i+16|0);K[a+8>>2]=K[a+8>>2]|32768;a=0;break a}Ha(c+1|0,f+8|0,1);K[f+8>>2]=K[f+8>>2]+2;Ha(c+2|0,f+12|0,1);a=K[f+12>>2]+2|0;K[f+12>>2]=a;b=K[f+8>>2];if(!(!(b>>>0>10|a>>>0>10)&a+b>>>0<13)){a=0;Fa(e,1,5431,0);break a}Ha(c+3|0,f+16|0,1);if(L[f+16|0]&128){a=0;Fa(e,1,6527,0);break a}Ha(c+4|0,f+20|0,1);if(N[f+20>>2]>=2){a=0;Fa(e,1,6462,0);break a}b=K[d>>2]-5|0;K[d>>2]=b;a=1;h=K[f+4>>2];if(!(I[f|0]&1)){if(!h){break a}d=f+944|0;e=f+812|0;b=0;c=0;if(h>>>0>=4){k=h&-4;g=0;while(1){f=c<<2;K[f+e>>2]=15;K[d+f>>2]=15;j=f|4;K[j+e>>2]=15;K[d+j>>2]=15;j=f|8;K[j+e>>2]=15;K[d+j>>2]=15;f=f|12;K[f+e>>2]=15;K[d+f>>2]=15;c=c+4|0;g=g+4|0;if((k|0)!=(g|0)){continue}break}}f=h&3;if(!f){break a}while(1){a=c<<2;K[a+e>>2]=15;K[a+d>>2]=15;a=1;c=c+1|0;b=b+1|0;if((f|0)!=(b|0)){continue}break}break a}if(b>>>0>=h>>>0){b:{if(!h){g=0;break b}Ha(c+5|0,i+28|0,1);a=K[i+28>>2];K[f+944>>2]=a>>>4;K[f+812>>2]=a&15;g=K[f+4>>2];if(g>>>0>=2){h=f+944|0;k=f+812|0;a=c+6|0;c=1;while(1){Ha(a,i+28|0,1);c:{b=K[i+28>>2];if(b>>>0>=16){g=b&15;if(g){break c}}a=0;Fa(e,1,5988,0);break a}j=c<<2;K[j+k>>2]=g;K[h+j>>2]=b>>>4;a=a+1|0;c=c+1|0;g=K[f+4>>2];if(c>>>0<g>>>0){continue}break}}b=K[d>>2]}K[d>>2]=b-g;a=1;break a}a=0;Fa(e,1,2570,0)}ra=i+32|0;return a}function nc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;K[a+8>>2]=0;K[a+12>>2]=0;K[a>>2]=b;K[a+28>>2]=d;K[a+16>>2]=0;K[a+20>>2]=0;h=c-1|0;K[a+24>>2]=h;n=b&3;a:{if((c|0)<=0){e=b;b=d;break a}e=b+1|0;K[a>>2]=e;b=L[b|0]}g=b;i=8;K[a+16>>2]=8;j=(g|0)==255;K[a+20>>2]=j;K[a+8>>2]=g;K[a+12>>2]=0;b:{if((n|0)==3){break b}k=c-2|0;K[a+24>>2]=k;c:{if((c|0)<2){b=e;e=d;break c}b=e+1|0;K[a>>2]=b;e=L[e|0]}j=(e|0)==255;K[a+20>>2]=j;i=(g|0)==255?15:16;K[a+16>>2]=i;g=g|e<<8;K[a+8>>2]=g;K[a+12>>2]=0;if((n|0)==2){e=b;c=h;h=k;break b}o=c-3|0;K[a+24>>2]=o;d:{if((c|0)<3){f=b;b=d;break d}f=b+1|0;K[a>>2]=f;b=L[b|0]}j=(b|0)==255;K[a+20>>2]=j;l=((e|0)==255?7:8)+i|0;K[a+16>>2]=l;e=i&31;if((i&63)>>>0>=32){m=b<<e;e=0}else{m=(1<<e)-1&b>>>32-e;e=b<<e}g=e|g;K[a+8>>2]=g;K[a+12>>2]=m;if((n|0)==1){e=f;i=l;c=k;h=o;break b}h=c-4|0;K[a+24>>2]=h;e:{if((c|0)<4){e=f;c=d;break e}e=f+1|0;K[a>>2]=e;c=L[f|0]}j=(c|0)==255;K[a+20>>2]=j;i=l+((b|0)==255?7:8)|0;K[a+16>>2]=i;b=l&31;if((l&63)>>>0>=32){f=c<<b;b=0}else{f=(1<<b)-1&c>>>32-b;b=c<<b}g=b|g;b=f|m;m=b;K[a+8>>2]=g;K[a+12>>2]=b;c=o}f:{if((c|0)>=5){d=K[e>>2];K[a+24>>2]=c-5;K[a>>2]=e+4;break f}b=0;d=d?-1:0;if((c|0)<2){break f}while(1){c=e+1|0;K[a>>2]=c;e=L[e|0];f=h-1|0;K[a+24>>2]=f;d=(255<<b^-1)&d|e<<b;b=b+8|0;k=h>>>0>1;e=c;h=f;if(k){continue}break}}b=d>>>24|0;K[a+20>>2]=(b|0)==255;c=d>>>16&255;e=d>>>8&255;h=j?7:8;d=d&255;f=h+((d|0)==255?7:8)|0;k=((e|0)==255?7:8)+f|0;K[a+16>>2]=(((c|0)==255?7:8)+i|0)+k;b=d|(e<<h|c<<f|b<<k);c=a;c=a;a=b;b=i&31;if((i&63)>>>0>=32){d=a<<b;a=0}else{d=(1<<b)-1&a>>>32-b;a=a<<b}K[c+8>>2]=a|g;K[c+12>>2]=d|m}function Db(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;j=ra-32|0;ra=j;p=K[a+16>>2];a:{if(!p){k=1;break a}d=K[a>>2];c=d>>31;h=c;b:{if((c|0)<0){break b}e=K[a+4>>2];c=e>>31;l=c;if((c|0)<0){break b}f=K[a+8>>2];c=f>>31;m=c;if((c|0)<0){break b}i=K[a+12>>2];c=i>>31;if((c|0)<0){break b}a=K[a+24>>2];s=d-1|0;t=h-!d|0;u=e-1|0;v=l-!e|0;w=f-1|0;x=m-!f|0;y=i-1|0;z=c-!i|0;while(1){c=t;d=K[a>>2];e=d+s|0;c=d>>>0>e>>>0?c+1|0:c;h=Ne(e,c,d,0);K[a+16>>2]=h;c=v;e=K[a+4>>2];f=e+u|0;c=e>>>0>f>>>0?c+1|0:c;l=Ne(f,c,e,0);K[a+20>>2]=l;i=K[a+40>>2];f=i&31;if((i&63)>>>0>=32){c=1<<f;g=0}else{g=1<<f;c=g-1&1>>>32-f}n=g;k=c;f=n-1|0;c=c-!n|0;m=c;q=d>>31;g=q+x|0;r=d+w|0;g=r>>>0<d>>>0?g+1|0:g;d=Me(r,g,d,q);c=(d>>31)+c|0;g=d;d=d+f|0;c=g>>>0>d>>>0?c+1|0:c;g=d;d=i&31;if((i&63)>>>0>=32){d=c>>d}else{d=((1<<d)-1&c)<<32-d|g>>>d}c=(h>>31)+m|0;g=h;h=f+h|0;c=g>>>0>h>>>0?c+1|0:c;g=d;d=i&31;if((i&63)>>>0>=32){c=c>>d}else{c=((1<<d)-1&c)<<32-d|h>>>d}c=g-c|0;if((c|0)<0){K[j+4>>2]=c;K[j>>2]=o;Fa(b,1,13473,j);k=0;break a}K[a+8>>2]=c;d=e>>31;c=d+z|0;h=e+y|0;c=h>>>0<e>>>0?c+1|0:c;d=Me(h,c,e,d);c=(d>>31)+m|0;e=d;d=d+f|0;c=e>>>0>d>>>0?c+1|0:c;e=d;d=i&31;if((i&63)>>>0>=32){e=c>>d}else{e=((1<<d)-1&c)<<32-d|e>>>d}c=k+(l>>31)|0;d=l+n|0;c=d>>>0<n>>>0?c+1|0:c;f=d-1|0;h=e;d=c-!d|0;e=f;c=i&31;if((i&63)>>>0>=32){c=d>>c}else{c=((1<<c)-1&d)<<32-c|e>>>c}c=h-c|0;if((c|0)<0){K[j+20>>2]=c;K[j+16>>2]=o;Fa(b,1,13542,j+16|0);k=0;break a}K[a+12>>2]=c;a=a+52|0;k=1;o=o+1|0;if((p|0)!=(o|0)){continue}break}break a}Fa(b,1,6683,0)}ra=j+32|0;return k}function Ge(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;g=ra-16|0;ra=g;K[g+12>>2]=c;h=K[a+96>>2];if(K[a+8>>2]==16){e=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{e=K[a+12>>2]}I[e+5640|0]=L[e+5640|0]|1;a:{if(c>>>0<=4){Fa(d,1,4528,0);break a}Ha(b,e,1);if(N[e>>2]>=8){Fa(d,1,4494,0);break a}Ha(b+1|0,g+8|0,1);c=K[g+8>>2];K[e+4>>2]=c;if((c|0)>=5){Fa(d,1,4453,0);K[e+4>>2]=-1}Ha(b+2|0,e+8|0,2);c=K[e+8>>2];if(c-65536>>>0<=4294901760){K[g>>2]=c;Fa(d,1,8074,g);break a}i=K[a+188>>2];K[e+12>>2]=i?i:c;Ha(b+4|0,e+16|0,1);if(N[e+16>>2]>=2){Fa(d,1,5499,0);break a}i=b+5|0;K[g+12>>2]=K[g+12>>2]-5;h=K[h+16>>2];b:{if(!h){break b}b=K[e>>2]&1;c=K[e+5584>>2];e=0;if(h>>>0>=8){k=h&-8;while(1){K[c+Q(f,1080)>>2]=b;K[c+Q(f|1,1080)>>2]=b;K[c+Q(f|2,1080)>>2]=b;K[c+Q(f|3,1080)>>2]=b;K[c+Q(f|4,1080)>>2]=b;K[c+Q(f|5,1080)>>2]=b;K[c+Q(f|6,1080)>>2]=b;K[c+Q(f|7,1080)>>2]=b;f=f+8|0;j=j+8|0;if((k|0)!=(j|0)){continue}break}}h=h&7;if(!h){break b}while(1){K[c+Q(f,1080)>>2]=b;f=f+1|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}}f=0;if(!_c(a,0,i,g+12|0,d)){Fa(d,1,4528,0);break a}if(K[g+12>>2]){Fa(d,1,4528,0);break a}if(K[a+8>>2]==16){b=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{b=K[a+12>>2]}if(N[K[a+96>>2]+16>>2]>=2){b=K[b+5584>>2];d=K[b+4>>2]<<2;f=b+944|0;h=b+812|0;e=1;c=b;while(1){K[c+1084>>2]=K[b+4>>2];K[c+1088>>2]=K[b+8>>2];K[c+1092>>2]=K[b+12>>2];K[c+1096>>2]=K[b+16>>2];K[c+1100>>2]=K[b+20>>2];i=!d;if(!i){E(c+1892|0,h,d)}if(!i){E(c+2024|0,f,d)}c=c+1080|0;e=e+1|0;if(e>>>0<N[K[a+96>>2]+16>>2]){continue}break}}f=1}ra=g+16|0;return f|0}function wc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;j=ra-256|0;ra=j;f=Ia(1,20);a:{if(!f){Fa(c,1,6376,0);f=0;break a}K[f+4>>2]=b;K[f>>2]=a;K[j>>2]=b;K[j+128>>2]=a;while(1){p=g;g=g+1|0;h=g<<2;d=(b+1|0)/2|0;K[h+j>>2]=d;m=h+(j+128|0)|0;h=(a+1|0)/2|0;K[m>>2]=h;i=Q(a,b);e=i+e|0;b=d;a=h;if(i>>>0>1){continue}break}K[f+8>>2]=e;if(!e){Ga(f);f=0;break a}d=Ia(e,16);K[f+12>>2]=d;if(!d){Fa(c,1,3527,0);Ga(f);f=0;break a}l=K[f+8>>2];K[f+16>>2]=l<<4;a=d;if(p){e=(Q(K[f+4>>2],K[f>>2])<<4)+d|0;b=e;while(1){c=n<<2;i=K[c+j>>2];b:{if((i|0)<=0){break b}o=i-1|0;h=0;c:{c=K[c+(j+128|0)>>2];if((c|0)<=0){g=0;if((i|0)!=1){k=i&2147483646;while(1){m=(g|0)==(o|0);g=g+2|0;e=m?b:e;b=e;h=h+2|0;if((k|0)!=(h|0)){continue}break}}if(i&1){break c}b=e;break b}while(1){g=e;e=c;while(1){d:{K[a>>2]=b;if((e|0)==1){a=a+16|0;b=b+16|0;break d}K[a+16>>2]=b;b=b+16|0;a=a+32|0;k=(e|0)>2;e=e-2|0;if(k){continue}}break}k=((h|0)==(o|0)|h)&1;e=k?b:g+(c<<4)|0;b=k?b:g;h=h+1|0;if((i|0)!=(h|0)){continue}break}break b}g=((g|0)==(o|0)|g)&1;c=g?b:(c<<4)+e|0;b=g?b:e;e=c}n=n+1|0;if((n|0)!=(p|0)){continue}break}}K[a>>2]=0;e:{if(!l){break e}if(l>>>0>=4){a=l&-4;b=0;while(1){K[d+60>>2]=0;K[d+52>>2]=999;K[d+56>>2]=0;K[d+44>>2]=0;K[d+36>>2]=999;K[d+40>>2]=0;K[d+28>>2]=0;K[d+20>>2]=999;K[d+24>>2]=0;K[d+12>>2]=0;K[d+4>>2]=999;K[d+8>>2]=0;d=d- -64|0;b=b+4|0;if((a|0)!=(b|0)){continue}break}}a=l&3;if(!a){break e}b=0;while(1){K[d+12>>2]=0;K[d+4>>2]=999;K[d+8>>2]=0;d=d+16|0;b=b+1|0;if((a|0)!=(b|0)){continue}break}}}ra=j+256|0;return f}function La(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;if(!b){return 0}a:{if(!a){a=mb(8,b);break a}if(!b){Ga(a);a=0;break a}b:{if(b>>>0>4294967239){break b}h=b>>>0<=8?8:b+3&-4;b=h+8|0;c:{d:{k=a-4|0;f=k;c=K[f>>2];e=c+f|0;j=K[e>>2];g=j+e|0;e:{f:{if(K[g-4>>2]!=(j|0)){d=b+f|0;if(d+16>>>0<=g>>>0){c=K[e+4>>2];e=K[e+8>>2];K[c+8>>2]=e;K[e+4>>2]=c;c=g-d|0;K[d>>2]=c;K[(d+(c&-4)|0)-4>>2]=c|1;e=K[d>>2]-8|0;g:{if(e>>>0<=127){c=(e>>>3|0)-1|0;break g}g=T(e);c=((e>>>29-g^4)-(g<<2)|0)+110|0;if(e>>>0<=4095){break g}c=((e>>>30-g^2)-(g<<1)|0)+71|0;c=c>>>0>=63?63:c}e=c<<4;K[d+4>>2]=e+26352;e=e+26360|0;K[d+8>>2]=K[e>>2];K[e>>2]=d;K[K[d+8>>2]+4>>2]=d;e=K[6847];d=c&31;if((c&63)>>>0>=32){c=1<<d;g=0}else{g=1<<d;c=g-1&1>>>32-d}K[6846]=g|K[6846];K[6847]=c|e;K[f>>2]=b;break d}if(d>>>0>g>>>0){break f}b=K[e+4>>2];d=K[e+8>>2];K[b+8>>2]=d;K[d+4>>2]=b;b=c+j|0;K[f>>2]=b;break d}if(c>>>0>=b+16>>>0){K[f>>2]=b;K[(f+(b&-4)|0)-4>>2]=b;d=b+f|0;b=c-b|0;K[d>>2]=b;K[(d+(b&-4)|0)-4>>2]=b|1;c=K[d>>2]-8|0;h:{if(c>>>0<=127){b=(c>>>3|0)-1|0;break h}f=T(c);b=((c>>>29-f^4)-(f<<2)|0)+110|0;if(c>>>0<=4095){break h}b=((c>>>30-f^2)-(f<<1)|0)+71|0;b=b>>>0>=63?63:b}c=b<<4;K[d+4>>2]=c+26352;c=c+26360|0;K[d+8>>2]=K[c>>2];K[c>>2]=d;K[K[d+8>>2]+4>>2]=d;c=K[6847];d=b&31;if((b&63)>>>0>=32){b=1<<d;e=0}else{e=1<<d;b=e-1&1>>>32-d}K[6846]=e|K[6846];K[6847]=b|c;d=1;break c}d=1;if(b>>>0<=c>>>0){break e}}d=0}break c}K[(f+(b&-4)|0)-4>>2]=b;d=1}if(d){break a}b=mb(8,h);if(!b){break b}i=K[k>>2]-8|0;hb(b,a,h>>>0<i>>>0?h:i);Ga(a);i=b}a=i}return a}function Ub(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0;a:{d=Ia(1,48);if(d){b=K[a+224>>2];c=K[b+4>>2];K[d>>2]=K[b>>2];K[d+4>>2]=c;c=K[b+12>>2];K[d+8>>2]=K[b+8>>2];K[d+12>>2]=c;c=K[b+20>>2];K[d+16>>2]=K[b+16>>2];K[d+20>>2]=c;c=K[b+24>>2];K[d+24>>2]=c;f=Ja(Q(c,24));K[d+28>>2]=f;if(!f){Ga(d);return 0}b=K[K[a+224>>2]+28>>2];b:{if(b){c=Q(K[d+24>>2],24);if(!c){break b}E(f,b,c);break b}Ga(f);K[d+28>>2]=0}c=K[K[a+224>>2]+36>>2];K[d+36>>2]=c;b=Ia(c,40);K[d+40>>2]=b;if(!b){Ga(K[d+28>>2]);Ga(d);return 0}c:{if(K[K[a+224>>2]+40>>2]){if(!K[d+36>>2]){break c}while(1){e=Q(h,40);c=K[(e+K[K[a+224>>2]+40>>2]|0)+20>>2];K[(b+e|0)+20>>2]=c;g=Ja(Q(c,24));c=K[d+40>>2];f=c+e|0;K[f+24>>2]=g;if(!g){if(h){b=0;while(1){Ga(K[(K[d+40>>2]+Q(b,40)|0)+24>>2]);b=b+1|0;if((h|0)!=(b|0)){continue}break}c=K[d+40>>2]}break a}b=K[(e+K[K[a+224>>2]+40>>2]|0)+24>>2];d:{if(b){c=Q(K[f+20>>2],24);if(c){E(g,b,c)}b=K[d+40>>2];break d}Ga(g);b=K[d+40>>2];K[(e+b|0)+24>>2]=0}c=K[(e+K[K[a+224>>2]+40>>2]|0)+4>>2];K[(b+e|0)+4>>2]=c;g=Ja(Q(c,24));c=K[d+40>>2];f=c+e|0;K[f+16>>2]=g;if(!g){if(h){b=0;while(1){a=Q(b,40);Ga(K[(a+K[d+40>>2]|0)+24>>2]);Ga(K[(a+K[d+40>>2]|0)+16>>2]);b=b+1|0;if((h|0)!=(b|0)){continue}break}c=K[d+40>>2]}break a}b=K[(e+K[K[a+224>>2]+40>>2]|0)+16>>2];e:{if(b){c=Q(K[f+4>>2],24);if(c){E(g,b,c)}b=K[d+40>>2];break e}Ga(g);b=K[d+40>>2];K[(e+b|0)+16>>2]=0}c=b+e|0;K[c+32>>2]=0;K[c+36>>2]=0;h=h+1|0;if(h>>>0<N[d+36>>2]){continue}break}break c}Ga(b);K[d+40>>2]=0}}else{d=0}return d|0}Ga(c);Ga(K[d+28>>2]);Ga(d);return 0}function mb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{while(1){if(a-1&a|b>>>0>4294967239){break b}j=a>>>0>8;a=j?a:8;d=K[6847];e=d;g=K[6846];b=b>>>0<=8?8:b+3&-4;c:{if(b>>>0<=127){i=(b>>>3|0)-1|0;break c}c=T(b);i=((b>>>29-c^4)-(c<<2)|0)+110|0;if(b>>>0<=4095){break c}c=((b>>>30-c^2)-(c<<1)|0)+71|0;i=c>>>0>=63?63:c}h=i;f=h&31;if((h&63)>>>0>=32){c=0;d=d>>>f|0}else{c=d>>>f|0;d=((1<<f)-1&d)<<32-f|g>>>f}if(d|c){while(1){f=c;d:{if(c|d){e=c-1|0;g=e+1|0;i=e;e=d-1|0;g=(e|0)!=-1?g:i;c=T(c^g);c=(c|0)==32?T(d^e)+32|0:c;e=63-c|0;ua=0-(c>>>0>63)|0;break d}ua=0;e=64}g=e;e=g&31;if((g&63)>>>0>=32){c=0;i=f>>>e|0}else{c=f>>>e|0;i=((1<<e)-1&f)<<32-e|d>>>e}h=g+h|0;d=h<<4;f=K[d+26360>>2];e=d+26352|0;e:{if((f|0)!=(e|0)){d=Lb(f,a,b);if(d){break a}d=K[f+4>>2];g=K[f+8>>2];K[d+8>>2]=g;K[g+4>>2]=d;K[f+8>>2]=e;K[f+4>>2]=K[e+4>>2];K[e+4>>2]=f;K[K[f+4>>2]+8>>2]=f;h=h+1|0;d=(c&1)<<31|i>>>1;c=c>>>1|0;break e}d=K[6847];k=27384,l=K[6846]&Qe(-2,-1,h),K[k>>2]=l;K[6847]=ua&d;d=i^1}if(c|d){continue}break}g=K[6846];e=K[6847]}c=T(e);f=63-((c|0)==32?T(g)+32|0:c)|0;f:{if(!(e|g)){c=0;break f}d=f<<4;c=K[d+26360>>2];if(!e&g>>>0<1073741824){break f}h=99;e=d+26352|0;if((e|0)==(c|0)){break f}while(1){if(!h){break f}d=Lb(c,a,b);if(d){break a}h=h-1|0;c=K[c+8>>2];if((e|0)!=(c|0)){continue}break}}if(Pc((j?a+48|0:48)+b|0)){continue}break}if(!c){break b}f=(f<<4)+26352|0;if((f|0)==(c|0)){break b}while(1){d=Lb(c,a,b);if(d){break a}c=K[c+8>>2];if((f|0)!=(c|0)){continue}break}}d=0}return d}function Jd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=K[a+48>>2];if(e>>>0>=b>>>0){K[a+48>>2]=e-b;K[a+36>>2]=K[a+36>>2]+b;e=c+K[a+60>>2]|0;d=b+K[a+56>>2]|0;e=d>>>0<b>>>0?e+1|0:e;K[a+56>>2]=d;K[a+60>>2]=e;ua=c;return b|0}if(L[a+68|0]&4){K[a+48>>2]=0;K[a+36>>2]=e+K[a+36>>2];g=K[a+60>>2];c=K[a+56>>2];b=c+e|0;K[a+56>>2]=b;K[a+60>>2]=b>>>0<c>>>0?g+1|0:g;ua=e?0:-1;return(e?e:-1)|0}if(e){K[a+48>>2]=0;K[a+36>>2]=K[a+32>>2];h=b;f=e;b=b-e|0;c=c-(e>>>0>h>>>0)|0}a:{if((c|0)>0){h=1}else{h=!!b&(c|0)>=0}if(h){while(1){h=K[a+12>>2];e=c+g|0;i=b+f|0;e=K[a+60>>2]+(i>>>0<f>>>0?e+1|0:e)|0;j=i;i=i+K[a+56>>2]|0;e=j>>>0>i>>>0?e+1|0:e;if((e|0)==(h|0)&i>>>0>N[a+8>>2]|e>>>0>h>>>0){Fa(d,4,15593,0);K[a+48>>2]=0;K[a+36>>2]=K[a+32>>2];b=g+K[a+60>>2]|0;c=f+K[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;K[a+56>>2]=c;K[a+60>>2]=b;d=K[a+8>>2];f=d-c|0;e=K[a+12>>2];g=e-((c>>>0>d>>>0)+b|0)|0;h=va[K[a+28>>2]](d,e,K[a>>2])|0;i=K[a+68>>2];if(h){K[a+56>>2]=d;K[a+60>>2]=e}K[a+68>>2]=i|4;a=(c|0)==(d|0)&(b|0)==(e|0);b=a?-1:f;break a}e=va[K[a+24>>2]](b,c,K[a>>2])|0;h=ua;i=h;if((e&i)==-1){Fa(d,4,15593,0);K[a+68>>2]=K[a+68>>2]|4;e=g+K[a+60>>2]|0;b=f+K[a+56>>2]|0;e=b>>>0<f>>>0?e+1|0:e;K[a+56>>2]=b;K[a+60>>2]=e;a=!(g|f);b=a?-1:f;break a}g=g+i|0;f=e+f|0;g=f>>>0<e>>>0?g+1|0:g;h=b;b=b-e|0;c=c-((e>>>0>h>>>0)+i|0)|0;if(!!b&(c|0)>=0|(c|0)>0){continue}break}}b=g+K[a+60>>2]|0;c=f+K[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;K[a+56>>2]=c;K[a+60>>2]=b;ua=g;return f|0}ua=a?-1:g;return b|0}function Nd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;e=ra-80|0;ra=e;a:{if(c>>>0<=2){Fa(d,1,14441,0);break a}if(L[a+124|0]){Fa(d,4,11156,0);g=1;break a}g=1;Ha(b,a+40|0,1);Ha(b+1|0,a+52|0,1);Ha(b+2|0,a+44|0,1);f=b+3|0;b:{c:{d:{e:{f:{h=K[a+40>>2];switch(h-1|0){case 0:break f;case 1:break e;default:break d}}if(c>>>0<=6){K[e+16>>2]=c;Fa(d,1,15118,e+16|0);g=0;break a}if(!((c|0)==7|K[a+48>>2]==14)){K[e+48>>2]=c;Fa(d,2,15118,e+48|0)}Ha(f,a+48|0,4);if(K[a+48>>2]!=14){break b}f=Ja(36);if(!f){g=0;Fa(d,1,7956,0);break a}K[f>>2]=14;K[e+64>>2]=0;K[e+56>>2]=0;K[e+72>>2]=0;K[e+60>>2]=0;K[e+68>>2]=0;K[e+76>>2]=0;g=4470064;K[e+52>>2]=4470064;K[f+4>>2]=1145390592;g:{if((c|0)!=7){if((c|0)==35){Ha(b+7|0,e+76|0,4);Ha(b+11|0,e+72|0,4);Ha(b+15|0,e+68|0,4);Ha(b+19|0,e- -64|0,4);Ha(b+23|0,e+60|0,4);Ha(b+27|0,e+56|0,4);Ha(b+31|0,e+52|0,4);K[f+4>>2]=0;g=K[e+52>>2];c=K[e+56>>2];d=K[e+64>>2];i=K[e+68>>2];j=K[e+76>>2];h=K[e+72>>2];b=K[e+60>>2];break g}K[e+32>>2]=c;Fa(d,2,15154,e+32|0)}c=0;d=0;h=0;b=0}K[f+24>>2]=b;K[f+16>>2]=i;K[f+8>>2]=j;K[f+32>>2]=g;K[f+28>>2]=c;K[f+20>>2]=d;K[f+12>>2]=h;K[a+112>>2]=0;K[a+108>>2]=f;break b}b=c-3|0;K[a+112>>2]=b;d=Ia(1,b);K[a+108>>2]=d;if(!d){break c}if((c|0)<=3){break b}c=0;while(1){Ha(f,e+76|0,1);I[K[a+108>>2]+c|0]=K[e+76>>2];f=f+1|0;c=c+1|0;if((b|0)!=(c|0)){continue}break}break b}if(h>>>0<3){break a}K[e>>2]=h;Fa(d,4,15913,e);break a}g=0;K[a+112>>2]=0;break a}g=1;I[a+124|0]=1}ra=e+80|0;return g|0}function Na(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;h=K[a+48>>2];if(h>>>0>=c>>>0){if(c){E(b,K[a+36>>2],c)}K[a+36>>2]=K[a+36>>2]+c;K[a+48>>2]=K[a+48>>2]-c;b=K[a+60>>2];d=K[a+56>>2]+c|0;b=d>>>0<c>>>0?b+1|0:b;K[a+56>>2]=d;K[a+60>>2]=b;return c}if(L[a+68|0]&4){if(h){E(b,K[a+36>>2],h)}b=K[a+48>>2];K[a+48>>2]=0;K[a+36>>2]=b+K[a+36>>2];g=K[a+60>>2];c=b;b=K[a+56>>2]+b|0;g=c>>>0>b>>>0?g+1|0:g;K[a+56>>2]=b;K[a+60>>2]=g;return h?h:-1}a:{if(h){if(h){E(b,K[a+36>>2],h)}i=K[a+32>>2];K[a+36>>2]=i;e=K[a+48>>2];K[a+48>>2]=0;f=K[a+60>>2];g=K[a+56>>2]+e|0;f=g>>>0<e>>>0?f+1|0:f;K[a+56>>2]=g;K[a+60>>2]=f;c=c-e|0;b=b+e|0;break a}i=K[a+32>>2];K[a+36>>2]=i}b:{while(1){c:{e=K[a>>2];f=K[a+16>>2];g=K[a+64>>2];d:{if(g>>>0>c>>>0){f=va[f|0](i,g,e)|0;K[a+48>>2]=f;if((f|0)==-1){break b}if(c>>>0>f>>>0){if(f){E(b,K[a+36>>2],f)}i=K[a+32>>2];K[a+36>>2]=i;e=K[a+48>>2];break d}if(c){E(b,K[a+36>>2],c)}K[a+36>>2]=K[a+36>>2]+c;K[a+48>>2]=K[a+48>>2]-c;b=K[a+60>>2];d=K[a+56>>2]+c|0;b=d>>>0<c>>>0?b+1|0:b;K[a+56>>2]=d;K[a+60>>2]=b;return c+h|0}e=va[f|0](b,c,e)|0;K[a+48>>2]=e;if((e|0)==-1){break b}if(c>>>0<=e>>>0){break c}i=K[a+32>>2];K[a+36>>2]=i;f=e}K[a+48>>2]=0;g=K[a+60>>2];j=K[a+56>>2]+e|0;g=j>>>0<e>>>0?g+1|0:g;K[a+56>>2]=j;K[a+60>>2]=g;b=b+e|0;c=c-e|0;h=f+h|0;continue}break}K[a+48>>2]=0;K[a+36>>2]=K[a+32>>2];f=K[a+60>>2];b=K[a+56>>2]+e|0;f=b>>>0<e>>>0?f+1|0:f;K[a+56>>2]=b;K[a+60>>2]=f;return e+h|0}Fa(d,4,15593,0);K[a+48>>2]=0;K[a+68>>2]=K[a+68>>2]|4;return h?h:-1}function Vb(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;g=ra-16|0;ra=g;o=K[K[a+96>>2]+16>>2];b=Ia(1,56);K[g+12>>2]=b;a:{if(!b){break a}j=K[K[a+96>>2]+16>>2];K[b+24>>2]=j;K[b>>2]=K[a+108>>2];K[b+4>>2]=K[a+112>>2];K[b+8>>2]=K[a+116>>2];K[b+12>>2]=K[a+120>>2];K[b+16>>2]=K[a+128>>2];h=K[a+132>>2];K[b+52>>2]=0;K[b+20>>2]=h;i=K[a+12>>2];K[b+32>>2]=K[i>>2];K[b+36>>2]=K[i+4>>2];K[b+40>>2]=K[i+8>>2];K[b+44>>2]=K[i+16>>2];a=Ia(j,1080);K[b+48>>2]=a;if(a){if(o){while(1){a=Q(k,1080);d=a+K[b+48>>2]|0;c=a+K[i+5584>>2]|0;K[d+4>>2]=K[c>>2];a=K[c+4>>2];K[d+8>>2]=a;K[d+12>>2]=K[c+8>>2];K[d+16>>2]=K[c+12>>2];K[d+20>>2]=K[c+16>>2];K[d+24>>2]=K[c+20>>2];b:{if(a>>>0>32){break b}if(a){E(d+948|0,c+944|0,a)}a=K[c+4>>2];if(!a){break b}E(d+816|0,c+812|0,a)}a=K[c+24>>2];K[d+28>>2]=a;K[d+808>>2]=K[c+804>>2];f=1;c:{if((a|0)!=1){a=Q(K[c+4>>2],3);if(a-3>>>0>95){break c}f=a-2|0}p=f&1;l=d+420|0;m=d+32|0;n=c+28|0;a=0;if((f|0)!=1){j=f&-2;f=0;while(1){h=a<<2;e=(a<<3)+n|0;K[h+m>>2]=K[e+4>>2];K[h+l>>2]=K[e>>2];e=a|1;h=e<<2;e=(e<<3)+n|0;K[h+m>>2]=K[e+4>>2];K[h+l>>2]=K[e>>2];a=a+2|0;f=f+2|0;if((j|0)!=(f|0)){continue}break}}if(!p){break c}e=a<<2;a=(a<<3)+n|0;K[e+m>>2]=K[a+4>>2];K[e+l>>2]=K[a>>2]}K[d+812>>2]=K[c+808>>2];k=k+1|0;if((k|0)!=(o|0)){continue}break}}e=b;break a}if(g+12|0){a=K[g+12>>2];b=K[a+48>>2];if(b){Ga(b);a=K[g+12>>2]}Ga(a);K[g+12>>2]=0}}ra=g+16|0;return e|0}function oc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;f=K[a+28>>2]+Q(b,152)|0;d=K[f-144>>2]-K[f-152>>2]|0;e=K[f-140>>2]-K[f-148>>2]|0;c=e>>>0>=64?64:e;g=d>>>0>=64?64:d;a:{if(!(!d|!e|(!g|!c)|g>>>0>4294967295/(c>>>0)>>>2>>>0)){f=Ia(1,28);K[f+12>>2]=c;K[f+8>>2]=g;K[f+4>>2]=e;K[f>>2]=d;h=e;e=c+e|0;i=h>>>0>e>>>0?1:i;e=Ne(e-1|0,i-!e|0,c,0);K[f+20>>2]=e;c=0;h=d;d=d+g|0;c=h>>>0>d>>>0?1:c;c=Ne(d-1|0,c-!d|0,g,0);K[f+16>>2]=c;Le(e,0,c);b:{if(ua){break b}c=Ia(4,Q(c,e));K[f+24>>2]=c;if(!c){break b}break a}Ga(f)}f=0}if(!f){return 0}c:{if(b){while(1){o=Q(n,152);e=o+K[a+28>>2]|0;c=K[e+24>>2];if(c){r=e+28|0;d=K[e+20>>2];g=K[e+16>>2];l=0;while(1){if(Q(d,g)){i=Q(l,36)+r|0;m=0;while(1){k=K[i+20>>2]+Q(m,40)|0;c=K[k+20>>2];j=K[k+16>>2];if(Q(c,j)){g=0;while(1){d=K[k+24>>2]+Q(g,68)|0;p=K[d+60>>2];if(p){j=K[d+12>>2];s=K[d+20>>2];t=K[d+16>>2];q=K[d+8>>2];d=q-K[i>>2]|0;h=K[i+16>>2];if(h&1){c=K[a+28>>2]+o|0;d=(K[c-144>>2]+d|0)-K[c-152>>2]|0}c=j-K[i+4>>2]|0;if(h&2){h=c;c=K[a+28>>2]+o|0;c=(h+K[c-140>>2]|0)-K[c-148>>2]|0}h=d;d=t-q|0;if(!db(f,h,c,h+d|0,(s-j|0)+c|0,p,1,d)){break c}j=K[k+16>>2];c=K[k+20>>2]}g=g+1|0;if(g>>>0<Q(c,j)>>>0){continue}break}g=K[e+16>>2];d=K[e+20>>2]}m=m+1|0;if(m>>>0<Q(d,g)>>>0){continue}break}c=K[e+24>>2]}l=l+1|0;if(l>>>0<c>>>0){continue}break}}n=n+1|0;if((n|0)!=(b|0)){continue}break}}return f}_a(f);return 0}function Sb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;a:{b:{e=K[a+60>>2];if(!e){if(K[b+16>>2]){break b}return 1}i=Ja(Q(e,52));if(!i){break a}e=0;if(K[b+16>>2]){d=K[b+24>>2];while(1){e=Q(f,52);Ga(K[(e+d|0)+44>>2]);d=K[b+24>>2];K[(e+d|0)+44>>2]=0;f=f+1|0;e=K[b+16>>2];if(f>>>0<e>>>0){continue}break}}if(K[a+60>>2]){f=K[K[a+100>>2]+24>>2];e=0;while(1){h=Q(K[K[a+64>>2]+(e<<2)>>2],52);d=h+f|0;c=K[d+4>>2];g=i+Q(e,52)|0;K[g>>2]=K[d>>2];K[g+4>>2]=c;K[g+48>>2]=K[d+48>>2];c=K[d+44>>2];K[g+40>>2]=K[d+40>>2];K[g+44>>2]=c;c=K[d+36>>2];K[g+32>>2]=K[d+32>>2];K[g+36>>2]=c;c=K[d+28>>2];K[g+24>>2]=K[d+24>>2];K[g+28>>2]=c;c=K[d+20>>2];K[g+16>>2]=K[d+16>>2];K[g+20>>2]=c;c=K[d+12>>2];K[g+8>>2]=K[d+8>>2];K[g+12>>2]=c;f=K[K[a+100>>2]+24>>2];c=h+f|0;K[g+36>>2]=K[c+36>>2];K[g+44>>2]=K[c+44>>2];K[c+44>>2]=0;e=e+1|0;c=K[a+60>>2];if(e>>>0<c>>>0){continue}break}e=K[b+16>>2]}if(e){d=K[K[a+100>>2]+24>>2];f=0;while(1){c=Q(f,52);Ga(K[(c+d|0)+44>>2]);d=K[K[a+100>>2]+24>>2];K[(c+d|0)+44>>2]=0;f=f+1|0;if(f>>>0<N[b+16>>2]){continue}break}c=K[a+60>>2]}K[b+16>>2]=c;Ga(K[b+24>>2]);K[b+24>>2]=i;return 1}e=K[b+24>>2];f=K[K[a+100>>2]+24>>2];while(1){h=Q(d,52);c=h+e|0;K[c+36>>2]=K[(f+h|0)+36>>2];Ga(K[c+44>>2]);e=K[b+24>>2];f=K[K[a+100>>2]+24>>2];c=h+f|0;K[(h+e|0)+44>>2]=K[c+44>>2];K[c+44>>2]=0;d=d+1|0;if(d>>>0<N[b+16>>2]){continue}break}return 1}Ya(K[a+96>>2]);K[a+96>>2]=0;return 0}function se(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=ra-16|0;ra=h;if(K[a+8>>2]==16){f=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{f=K[a+12>>2]}a:{if(c>>>0<=1){Fa(d,1,4095,0);a=0;break a}Ha(b,h+12|0,2);b:{if(K[h+12>>2]){Fa(d,2,3571,0);break b}if(c>>>0<=6){Fa(d,1,4095,0);a=0;break a}Ha(b+2|0,h+12|0,2);e=K[f+5616>>2];k=L[h+12|0];c:{d:{e:{g=K[f+5620>>2];if(!g){a=e;break e}a=e;while(1){if(K[a+8>>2]==(k|0)){break e}a=a+20|0;i=i+1|0;if((i|0)!=(g|0)){continue}break}break d}if((g|0)!=(i|0)){break c}}if(K[f+5624>>2]==(g|0)){a=g+10|0;K[f+5624>>2]=a;a=La(e,Q(a,20));e=K[f+5616>>2];if(!a){Ga(e);K[f+5624>>2]=0;K[f+5616>>2]=0;K[f+5620>>2]=0;Fa(d,1,4121,0);a=0;break a}f:{if((a|0)==(e|0)){break f}l=K[f+5632>>2];if(!l){break f}m=K[f+5628>>2];i=0;while(1){g=Q(i,20)+m|0;j=K[g+8>>2];if(j){K[g+8>>2]=a+(j-e|0)}j=K[g+12>>2];if(j){K[g+12>>2]=a+(j-e|0)}i=i+1|0;if((l|0)!=(i|0)){continue}break}}K[f+5616>>2]=a;e=K[f+5620>>2];g=Q(K[f+5624>>2]-e|0,20);if(g){B(a+Q(e,20)|0,0,g)}g=K[f+5620>>2];e=K[f+5616>>2]}K[f+5620>>2]=g+1;a=Q(g,20)+e|0}e=K[a+12>>2];if(e){Ga(e);K[a+12>>2]=0;K[a+16>>2]=0}K[a+8>>2]=k;e=K[h+12>>2];K[a>>2]=e>>>10&3;K[a+4>>2]=e>>>8&3;Ha(b+4|0,h+12|0,2);if(K[h+12>>2]){Fa(d,2,2986,0);break b}c=c-6|0;e=Ja(c);K[a+12>>2]=e;if(!e){Fa(d,1,4095,0);a=0;break a}if(c){E(e,b+6|0,c)}K[a+16>>2]=c}a=1}ra=h+16|0;return a|0}function Za(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{if(!K[a+12>>2]){k=1;if(K[a+4>>2]>0|K[a+8>>2]>1){break b}break a}e=1;if(K[a+8>>2]>0){break b}if(K[a+4>>2]<2){break a}}b=K[a>>2];f=b+(e<<5)|0;g=K[a+16>>2];h=K[a+20>>2];if(g>>>0<h>>>0){d=g;while(1){c=(d<<6)+f|0;O[c>>2]=O[c>>2]*R(1.2301740646362305);O[c+4>>2]=O[c+4>>2]*R(1.2301740646362305);O[c+8>>2]=O[c+8>>2]*R(1.2301740646362305);O[c+12>>2]=O[c+12>>2]*R(1.2301740646362305);O[c+16>>2]=O[c+16>>2]*R(1.2301740646362305);O[c+20>>2]=O[c+20>>2]*R(1.2301740646362305);O[c+24>>2]=O[c+24>>2]*R(1.2301740646362305);O[c+28>>2]=O[c+28>>2]*R(1.2301740646362305);d=d+1|0;if((h|0)!=(d|0)){continue}break}}i=b+(k<<5)|0;j=K[a+28>>2];c=K[a+24>>2];if(j>>>0>c>>>0){d=c;while(1){b=(d<<6)+i|0;O[b>>2]=O[b>>2]*R(1.625732421875);O[b+4>>2]=O[b+4>>2]*R(1.625732421875);O[b+8>>2]=O[b+8>>2]*R(1.625732421875);O[b+12>>2]=O[b+12>>2]*R(1.625732421875);O[b+16>>2]=O[b+16>>2]*R(1.625732421875);O[b+20>>2]=O[b+20>>2]*R(1.625732421875);O[b+24>>2]=O[b+24>>2]*R(1.625732421875);O[b+28>>2]=O[b+28>>2]*R(1.625732421875);d=d+1|0;if((j|0)!=(d|0)){continue}break}}b=f+32|0;d=K[a+8>>2];a=K[a+4>>2];e=a-e|0;e=(d|0)<(e|0)?d:e;qb(i,b,g,h,e,R(-.4435068666934967));l=i+32|0;d=d-k|0;a=(a|0)<(d|0)?a:d;qb(f,l,c,j,a,R(-.8829110860824585));qb(i,b,g,h,e,R(.05298011749982834));qb(f,l,c,j,a,R(1.5861343145370483))}}function hc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if(a){b=K[a+20>>2];if(b){g=K[b>>2];if(g){d=K[g+20>>2];if(K[g+16>>2]){i=I[a+40|0]&1?16:17;while(1){c=K[d+28>>2];if(c){b=K[d+32>>2];l=(b>>>0)/152|0;j=0;if(b>>>0>=152){while(1){b=K[c+48>>2];if(b){f=K[c+52>>2];h=(f>>>0)/40|0;e=0;if(f>>>0>=40){while(1){eb(K[b+32>>2]);K[b+32>>2]=0;eb(K[b+36>>2]);K[b+36>>2]=0;va[i|0](b);b=b+40|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}b=K[c+48>>2]}Ga(b);K[c+48>>2]=0}b=K[c+84>>2];if(b){f=K[c+88>>2];h=(f>>>0)/40|0;e=0;if(f>>>0>=40){while(1){eb(K[b+32>>2]);K[b+32>>2]=0;eb(K[b+36>>2]);K[b+36>>2]=0;va[i|0](b);b=b+40|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}b=K[c+84>>2]}Ga(b);K[c+84>>2]=0}b=K[c+120>>2];if(b){f=K[c+124>>2];h=(f>>>0)/40|0;e=0;if(f>>>0>=40){while(1){eb(K[b+32>>2]);K[b+32>>2]=0;eb(K[b+36>>2]);K[b+36>>2]=0;va[i|0](b);b=b+40|0;e=e+1|0;if((h|0)!=(e|0)){continue}break}b=K[c+120>>2]}Ga(b);K[c+120>>2]=0}c=c+152|0;j=j+1|0;if((l|0)!=(j|0)){continue}break}c=K[d+28>>2]}Ga(c);K[d+28>>2]=0}a:{if(!K[d+40>>2]){break a}b=K[d+36>>2];if(!b){break a}Ga(b);K[d+44>>2]=0;K[d+48>>2]=0;K[d+36>>2]=0;K[d+40>>2]=0}Ga(K[d+52>>2]);d=d+76|0;k=k+1|0;if(k>>>0<N[g+16>>2]){continue}break}d=K[g+20>>2]}Ga(d);K[g+20>>2]=0;Ga(K[K[a+20>>2]>>2]);b=K[a+20>>2];K[b>>2]=0}Ga(b);K[a+20>>2]=0}Ga(K[a+68>>2]);Ga(a)}}function pc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;c=K[a+8>>2];f=c+K[a+4>>2]|0;a:{if(!K[a+12>>2]){if((f|0)<2){break a}h=(c<<2)+b|0;d=K[h>>2];e=K[b>>2]-(d+1>>1)|0;i=K[a>>2];b:{if(f>>>0<4){c=d;break b}k=(f-4>>>1|0)+1|0;a=1;while(1){c=a<<2;m=K[c+b>>2];c=K[c+h>>2];l=i+(g<<2)|0;K[l>>2]=e;j=e;e=m-((c+d|0)+2>>2)|0;K[l+4>>2]=(j+e>>1)+d;g=g+2|0;j=(a|0)!=(k|0);d=c;a=a+1|0;if(j){continue}break}}K[i+(g<<2)>>2]=e;if(f&1){d=f-1|0;a=K[(d<<1)+b>>2]-(c+1>>1)|0;K[i+(d<<2)>>2]=a;e=a+e>>1;d=-8}else{d=-4}a=f<<2;K[d+(a+i|0)>>2]=c+e;if(!a){break a}E(b,i,a);return}c:{switch(f-1|0){case 0:K[b>>2]=K[b>>2]/2;return;case 1:a=K[a>>2];c=(c<<2)+b|0;d=K[b>>2]-(K[c>>2]+1>>1)|0;K[a+4>>2]=d;K[a>>2]=d+K[c>>2];c=K[a+4>>2];K[b>>2]=K[a>>2];K[b+4>>2]=c;return;default:break c}}if((f|0)<3){break a}h=K[a>>2];k=(c<<2)+b|0;d=K[k+4>>2];a=K[k>>2];e=K[b>>2]-((d+a|0)+2>>2)|0;K[h>>2]=e+a;g=1;m=f-2|0;l=f&1;a=!l;d:{if(m-a>>>0<2){c=d;break d}o=((f-a|0)-4>>>1|0)+1|0;a=1;while(1){p=K[(a<<2)+b>>2];j=a+1|0;c=K[k+(j<<2)>>2];n=h+(g<<2)|0;K[n>>2]=e;i=e;e=p-((c+d|0)+2>>2)|0;K[n+4>>2]=(i+e>>1)+d;g=g+2|0;i=(a|0)!=(o|0);d=c;a=j;if(i){continue}break}}K[h+(g<<2)>>2]=e;e:{if(!l){g=K[((f<<1)+b|0)-4>>2]-(c+1>>1)|0;K[h+(m<<2)>>2]=(g+e>>1)+c;break e}g=c+e|0}a=f<<2;K[(a+h|0)-4>>2]=g;if(!a){break a}E(b,h,a)}}function fc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=K[a+24>>2];j=K[e+16>>2];if(!j){return 0}f=K[e+24>>2];e=K[K[K[a+20>>2]>>2]+20>>2];a:{b:{if(!b){b=0;while(1){c=K[f+24>>2];a=K[e+28>>2]+Q(K[e+24>>2],152)|0;d=K[a-140>>2];g=K[a-144>>2]-K[a-152>>2]|0;a=K[a-148>>2];h=d-a|0;Le(g,0,h);if(!(!ua|(a|0)==(d|0))){break a}a=(c>>>3|0)+((c&7)!=0)|0;c=(a|0)==3?4:a;a=!c;d=Q(g,h);Le(c,0,d);if(!(!ua|a)){break a}a=-1;c=Q(c,d);if(c>>>0>(b^-1)>>>0){break b}e=e+76|0;f=f+52|0;b=b+c|0;a=b;i=i+1|0;if((j|0)!=(i|0)){continue}break}break b}b=0;if(!K[a+64>>2]){while(1){c=K[f+24>>2];a=K[e+28>>2]+Q(K[e+24>>2],152)|0;d=K[a-4>>2];g=K[a-8>>2]-K[a-16>>2]|0;a=K[a-12>>2];h=d-a|0;Le(g,0,h);if(!(!ua|(a|0)==(d|0))){break a}a=(c>>>3|0)+((c&7)!=0)|0;c=(a|0)==3?4:a;a=!c;d=Q(g,h);Le(c,0,d);if(!(!ua|a)){break a}a=-1;c=Q(c,d);if(c>>>0>(b^-1)>>>0){break b}e=e+76|0;f=f+52|0;b=b+c|0;a=b;i=i+1|0;if((j|0)!=(i|0)){continue}break}break b}while(1){c=K[f+24>>2];a=K[e+28>>2]+Q(K[e+24>>2],152)|0;d=K[a-140>>2];g=K[a-144>>2]-K[a-152>>2]|0;a=K[a-148>>2];h=d-a|0;Le(g,0,h);if(!(!ua|(a|0)==(d|0))){break a}a=(c>>>3|0)+((c&7)!=0)|0;c=(a|0)==3?4:a;a=!c;d=Q(g,h);Le(c,0,d);if(!(!ua|a)){break a}a=-1;c=Q(c,d);if(c>>>0>(b^-1)>>>0){break b}e=e+76|0;f=f+52|0;b=b+c|0;a=b;i=i+1|0;if((j|0)!=(i|0)){continue}break}}return a}return-1}function Wb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;d=ra-256|0;ra=d;if(a){Sa(1769,17,c);K[d+240>>2]=K[a>>2];Ka(c,2311,d+240|0);K[d+224>>2]=K[a+4>>2];Ka(c,2324,d+224|0);K[d+208>>2]=K[a+8>>2];Ka(c,7223,d+208|0);K[d+192>>2]=K[a+16>>2];Ka(c,2282,d+192|0);if((b|0)>0){while(1){e=K[a+5584>>2];K[d+176>>2]=h;Ka(c,1807,d+176|0);e=e+Q(h,1080)|0;K[d+160>>2]=K[e>>2];Ka(c,2310,d+160|0);K[d+144>>2]=K[e+4>>2];Ka(c,7337,d+144|0);K[d+128>>2]=K[e+8>>2];Ka(c,7125,d+128|0);K[d+112>>2]=K[e+12>>2];Ka(c,7141,d+112|0);K[d+96>>2]=K[e+16>>2];Ka(c,2293,d+96|0);K[d+80>>2]=K[e+20>>2];Ka(c,7403,d+80|0);Sa(1530,23,c);if(K[e+4>>2]){i=e+944|0;j=e+812|0;f=0;while(1){g=f<<2;k=K[j+g>>2];K[d+68>>2]=K[i+g>>2];K[d+64>>2]=k;Ka(c,1656,d- -64|0);f=f+1|0;if(f>>>0<N[e+4>>2]){continue}break}}Qc(c);K[d+48>>2]=K[e+24>>2];Ka(c,7157,d+48|0);K[d+32>>2]=K[e+804>>2];Ka(c,7206,d+32|0);i=1;Sa(1554,20,c);a:{if(K[e+24>>2]!=1){f=K[e+4>>2];if((f|0)<=0){break a}i=Q(f,3)-2|0}j=e+28|0;f=0;while(1){g=j+(f<<3)|0;l=d,m=Qe(K[g>>2],K[g+4>>2],32),K[l+16>>2]=m;K[d+20>>2]=ua;Ka(c,1656,d+16|0);f=f+1|0;if((i|0)!=(f|0)){continue}break}}Qc(c);K[d>>2]=K[e+808>>2];Ka(c,7189,d);Sa(1670,5,c);h=h+1|0;if((h|0)!=(b|0)){continue}break}}Sa(1671,4,c)}ra=d+256|0}function Je(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{e=b;if(e){if(!c){break j}if(!d){break i}e=T(d)-T(e)|0;if(e>>>0<=31){break h}break b}if((d|0)==1|d>>>0>1){break b}b=(a>>>0)/(c>>>0)|0;sa=a-Q(b,c)|0;ta=0;ua=0;return b}if(!a){break g}if(!d){break f}f=d-1|0;if(f&d){break f}sa=a;ta=e&f;a=e>>>Ke(d)|0;ua=0;return a}f=c-1|0;if(!(f&c)){break e}k=(T(c)+33|0)-T(e)|0;g=0-k|0;break c}k=e+1|0;g=63-e|0;break c}sa=0;a=(e>>>0)/(d>>>0)|0;ta=e-Q(a,d)|0;ua=0;return a}e=T(d)-T(e)|0;if(e>>>0<31){break d}break b}sa=a&f;ta=0;if((c|0)==1){break a}c=Ke(c);d=c&31;if((c&63)>>>0>=32){e=0;a=b>>>d|0}else{e=b>>>d|0;a=((1<<d)-1&b)<<32-d|a>>>d}ua=e;return a}k=e+1|0;g=63-e|0}f=a;e=k&63;h=e&31;if((e&63)>>>0>=32){e=0;f=b>>>h|0}else{e=b>>>h|0;f=((1<<h)-1&b)<<32-h|f>>>h}h=g&63;g=a;i=h&31;if((h&63)>>>0>=32){j=a<<i;a=0}else{j=(1<<i)-1&g>>>32-i|b<<i;a=g<<i}b=j;if(k){g=d-1|0;l=c-1|0;g=(l|0)!=-1?g+1|0:g;h=l;while(1){e=e<<1|f>>>31;f=f<<1|b>>>31;l=e;i=g-(e+(f>>>0>h>>>0)|0)|0;m=i>>31;j=m;e=f;i=c&j;f=e-i|0;e=l-((d&j)+(e>>>0<i>>>0)|0)|0;j=b<<1|a>>>31;a=n|a<<1;b=j|o;l=m&1;n=l;k=k-1|0;if(k){continue}break}}sa=f;ta=e;j=b<<1|a>>>31;a=l|a<<1;ua=j|o;return a}sa=a;ta=b;a=0;b=0}ua=b;return a}function Zc(a,b,c,d,e){var f=0,g=0,h=0,i=0;h=ra-16|0;ra=h;if(K[a+8>>2]==16){a=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{a=K[a+12>>2]}f=K[d>>2];a:{if(!f){d=0;Fa(e,1,2605,0);break a}a=K[a+5584>>2];K[d>>2]=f-1;Ha(c,h+12|0,1);g=Q(b,1080)+a|0;a=K[h+12>>2];K[g+804>>2]=a>>>5;b=a&31;K[g+24>>2]=b;a=c+1|0;b:{c:{d:{e:{f:{switch(b|0){case 0:f=K[d>>2];break e;case 1:break d;default:break f}}f=K[d>>2]>>>1|0}if(f>>>0>=98){K[h+4>>2]=97;K[h+8>>2]=97;K[h>>2]=f;Fa(e,2,16019,h);b=K[g+24>>2]}if(b){b=f;if(b){break d}a=0;break c}if(f){b=g+28|0;c=0;while(1){Ha(a,h+12|0,1);if(c>>>0<=96){e=K[h+12>>2];i=b+(c<<3)|0;K[i+4>>2]=0;K[i>>2]=e>>>3}a=a+1|0;c=c+1|0;if((f|0)!=(c|0)){continue}break}}a=K[d>>2];if(a>>>0<f>>>0){d=0;break a}a=a-f|0;break b}e=g+28|0;c=0;while(1){Ha(a,h+12|0,2);if(c>>>0<=96){f=e+(c<<3)|0;i=K[h+12>>2];K[f+4>>2]=i&2047;K[f>>2]=i>>>11}a=a+2|0;c=c+1|0;if((c|0)!=(b|0)){continue}break}a=b<<1}b=K[d>>2];if(a>>>0>b>>>0){d=0;break a}a=b-a|0}K[d>>2]=a;d=1;if(K[g+24>>2]!=1){break a}f=g+28|0;c=K[g+32>>2];e=K[g+28>>2];a=1;while(1){b=f+(a<<3)|0;K[b+4>>2]=c;K[b+12>>2]=c;g=e-((a>>>0)/3|0)|0;K[b+8>>2]=(g|0)>0?g:0;g=b;b=e-((a-1>>>0)/3|0)|0;K[g>>2]=(b|0)>0?b:0;a=a+2|0;if((a|0)!=97){continue}break}}ra=h+16|0;return d}function ye(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;f=ra-32|0;ra=f;g=1;a:{if(c>>>0<=1){g=0;Fa(d,1,10025,0);break a}if(K[a+76>>2]){break a}Ha(b,f+28|0,1);Ha(b+1|0,f+24|0,1);e=K[f+24>>2];i=e>>>4&3;if((i|0)==3){K[a+76>>2]=1;Fa(d,2,11521,0);break a}c=c-2|0;j=(e>>>5&2)+2|0;h=i+j|0;e=(c>>>0)/(h>>>0)|0;if((c|0)!=(Q(e,h)|0)){K[a+76>>2]=1;Fa(d,2,11102,0);break a}if(c>>>0<h>>>0){break a}b:{c=K[a+68>>2];if(c>>>0<=(e^-1)>>>0){c=c+e|0;if(c>>>0<536870912){break b}}K[a+76>>2]=1;Fa(d,2,9363,0);break a}h=La(K[a+72>>2],c<<3);if(!h){K[a+76>>2]=1;Fa(d,2,9406,0);break a}c=b+2|0;K[a+72>>2]=h;c:{if(i){k=e>>>0<=1?1:e;e=0;while(1){Ha(c,f+20|0,i);b=K[f+20>>2];if(b>>>0>=Q(K[a+132>>2],K[a+128>>2])>>>0){break c}b=c+i|0;Ha(b,f+16|0,j);c=K[a+68>>2];g=h+(c<<3)|0;J[g>>1]=K[f+20>>2];K[g+4>>2]=K[f+16>>2];g=1;K[a+68>>2]=c+1;c=b+j|0;e=e+1|0;if((k|0)!=(e|0)){continue}break}break a}i=e>>>0<=1?1:e;b=K[a+68>>2];e=0;while(1){K[f+20>>2]=b;if(Q(K[a+132>>2],K[a+128>>2])>>>0<=b>>>0){break c}Ha(c,f+16|0,j);k=K[a+68>>2];g=h+(k<<3)|0;J[g>>1]=b;K[g+4>>2]=K[f+16>>2];g=1;b=k+1|0;K[a+68>>2]=b;c=c+j|0;e=e+1|0;if((i|0)!=(e|0)){continue}break}break a}K[a+76>>2]=1;K[f>>2]=b;Fa(d,2,7762,f)}ra=f+32|0;return g|0}function Pd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;h=ra-16|0;ra=h;a:{if(!(L[a+100|0]&2)){Fa(d,1,11319,0);a=0;break a}K[a+104>>2]=0;b:{c:{d:{if(c){while(1){if(c>>>0<=7){Fa(d,1,3366,0);break b}g=h+12|0;Ha(b,g,4);e=K[h+12>>2];Ha(b+4|0,g,4);f=8;g=K[h+12>>2];e:{f:{g:{switch(e|0){case 1:if(c>>>0<16){e=3406;break c}Ha(b+8|0,h+8|0,4);if(K[h+8>>2]){e=8412;break c}Ha(b+12|0,h+12|0,4);e=K[h+12>>2];if(e){break f}e=3231;break c;case 0:break g;default:break e}}Fa(d,1,3231,0);break b}f=16}if(e>>>0<f>>>0){Fa(d,1,9111,0);break b}if(c>>>0<e>>>0){Fa(d,1,9039,0);a=0;break a}h:{i:{j=b+f|0;k=e-f|0;j:{k:{l:{m:{if((g|0)<=1668246641){if((g|0)==1651532643){break m}if((g|0)==1667523942){break k}if((g|0)!=1668112752){break i}f=25248;break j}if((g|0)==1885564018){break l}f=25216;if((g|0)==1768449138){break j}if((g|0)!=1668246642){break i}f=25224;break j}f=25232;break j}f=25240;break j}f=25256}if(va[K[f+4>>2]](a,j,k,d)|0){break h}a=0;break a}K[a+104>>2]=K[a+104>>2]|2147483647}i=(g|0)==1768449138?1:i;b=b+e|0;c=c-e|0;if(c){continue}break}if(i){break d}}Fa(d,1,8939,0);a=0;break a}I[a+132|0]=1;K[a+100>>2]=K[a+100>>2]|4;a=1;break a}Fa(d,1,e,0)}Fa(d,1,1931,0);a=0}ra=h+16|0;return a|0}function Tb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{if(!c){break a}b:{e=K[a+184>>2];if(!e){break b}g=K[a+96>>2];if(!g|!K[g+16>>2]|(e|0)!=K[K[g+24>>2]+40>>2]){break b}h=K[c+16>>2];if(!h){break b}f=K[c+24>>2];if(K[f+40>>2]|K[f+44>>2]){break b}g=0;if(h>>>0>=8){j=h&-8;while(1){K[(f+Q(g,52)|0)+40>>2]=e;K[(f+Q(g|1,52)|0)+40>>2]=e;K[(f+Q(g|2,52)|0)+40>>2]=e;K[(f+Q(g|3,52)|0)+40>>2]=e;K[(f+Q(g|4,52)|0)+40>>2]=e;K[(f+Q(g|5,52)|0)+40>>2]=e;K[(f+Q(g|6,52)|0)+40>>2]=e;K[(f+Q(g|7,52)|0)+40>>2]=e;g=g+8|0;k=k+8|0;if((j|0)!=(k|0)){continue}break}}h=h&7;if(h){while(1){K[(f+Q(g,52)|0)+40>>2]=e;g=g+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(Db(c,d)){break b}return 0}f=K[a+100>>2];if(!f){f=Bb();K[a+100>>2]=f;if(!f){break a}}Ob(c,f);if(!$a(K[a+216>>2],22,d)){break a}h=K[a+216>>2];e=K[h>>2];f=K[h+8>>2];c:{if(e){i=1;j=e&1;if((e|0)==1){e=0}else{k=e&-2;g=0;while(1){e=0;d:{if(!i){break d}e=0;if(!(va[K[f>>2]](a,b,d)|0)){break d}e=(va[K[f+4>>2]](a,b,d)|0)!=0}i=e;f=f+8|0;g=g+2|0;if((k|0)!=(g|0)){continue}break}e=!i}i=j?0:i;if(!(e|!j)){i=(va[K[f>>2]](a,b,d)|0)!=0}Ta(h);if(i){break c}Ya(K[a+96>>2]);K[a+96>>2]=0;return 0}Ta(h)}i=Sb(a,c)}return i|0}function ae(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;if(!$a(K[b+8>>2],54,d)){return 0}j=K[b+4>>2];e=K[j>>2];h=K[j+8>>2];a:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;while(1){i=0;b:{if(!f){break b}i=0;if(!(va[K[h>>2]](b,a,d)|0)){break b}i=(va[K[h+4>>2]](b,a,d)|0)!=0}f=i;h=h+8|0;g=g+2|0;if((e|0)!=(g|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(va[K[h>>2]](b,a,d)|0)!=0}Ta(j);if(f){break a}return 0}Ta(j)}j=K[b+8>>2];e=K[j>>2];h=K[j+8>>2];c:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;g=0;while(1){i=0;d:{if(!f){break d}i=0;if(!(va[K[h>>2]](b,a,d)|0)){break d}i=(va[K[h+4>>2]](b,a,d)|0)!=0}f=i;h=h+8|0;g=g+2|0;if((e|0)!=(g|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(va[K[h>>2]](b,a,d)|0)!=0}Ta(j);if(f){break c}return 0}Ta(j)}if(!L[b+132|0]){Fa(d,1,11659,0);return 0}if(!L[b+133|0]){Fa(d,1,11630,0);return 0}d=ac(a,K[b>>2],c,d);e:{if(!c){break e}a=K[c>>2];if(!a){break e}g=1;f:{g:{switch(K[b+48>>2]-12|0){case 5:g=2;break f;case 6:g=3;break f;case 12:g=4;break f;case 0:g=5;break f;case 4:break f;default:break g}}g=-1}K[a+20>>2]=g;c=K[b+108>>2];if(!c){break e}K[a+28>>2]=c;K[a+32>>2]=K[b+112>>2];K[b+108>>2]=0}return d|0}function Ob(a,b){var c=0,d=0,e=0,f=0,g=0;K[b>>2]=K[a>>2];K[b+4>>2]=K[a+4>>2];K[b+8>>2]=K[a+8>>2];K[b+12>>2]=K[a+12>>2];c=K[b+24>>2];if(c){d=K[b+16>>2];if(d){c=0;while(1){f=K[(K[b+24>>2]+Q(c,52)|0)+44>>2];if(f){Ga(f);d=K[b+16>>2]}c=c+1|0;if(d>>>0>c>>>0){continue}break}c=K[b+24>>2]}Ga(c);K[b+24>>2]=0}c=K[a+16>>2];K[b+16>>2]=c;c=Ja(Q(c,52));K[b+24>>2]=c;if(c){if(K[b+16>>2]){f=0;while(1){g=Q(f,52);c=g+c|0;d=K[a+24>>2]+g|0;e=K[d+4>>2];K[c>>2]=K[d>>2];K[c+4>>2]=e;K[c+48>>2]=K[d+48>>2];e=K[d+44>>2];K[c+40>>2]=K[d+40>>2];K[c+44>>2]=e;e=K[d+36>>2];K[c+32>>2]=K[d+32>>2];K[c+36>>2]=e;e=K[d+28>>2];K[c+24>>2]=K[d+24>>2];K[c+28>>2]=e;e=K[d+20>>2];K[c+16>>2]=K[d+16>>2];K[c+20>>2]=e;e=K[d+12>>2];K[c+8>>2]=K[d+8>>2];K[c+12>>2]=e;c=K[b+24>>2];K[(g+c|0)+44>>2]=0;f=f+1|0;if(f>>>0<N[b+16>>2]){continue}break}}K[b+20>>2]=K[a+20>>2];c=K[a+32>>2];K[b+32>>2]=c;a:{if(c){c=Ja(c);K[b+28>>2]=c;if(!c){K[b+28>>2]=0;K[b+32>>2]=0;return}b=K[a+32>>2];if(!b){break a}E(c,K[a+28>>2],b);return}K[b+28>>2]=0}return}K[b+16>>2]=0;K[b+24>>2]=0}function ac(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;f=Bb();K[b+96>>2]=f;a:{b:{if(!f){break b}c:{if($a(K[b+220>>2],18,d)){if($a(K[b+220>>2],19,d)){break c}}break a}i=K[b+220>>2];e=K[i>>2];g=K[i+8>>2];d:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;while(1){h=0;e:{if(!f){break e}h=0;if(!(va[K[g>>2]](b,a,d)|0)){break e}h=(va[K[g+4>>2]](b,a,d)|0)!=0}f=h;g=g+8|0;j=j+2|0;if((e|0)!=(j|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(va[K[g>>2]](b,a,d)|0)!=0}Ta(i);if(f){break d}break a}Ta(i)}f:{if($a(K[b+216>>2],20,d)){if($a(K[b+216>>2],21,d)){break f}}break a}i=K[b+216>>2];e=K[i>>2];g=K[i+8>>2];g:{if(e){f=1;k=e&1;if((e|0)==1){e=0}else{e=e&-2;j=0;while(1){h=0;h:{if(!f){break h}h=0;if(!(va[K[g>>2]](b,a,d)|0)){break h}h=(va[K[g+4>>2]](b,a,d)|0)!=0}f=h;g=g+8|0;j=j+2|0;if((e|0)!=(j|0)){continue}break}e=!f}f=k?0:f;if(!(e|!k)){f=(va[K[g>>2]](b,a,d)|0)!=0}Ta(i);if(f){break g}break a}Ta(i)}a=Bb();K[c>>2]=a;if(!a){break b}Ob(K[b+96>>2],a);l=1}return l|0}Ya(K[b+96>>2]);K[b+96>>2]=0;return 0}function qb(a,b,c,d,e,f){var g=0,h=R(0),i=0,j=R(0);g=(c<<6)+b|0;a=c?g+-64|0:a;i=d>>>0<e>>>0?d:e;a:{if(i>>>0<=c>>>0){b=a;break a}h=O[a>>2];while(1){b=g;g=b-32|0;j=h;h=O[b>>2];O[g>>2]=R(R(j+h)*f)+O[g>>2];g=b-28|0;O[g>>2]=R(R(O[a+4>>2]+O[b+4>>2])*f)+O[g>>2];g=b-24|0;O[g>>2]=R(R(O[a+8>>2]+O[b+8>>2])*f)+O[g>>2];g=b-20|0;O[g>>2]=R(R(O[a+12>>2]+O[b+12>>2])*f)+O[g>>2];g=b-16|0;O[g>>2]=R(R(O[a+16>>2]+O[b+16>>2])*f)+O[g>>2];g=b-12|0;O[g>>2]=R(R(O[a+20>>2]+O[b+20>>2])*f)+O[g>>2];g=b-8|0;O[g>>2]=R(R(O[a+24>>2]+O[b+24>>2])*f)+O[g>>2];g=b-4|0;O[g>>2]=R(R(O[a+28>>2]+O[b+28>>2])*f)+O[g>>2];g=b- -64|0;a=b;c=c+1|0;if((i|0)!=(c|0)){continue}break}}if(d>>>0>e>>>0){a=g-32|0;f=R(f+f);O[a>>2]=R(O[b>>2]*f)+O[a>>2];a=g-28|0;O[a>>2]=R(O[b+4>>2]*f)+O[a>>2];a=g-24|0;O[a>>2]=R(O[b+8>>2]*f)+O[a>>2];a=g-20|0;O[a>>2]=R(O[b+12>>2]*f)+O[a>>2];a=g-16|0;O[a>>2]=R(O[b+16>>2]*f)+O[a>>2];a=g-12|0;O[a>>2]=R(O[b+20>>2]*f)+O[a>>2];a=g-8|0;O[a>>2]=R(O[b+24>>2]*f)+O[a>>2];a=g-4|0;O[a>>2]=R(O[b+28>>2]*f)+O[a>>2]}}function Pc(a){var b=0,c=0,d=0,e=0,f=0;d=K[6506];b=a+7&-8;c=b+7&-8;a=d+c|0;a:{b:{if(!(a>>>0<=d>>>0?c:0)){if(a>>>0<=wa()<<16>>>0){break b}if(na(a|0)|0){break b}}K[6585]=48;d=-1;break a}K[6506]=a}if((d|0)!=-1){a=b+d|0;K[a-4>>2]=16;c=a-16|0;K[c>>2]=16;b=K[6844];if(b){f=K[b+8>>2]}else{f=0}c:{d:{if((f|0)==(d|0)){e=d-(K[d-4>>2]&-2)|0;f=K[e-4>>2];K[b+8>>2]=a;a=e-(f&-2)|0;if(I[(a+K[a>>2]|0)-4|0]&1){b=K[a+4>>2];e=K[a+8>>2];K[b+8>>2]=e;K[e+4>>2]=b;b=c-a|0;K[a>>2]=b;break c}a=d-16|0;break d}K[d>>2]=16;K[d+8>>2]=a;K[d+4>>2]=b;K[d+12>>2]=16;K[6844]=d;a=d+16|0}b=c-a|0;K[a>>2]=b}K[((b&-4)+a|0)-4>>2]=b|1;c=K[a>>2]-8|0;e:{if(c>>>0<=127){b=(c>>>3|0)-1|0;break e}e=T(c);b=((c>>>29-e^4)-(e<<2)|0)+110|0;if(c>>>0<=4095){break e}b=((c>>>30-e^2)-(e<<1)|0)+71|0;b=b>>>0>=63?63:b}c=b<<4;K[a+4>>2]=c+26352;c=c+26360|0;K[a+8>>2]=K[c>>2];K[c>>2]=a;K[K[a+8>>2]+4>>2]=a;c=K[6846];e=K[6847];a=b&31;if((b&63)>>>0>=32){b=1<<a;f=0}else{f=1<<a;b=f-1&1>>>32-a}K[6846]=f|c;K[6847]=b|e}return(d|0)!=-1}function Ld(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;f=ra-16|0;ra=f;a:{if(K[a+120>>2]|c>>>0<3){break a}Ha(b,f+12|0,2);k=M[f+12>>1];if(k-1025>>>0<=4294966271){K[f>>2]=k;Fa(d,1,3489,f);break a}Ha(b+2|0,f+12|0,1);i=M[f+12>>1];if(!i){Fa(d,1,3137,0);break a}if(i+3>>>0>c>>>0){break a}h=Ja(Q(i,k)<<2);if(!h){break a}j=Ja(i);if(!j){Ga(h);break a}l=Ja(i);if(!l){Ga(h);Ga(j);break a}g=Ja(20);if(!g){Ga(h);Ga(j);Ga(l);break a}d=b+3|0;K[g+8>>2]=j;K[g+4>>2]=l;J[g+16>>1]=k;K[g>>2]=h;m=K[f+12>>2];K[g+12>>2]=0;I[g+18|0]=m;K[a+120>>2]=g;while(1){Ha(d,f+12|0,1);I[e+j|0]=(L[f+12|0]&127)+1;I[e+l|0]=(K[f+12>>2]&128)>>>7;d=d+1|0;e=e+1|0;if((i|0)!=(e|0)){continue}break}g=0;while(1){e=0;a=0;while(1){e=L[e+j|0]+7>>>3|0;e=e>>>0>=4?4:e;if((e+(d-b|0)|0)>(c|0)){e=0;break a}Ha(d,f+12|0,e);K[h>>2]=K[f+12>>2];h=h+4|0;d=d+e|0;a=a+1|0;e=a&65535;if(i>>>0>e>>>0){continue}break}e=1;g=g+1|0;if((g&65535)>>>0<k>>>0){continue}break}}ra=f+16|0;return e|0}function Dd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;f=-1;e=-1;if(!(L[a+68|0]&8)){f=K[a+32>>2];K[a+36>>2]=f;a:{b:{c:{e=K[a+48>>2];if(e){while(1){e=va[K[a+20>>2]](f,e,K[a>>2])|0;if((e|0)==-1){break c}f=e+K[a+36>>2]|0;K[a+36>>2]=f;e=K[a+48>>2]-e|0;K[a+48>>2]=e;if(e){continue}break}f=K[a+32>>2]}K[a+36>>2]=f;if(!!b&(c|0)>=0|(c|0)>0){break b}f=0;e=0;break a}K[a+68>>2]=K[a+68>>2]|8;Fa(d,4,15567,0);K[a+48>>2]=0;K[a+68>>2]=K[a+68>>2]|8;ua=-1;return-1}f=0;e=0;while(1){g=va[K[a+24>>2]](b,c,K[a>>2])|0;h=ua;i=h;if((g&h)==-1){Fa(d,4,15552,0);K[a+68>>2]=K[a+68>>2]|8;b=e+K[a+60>>2]|0;c=f+K[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;K[a+56>>2]=c;K[a+60>>2]=b;a=!(e|f);b=a?-1:f;ua=a?-1:e;return b|0}e=e+i|0;f=f+g|0;e=f>>>0<g>>>0?e+1|0:e;h=b;b=b-g|0;c=c-(i+(g>>>0>h>>>0)|0)|0;if(!!b&(c|0)>=0|(c|0)>0){continue}break}}b=e+K[a+60>>2]|0;c=f+K[a+56>>2]|0;b=c>>>0<f>>>0?b+1|0:b;K[a+56>>2]=c;K[a+60>>2]=b}ua=e;return f|0}function Oc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;b=a;a:{if(b&3){while(1){c=L[b|0];if(!c|(c|0)==61){break a}b=b+1|0;if(b&3){continue}break}}b:{c:{d=K[b>>2];if(((d|16843008-d)&-2139062144)!=-2139062144){break c}while(1){c=d^1027423549;if(((16843008-c|c)&-2139062144)!=-2139062144){break c}d=K[b+4>>2];c=b+4|0;b=c;if(((16843008-d|d)&-2139062144)==-2139062144){continue}break}break b}c=b}while(1){b=c;d=L[b|0];if(!d){break a}c=b+1|0;if((d|0)!=61){continue}break}}if((a|0)==(b|0)){return 0}g=b-a|0;d:{if(L[g+a|0]){break d}f=K[6848];if(!f){break d}b=K[f>>2];if(!b){break d}while(1){e:{d=a;c=b;h=g;e=0;f:{if(!g){break f}e=L[d|0];if(e){g:{while(1){i=L[c|0];if((i|0)!=(e|0)|!i){break g}h=h-1|0;if(!h){break g}c=c+1|0;e=L[d+1|0];d=d+1|0;if(e){continue}break}e=0}}else{e=0}e=e-L[c|0]|0}if(!e){b=b+g|0;if(L[b|0]==61){break e}}b=K[f+4>>2];f=f+4|0;if(b){continue}break d}break}j=b+1|0}return j}function ue(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;g=ra-16|0;ra=g;a:{if(c>>>0<=1){Fa(d,1,3946,0);a=0;break a}if(I[a+212|0]&1){Fa(d,1,12631,0);a=0;break a}a=K[a+180>>2]+Q(K[a+228>>2],5644)|0;I[a+5640|0]=L[a+5640|0]|2;Ha(b,g+12|0,1);e=K[a+5164>>2];b:{if(!e){f=K[g+12>>2]+1|0;e=Ia(f,8);K[a+5164>>2]=e;if(!e){Fa(d,1,3972,0);a=0;break a}K[a+5160>>2]=f;break b}f=K[g+12>>2];if(f>>>0<N[a+5160>>2]){break b}h=e;e=f+1|0;f=La(h,e<<3);if(!f){Fa(d,1,3972,0);a=0;break a}K[a+5164>>2]=f;h=K[a+5160>>2];i=e-h<<3;if(i){B(f+(h<<3)|0,0,i)}K[a+5160>>2]=e;e=K[a+5164>>2]}h=e;e=K[g+12>>2];if(K[h+(e<<3)>>2]){K[g>>2]=e;Fa(d,1,7026,g);a=0;break a}c=c-1|0;e=Ja(c);a=K[a+5164>>2];f=K[g+12>>2];K[a+(f<<3)>>2]=e;if(!e){Fa(d,1,3972,0);a=0;break a}K[(a+(f<<3)|0)+4>>2]=c;if(c){E(K[a+(K[g+12>>2]<<3)>>2],b+1|0,c)}a=1}ra=g+16|0;return a|0}function Lb(a,b,c){var d=0,e=0,f=0,g=0;e=a+4|0;d=(e+b|0)-1&0-b;b=K[a>>2];if(d+c>>>0<=(b+a|0)-4>>>0){f=K[a+4>>2];g=K[a+8>>2];K[f+8>>2]=g;K[g+4>>2]=f;if((d|0)!=(e|0)){d=d-e|0;f=a-(K[a-4>>2]&-2)|0;e=d+K[f>>2]|0;K[f>>2]=e;K[(f+(e&-4)|0)-4>>2]=e;a=a+d|0;b=b-d|0;K[a>>2]=b}a:{if(c+24>>>0<=b>>>0){e=a+c|0;b=(b-c|0)-8|0;K[e+8>>2]=b;g=e+8|0;K[(g+(b&-4)|0)-4>>2]=b|1;d=K[e+8>>2]-8|0;b:{if(d>>>0<=127){b=(d>>>3|0)-1|0;break b}f=T(d);b=((d>>>29-f^4)-(f<<2)|0)+110|0;if(d>>>0<=4095){break b}b=((d>>>30-f^2)-(f<<1)|0)+71|0;b=b>>>0>=63?63:b}d=b<<4;K[e+12>>2]=d+26352;d=d+26360|0;K[e+16>>2]=K[d>>2];K[d>>2]=g;K[K[e+16>>2]+4>>2]=g;d=K[6846];f=K[6847];e=b&31;if((b&63)>>>0>=32){b=1<<e;g=0}else{g=1<<e;b=g-1&1>>>32-e}K[6846]=g|d;K[6847]=b|f;b=c+8|0;K[a>>2]=b;c=(b&-4)+a|0;break a}c=a+b|0}K[c-4>>2]=b;a=a+4|0}else{a=0}return a}function Ae(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;g=ra-16|0;ra=g;i=K[K[a+96>>2]+16>>2];h=i>>>0<257?1:2;e=(h<<1)+5|0;f=(c>>>0)/(e>>>0)|0;a:{if(!((Q(e,f)|0)==(c|0)&c>>>0>=e>>>0)){Fa(d,1,4606,0);a=0;break a}if(K[a+8>>2]==16){e=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{e=K[a+12>>2]}a=0;c=L[e+5640|0];a=c&4?K[e+420>>2]+1|0:a;f=f+a|0;if(f>>>0>=32){K[g>>2]=f;Fa(d,1,7744,g);a=0;break a}I[e+5640|0]=c|4;if(a>>>0<f>>>0){c=(e+Q(a,148)|0)+424|0;while(1){Ha(b,c,1);b=b+1|0;Ha(b,c+4|0,h);b=b+h|0;Ha(b,c+8|0,2);d=K[c+8>>2];j=K[e+8>>2];K[c+8>>2]=d>>>0<j>>>0?d:j;Ha(b+2|0,c+12|0,1);b=b+3|0;Ha(b,c+16|0,h);b=b+h|0;Ha(b,g+12|0,1);K[c+36>>2]=K[g+12>>2];d=K[c+16>>2];K[c+16>>2]=d>>>0<i>>>0?d:i;c=c+148|0;b=b+1|0;a=a+1|0;if((f|0)!=(a|0)){continue}break}}K[e+420>>2]=f-1;a=1}ra=g+16|0;return a|0}function nb(a){var b=0,c=0,d=0,e=0;a:{if(!a){break a}b=K[a+5164>>2];if(b){c=K[a+5160>>2];if(c){b=0;while(1){d=K[K[a+5164>>2]+(b<<3)>>2];if(d){Ga(d);c=K[a+5160>>2]}b=b+1|0;if(c>>>0>b>>>0){continue}break}b=K[a+5164>>2]}K[a+5160>>2]=0;Ga(b);K[a+5164>>2]=0}b=K[a+5172>>2];if(b){Ga(b);K[a+5172>>2]=0}b=K[a+5584>>2];if(b){Ga(b);K[a+5584>>2]=0}b=K[a+5612>>2];if(b){Ga(b);K[a+5612>>2]=0}b=K[a+5608>>2];if(b){Ga(b);K[a+5608>>2]=0}b=K[a+5628>>2];if(b){Ga(b);K[a+5636>>2]=0;K[a+5628>>2]=0;K[a+5632>>2]=0}b=K[a+5616>>2];if(b){e=K[a+5620>>2];if(e){c=0;while(1){d=K[b+12>>2];if(d){Ga(d);K[b+12>>2]=0;e=K[a+5620>>2]}b=b+20|0;c=c+1|0;if(e>>>0>c>>>0){continue}break}b=K[a+5616>>2]}Ga(b);K[a+5616>>2]=0}b=K[a+5604>>2];if(b){Ga(b);K[a+5604>>2]=0}b=K[a+5596>>2];if(!b){break a}Ga(b);K[a+5596>>2]=0;K[a+5600>>2]=0}}function Od(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=ra-32|0;ra=e;a:{if(K[a+72>>2]){Fa(d,2,6978,0);c=1;break a}if((c|0)!=14){c=0;Fa(d,1,14408,0);break a}Ha(b,a+16|0,4);Ha(b+4|0,a+12|0,4);Ha(b+8|0,a+20|0,2);f=K[a+12>>2];b:{g=K[a+16>>2];c=K[a+20>>2];c:{if(!g){break c}c=K[a+20>>2];if(!f){break c}if(c){break b}c=0}K[e+8>>2]=c;K[e+4>>2]=g;K[e>>2]=f;Fa(d,1,14252,e);c=0;break a}if(c-16385>>>0<=4294950911){c=0;Fa(d,1,14166,0);break a}c=Ia(c,12);K[a+72>>2]=c;if(!c){c=0;Fa(d,1,14203,0);break a}c=1;Ha(b+10|0,a+24|0,1);Ha(b+11|0,a+28|0,1);f=K[a+28>>2];if((f|0)!=7){K[e+16>>2]=f;Fa(d,4,16235,e+16|0)}Ha(b+12|0,a+32|0,1);Ha(b+13|0,a+36|0,1);b=K[a>>2];I[b+212|0]=L[b+212|0]&251|(K[a+24>>2]==255?4:0);b=K[a>>2];K[b+240>>2]=K[a+12>>2];K[b+244>>2]=K[a+16>>2];I[a+133|0]=1}ra=e+32|0;return c|0}function Hc(a,b,c,d){a:{switch(b-9|0){case 0:b=K[c>>2];K[c>>2]=b+4;K[a>>2]=K[b>>2];return;case 6:b=K[c>>2];K[c>>2]=b+4;b=J[b>>1];K[a>>2]=b;K[a+4>>2]=b>>31;return;case 7:b=K[c>>2];K[c>>2]=b+4;K[a>>2]=M[b>>1];K[a+4>>2]=0;return;case 8:b=K[c>>2];K[c>>2]=b+4;b=I[b|0];K[a>>2]=b;K[a+4>>2]=b>>31;return;case 9:b=K[c>>2];K[c>>2]=b+4;K[a>>2]=L[b|0];K[a+4>>2]=0;return;case 16:b=K[c>>2]+7&-8;K[c>>2]=b+8;P[a>>3]=P[b>>3];return;case 17:va[d|0](a,c);default:return;case 1:case 4:case 14:b=K[c>>2];K[c>>2]=b+4;b=K[b>>2];K[a>>2]=b;K[a+4>>2]=b>>31;return;case 2:case 5:case 11:case 15:b=K[c>>2];K[c>>2]=b+4;K[a>>2]=K[b>>2];K[a+4>>2]=0;return;case 3:case 10:case 12:case 13:break a}}b=K[c>>2]+7&-8;K[c>>2]=b+8;c=K[b+4>>2];K[a>>2]=K[b>>2];K[a+4>>2]=c}function ve(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;g=ra-16|0;ra=g;a:{if(c>>>0<=1){Fa(d,1,4274,0);a=0;break a}I[a+212|0]=L[a+212|0]|1;Ha(b,g+12|0,1);e=K[a+140>>2];b:{if(!e){f=K[g+12>>2]+1|0;e=Ia(f,8);K[a+140>>2]=e;if(!e){Fa(d,1,4300,0);a=0;break a}K[a+136>>2]=f;break b}f=K[g+12>>2];if(f>>>0<N[a+136>>2]){break b}h=e;e=f+1|0;f=La(h,e<<3);if(!f){Fa(d,1,4300,0);a=0;break a}K[a+140>>2]=f;h=K[a+136>>2];i=e-h<<3;if(i){B(f+(h<<3)|0,0,i)}K[a+136>>2]=e;e=K[a+140>>2]}h=e;e=K[g+12>>2];if(K[h+(e<<3)>>2]){K[g>>2]=e;Fa(d,1,7048,g);a=0;break a}c=c-1|0;e=Ja(c);a=K[a+140>>2];f=K[g+12>>2];K[a+(f<<3)>>2]=e;if(!e){Fa(d,1,4300,0);a=0;break a}K[(a+(f<<3)|0)+4>>2]=c;if(c){E(K[a+(K[g+12>>2]<<3)>>2],b+1|0,c)}a=1}ra=g+16|0;return a|0}function yd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;d=ra-32|0;ra=d;e=K[a+28>>2];K[d+16>>2]=e;f=K[a+20>>2];K[d+28>>2]=c;K[d+24>>2]=b;b=f-e|0;K[d+20>>2]=b;f=b+c|0;i=2;b=d+16|0;a:{while(1){b:{c:{d:{if(!Kb(ba(K[a+60>>2],b|0,i|0,d+12|0)|0)){g=K[d+12>>2];if((g|0)==(f|0)){break d}if((g|0)>=0){break c}break b}if((f|0)!=-1){break b}}b=K[a+44>>2];K[a+28>>2]=b;K[a+20>>2]=b;K[a+16>>2]=b+K[a+48>>2];a=c;break a}h=K[b+4>>2];j=h>>>0<g>>>0;e=(j?8:0)+b|0;h=g-(j?h:0)|0;K[e>>2]=h+K[e>>2];b=(j?12:4)+b|0;K[b>>2]=K[b>>2]-h;f=f-g|0;i=i-j|0;b=e;continue}break}K[a+28>>2]=0;K[a+16>>2]=0;K[a+20>>2]=0;K[a>>2]=K[a>>2]|32;a=0;if((i|0)==2){break a}a=c-K[b+4>>2]|0}ra=d+32|0;return a|0}function Ga(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;if(a){b=a-4|0;f=K[b>>2];c=f;d=b;e=K[a-8>>2];a=e&-2;if((a|0)!=(e|0)){d=b-a|0;c=K[d+4>>2];e=K[d+8>>2];K[c+8>>2]=e;K[e+4>>2]=c;c=a+f|0}a=b+f|0;b=K[a>>2];if((b|0)!=K[(a+b|0)-4>>2]){f=K[a+4>>2];a=K[a+8>>2];K[f+8>>2]=a;K[a+4>>2]=f;c=b+c|0}K[d>>2]=c;K[((c&-4)+d|0)-4>>2]=c|1;b=K[d>>2]-8|0;a:{if(b>>>0<=127){a=(b>>>3|0)-1|0;break a}c=T(b);a=((b>>>29-c^4)-(c<<2)|0)+110|0;if(b>>>0<=4095){break a}a=((b>>>30-c^2)-(c<<1)|0)+71|0;a=a>>>0>=63?63:a}b=a<<4;K[d+4>>2]=b+26352;b=b+26360|0;K[d+8>>2]=K[b>>2];K[b>>2]=d;K[K[d+8>>2]+4>>2]=d;b=K[6846];c=K[6847];d=a&31;if((a&63)>>>0>=32){a=1<<d;e=0}else{e=1<<d;a=e-1&1>>>32-d}K[6846]=e|b;K[6847]=a|c}}function ld(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;if(N[a+44>>2]>=8){e=K[a+36>>2];j=e<<5;k=Q(e,28);l=Q(e,24);m=Q(e,20);n=e<<4;o=Q(e,12);p=e<<3;f=K[a+40>>2];g=8;while(1){Hb(a,f,K[a+36>>2],8);Za(a);h=K[a+32>>2];if(h){i=K[a>>2];b=0;while(1){c=(b<<2)+f|0;d=i+(b<<5)|0;O[c>>2]=O[d>>2];O[c+(e<<2)>>2]=O[d+4>>2];O[c+p>>2]=O[d+8>>2];O[c+o>>2]=O[d+12>>2];b=b+1|0;if((h|0)!=(b|0)){continue}break}i=K[a>>2];b=0;while(1){c=(b<<2)+f|0;d=i+(b<<5)|0;O[c+n>>2]=O[d+16>>2];O[c+m>>2]=O[d+20>>2];O[c+l>>2]=O[d+24>>2];O[c+k>>2]=O[d+28>>2];b=b+1|0;if((h|0)!=(b|0)){continue}break}}f=f+j|0;g=g+8|0;if(g>>>0<=N[a+44>>2]){continue}break}}Ga(K[a>>2]);Ga(a)}function Id(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;e=ra-16|0;ra=e;a:{if(K[a+116>>2]){break a}if(c>>>0<=1){Fa(d,1,8845,0);break a}Ha(b,e+12|0,2);f=K[e+12>>2];h=f&65535;if(!h){Fa(d,1,8878,0);break a}if(Q(h,6)+2>>>0>c>>>0){Fa(d,1,8845,0);break a}d=Ja(Q(f,6));if(!d){break a}c=Ja(8);K[a+116>>2]=c;if(!c){Ga(d);break a}K[c>>2]=d;f=c;c=M[e+12>>1];J[f+4>>1]=c;if(!c){g=1;break a}c=0;while(1){g=e+12|0;Ha(b+2|0,g,2);f=d+Q(c,6)|0;J[f>>1]=K[e+12>>2];Ha(b+4|0,g,2);J[f+2>>1]=K[e+12>>2];b=b+6|0;Ha(b,g,2);J[f+4>>1]=K[e+12>>2];g=1;c=c+1|0;if(c>>>0<M[K[a+116>>2]+4>>1]){continue}break}}ra=e+16|0;return g|0}function $b(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;g=ra-32|0;ra=g;f=K[a+96>>2];a:{if(!f){Fa(d,1,13715,0);e=0;break a}f=Ia(4,K[f+16>>2]);e=0;if(!f){break a}if(b){j=K[a+96>>2];while(1){b:{e=K[(h<<2)+c>>2];c:{if(e>>>0>=N[j+16>>2]){K[g+16>>2]=e;Fa(d,1,2406,g+16|0);break c}i=f+(e<<2)|0;if(!K[i>>2]){break b}K[g>>2]=e;Fa(d,1,3450,g)}Ga(f);e=0;break a}K[i>>2]=1;h=h+1|0;if((h|0)!=(b|0)){continue}break}}Ga(f);Ga(K[a+64>>2]);d:{if(b){d=b<<2;e=Ja(d);K[a+64>>2]=e;if(!e){K[a+60>>2]=0;e=0;break a}if(!d){break d}E(e,c,d);break d}K[a+64>>2]=0}K[a+60>>2]=b;e=1}ra=g+32|0;return e|0}function Tc(a){a=a|0;var b=0,c=0;if(a){Eb(K[a>>2]);K[a>>2]=0;b=K[a+72>>2];if(b){Ga(b);K[a+72>>2]=0}b=K[a+68>>2];if(b){Ga(b);K[a+68>>2]=0}b=K[a+108>>2];if(b){Ga(b);K[a+108>>2]=0}b=K[a+116>>2];if(b){c=K[b>>2];if(c){Ga(c);b=K[a+116>>2];K[b>>2]=0}Ga(b);K[a+116>>2]=0}b=K[a+120>>2];if(b){c=K[b+12>>2];if(c){Ga(c);b=K[a+120>>2];K[b+12>>2]=0}c=K[b+4>>2];if(c){Ga(c);b=K[a+120>>2];K[b+4>>2]=0}c=K[b+8>>2];if(c){Ga(c);b=K[a+120>>2];K[b+8>>2]=0}c=K[b>>2];if(c){Ga(c);b=K[a+120>>2];K[b>>2]=0}Ga(b);K[a+120>>2]=0}b=K[a+4>>2];if(b){tb(b);K[a+4>>2]=0}b=K[a+8>>2];if(b){tb(b);K[a+8>>2]=0}Ga(a)}}function Yb(){var a=0,b=0,c=0;a:{a=Ia(1,256);if(a){K[a>>2]=1;K[a+208>>2]=1;I[a+212|0]=L[a+212|0]|6;b=Ia(1,5644);K[a+12>>2]=b;if(!b){break a}b=Ia(1,1e3);K[a+16>>2]=b;if(!b){break a}K[a+48>>2]=0;K[a+52>>2]=0;K[a+44>>2]=-1;K[a+20>>2]=1e3;b:{c=Ia(1,48);if(c){K[c+24>>2]=0;K[c+32>>2]=100;b=Ia(100,24);K[c+28>>2]=b;if(b){break b}Ga(c)}K[a+224>>2]=0;break a}K[c+40>>2]=0;K[a+224>>2]=c;b=ub();K[a+220>>2]=b;if(!b){break a}b=ub();K[a+216>>2]=b;if(!b){break a}c:{if(!Oc(1382)){break c}}b=zc();K[a+236>>2]=b;if(!b){b=zc();K[a+236>>2]=b;if(!b){break a}}}else{a=0}return a}Eb(a);return 0}function xb(a,b,c,d,e,f){var g=0,h=0,i=0,j=0,k=0,l=0;g=ra-240|0;ra=g;K[g+236>>2]=c;K[g+232>>2]=b;K[g>>2]=a;l=!e;a:{b:{c:{d:{if((b|0)!=1){h=a;i=1;break d}h=a;i=1;if(c){break d}e=a;break c}while(1){j=(d<<2)+f|0;e=h-K[j>>2]|0;if((gb(e,a)|0)<=0){e=h;break c}k=l^-1;l=1;e:{if(!((k|(d|0)<2)&1)){j=K[j-8>>2];k=h-8|0;if((gb(k,e)|0)>=0){break e}if((gb(k-j|0,e)|0)>=0){break e}}K[(i<<2)+g>>2]=e;b=Nc(b,c);yb(g+232|0,b);i=i+1|0;d=b+d|0;h=e;c=K[g+236>>2];b=K[g+232>>2];if(c|(b|0)!=1){continue}break b}break}e=h;break b}if(!l){break a}}Mc(g,i);Jb(e,d,f)}ra=g+240|0}function Kc(a,b,c,d,e){var f=0,g=0,h=0;f=ra-208|0;ra=f;K[f+204>>2]=c;c=f+160|0;B(c,0,40);K[f+200>>2]=K[f+204>>2];a:{if((Jc(0,b,f+200|0,f+80|0,c,d,e)|0)<0){break a}c=K[a+76>>2]<0;g=K[a>>2];K[a>>2]=g&-33;b:{c:{d:{if(!K[a+48>>2]){K[a+48>>2]=80;K[a+28>>2]=0;K[a+16>>2]=0;K[a+20>>2]=0;h=K[a+44>>2];K[a+44>>2]=f;break d}if(K[a+16>>2]){break c}}if(Nb(a)){break b}}Jc(a,b,f+200|0,f+80|0,f+160|0,d,e)}if(h){va[K[a+36>>2]](a,0,0)|0;K[a+48>>2]=0;K[a+44>>2]=h;K[a+28>>2]=0;K[a+16>>2]=0;K[a+20>>2]=0}K[a>>2]=K[a>>2]|g&32;if(c){break a}}ra=f+208|0}function Fe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=ra-16|0;ra=e;if(K[a+8>>2]==16){g=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{g=K[a+12>>2]}h=K[a+96>>2];f=N[h+16>>2]<257?1:2;a:{if(f>>>0>=c>>>0){c=0;Fa(d,1,4632,0);break a}K[e+12>>2]=(f^-1)+c;Ha(b,e+8|0,f);i=K[e+8>>2];if(i>>>0>=N[h+16>>2]){c=0;Fa(d,1,14030,0);break a}c=1;b=b+f|0;Ha(b,K[g+5584>>2]+Q(i,1080)|0,1);if(!_c(a,K[e+8>>2],b+1|0,e+12|0,d)){c=0;Fa(d,1,4632,0);break a}if(!K[e+12>>2]){break a}c=0;Fa(d,1,4632,0)}ra=e+16|0;return c|0}function Vc(a,b){var c=0,d=0,e=0,f=0,g=0;f=ra-32|0;ra=f;c=K[a+60>>2];a:{b:{if(c){g=1;while(1){e=K[K[a+64>>2]+(d<<2)>>2];if(!K[(K[K[a+100>>2]+24>>2]+Q(e,52)|0)+44>>2]){K[f+16>>2]=e;Fa(b,2,7567,f+16|0);g=0;c=K[a+60>>2]}d=d+1|0;if(c>>>0>d>>>0){continue}break}break b}g=1;c=K[a+100>>2];e=1;if(!K[c+16>>2]){break a}while(1){if(!K[(K[c+24>>2]+Q(d,52)|0)+44>>2]){K[f>>2]=d;Fa(b,2,7567,f);g=0;c=K[a+100>>2]}d=d+1|0;if(d>>>0<N[c+16>>2]){continue}break}}e=1;if(g){break a}Fa(b,1,2860,0);e=0}ra=f+32|0;return e}function Kd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;f=ra-16|0;ra=f;e=K[a+120>>2];a:{if(!e){Fa(d,1,8799,0);c=0;break a}if(K[e+12>>2]){Fa(d,1,11561,0);c=0;break a}e=L[e+18|0];g=e<<2;if(g>>>0>c>>>0){Fa(d,1,8766,0);c=0;break a}g=Ja(g);c=0;if(!g){break a}if(e){d=0;while(1){c=f+12|0;Ha(b,c,2);h=g+(d<<2)|0;J[h>>1]=K[f+12>>2];Ha(b+2|0,c,1);I[h+2|0]=K[f+12>>2];Ha(b+3|0,c,1);I[h+3|0]=K[f+12>>2];b=b+4|0;d=d+1|0;if((e|0)!=(d|0)){continue}break}}K[K[a+120>>2]+12>>2]=g;c=1}ra=f+16|0;return c|0}function qe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0;e=ra-16|0;ra=e;g=K[K[a+96>>2]+16>>2];a:{if((g+2|0)!=(c|0)){Fa(d,1,4580,0);break a}Ha(b,e+12|0,2);if(K[e+12>>2]!=(g|0)){Fa(d,1,4580,0);break a}if(!g){f=1;break a}c=b+2|0;a=K[K[a+96>>2]+24>>2];b=0;while(1){Ha(c,e+8|0,1);f=K[e+8>>2];h=f&127;i=h+1|0;K[a+24>>2]=i;K[a+32>>2]=f>>>7&1;if(h>>>0>=31){K[e+4>>2]=i;K[e>>2]=b;Fa(d,1,15365,e);f=0;break a}a=a+52|0;f=1;c=c+1|0;b=b+1|0;if((g|0)!=(b|0)){continue}break}}ra=e+16|0;return f|0}function Ce(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;e=ra-16|0;ra=e;a:{b:{h=e+8|0;c:{if(N[K[a+96>>2]+16>>2]<=256){if(c){f=-1;g=1;break c}Fa(d,1,4658,0);a=0;break a}if(c>>>0<=1){break b}f=-2;g=2}Ha(b,h,g);K[e+12>>2]=c+f;c=K[e+8>>2];f=K[K[a+96>>2]+16>>2];if(c>>>0>=f>>>0){K[e+4>>2]=f;K[e>>2]=c;Fa(d,1,7675,e);a=0;break a}if(!Zc(a,c,b+g|0,e+12|0,d)){Fa(d,1,4658,0);a=0;break a}a=1;if(!K[e+12>>2]){break a}Fa(d,1,4658,0);a=0;break a}Fa(d,1,4658,0);a=0}ra=e+16|0;return a|0}function tc(a,b,c,d){var e=0,f=0,g=0;g=ra-128|0;ra=g;f=g;c=K[b+12>>2]+(c<<4)|0;e=K[c>>2];a:{if(!e){b=c;break a}while(1){K[f>>2]=c;f=f+4|0;b=e;c=b;e=K[c>>2];if(e){continue}break}}e=0;while(1){c=K[b+8>>2];if((e|0)>(c|0)){K[b+8>>2]=e;c=e}b:{if((c|0)>=(d|0)){break b}while(1){if(K[b+4>>2]<=(c|0)){break b}c:{if(Wa(a,1)){K[b+4>>2]=c;break c}c=c+1|0}if((c|0)<(d|0)){continue}break}}K[b+8>>2]=c;if((f|0)!=(g|0)){f=f-4|0;b=K[f>>2];e=c;continue}break}ra=g+128|0;return K[b+4>>2]<(d|0)}
function Ud(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;f=K[a+32>>2];K[a+36>>2]=f;a:{e=K[a+48>>2];if(e){while(1){e=va[K[a+20>>2]](f,e,K[a>>2])|0;if((e|0)==-1){break a}f=e+K[a+36>>2]|0;K[a+36>>2]=f;e=K[a+48>>2]-e|0;K[a+48>>2]=e;if(e){continue}break}f=K[a+32>>2]}K[a+48>>2]=0;K[a+36>>2]=f;if(!(va[K[a+28>>2]](b,c,K[a>>2])|0)){K[a+68>>2]=K[a+68>>2]|8;return 0}K[a+56>>2]=b;K[a+60>>2]=c;return 1}K[a+68>>2]=K[a+68>>2]|8;Fa(d,4,15567,0);K[a+68>>2]=K[a+68>>2]|8;return 0}function Fa(a,b,c,d){var e=0,f=0;e=ra-528|0;ra=e;a:{if(!a){break a}b:{c:{switch(b-1|0){case 0:b=a+12|0;break b;case 1:b=a+16|0;a=a+4|0;break b;case 3:break c;default:break a}}b=a+20|0;a=a+8|0}b=K[b>>2];if(!b|!c){break a}f=K[a>>2];B(e,0,512);K[e+524>>2]=d;a=ra-160|0;ra=a;K[a+148>>2]=e;K[a+152>>2]=511;B(a,0,144);K[a+76>>2]=-1;K[a+36>>2]=103;K[a+80>>2]=-1;K[a+44>>2]=a+159;K[a+84>>2]=a+148;I[e|0]=0;Kc(a,c,d,104,105);ra=a+160|0;I[e+511|0]=0;va[b|0](e,f)}ra=e+528|0}function Qd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;if(K[a+100>>2]!=1){Fa(d,1,11364,0);return 0}a:{if(c>>>0<=7){break a}Ha(b,a+56|0,4);Ha(b+4|0,a+60|0,4);if(c&3){break a}c=c-8|0;e=c>>>2|0;K[a+64>>2]=e;b:{if(!c){break b}c=Ia(e,4);K[a+68>>2]=c;if(!c){Fa(d,1,2198,0);return 0}if(!K[a+64>>2]){break b}d=b+8|0;c=0;while(1){Ha(d,K[a+68>>2]+(c<<2)|0,4);d=d+4|0;c=c+1|0;if(c>>>0<N[a+64>>2]){continue}break}}K[a+100>>2]=K[a+100>>2]|2;return 1}Fa(d,1,5918,0);return 0}function vc(a){var b=0,c=0,d=0;a:{if(!a){break a}b=K[a+8>>2];if(!b){break a}a=K[a+12>>2];if(b>>>0>=4){d=b&-4;while(1){K[a+60>>2]=0;K[a+52>>2]=999;K[a+56>>2]=0;K[a+44>>2]=0;K[a+36>>2]=999;K[a+40>>2]=0;K[a+28>>2]=0;K[a+20>>2]=999;K[a+24>>2]=0;K[a+12>>2]=0;K[a+4>>2]=999;K[a+8>>2]=0;a=a- -64|0;c=c+4|0;if((d|0)!=(c|0)){continue}break}}b=b&3;if(!b){break a}c=0;while(1){K[a+12>>2]=0;K[a+4>>2]=999;K[a+8>>2]=0;a=a+16|0;c=c+1|0;if((b|0)!=(c|0)){continue}break}}}function De(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=ra-16|0;ra=e;K[e+12>>2]=c;a:{if(!(!Zc(a,0,b,e+12|0,d)|K[e+12>>2])){if(K[a+8>>2]==16){b=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{b=K[a+12>>2]}f=1;if(N[K[a+96>>2]+16>>2]<2){break a}c=K[b+5584>>2];g=c+28|0;b=1;d=c;while(1){K[d+1104>>2]=K[c+24>>2];K[d+1884>>2]=K[c+804>>2];E(d+1108|0,g,776);d=d+1080|0;b=b+1|0;if(b>>>0<N[K[a+96>>2]+16>>2]){continue}break}break a}Fa(d,1,4554,0)}ra=e+16|0;return f|0}function Gc(a,b){a:{b:{if(b>>>0<=127){break b}c:{if(!K[K[6873]>>2]){if((b&-128)==57216){break b}break c}if(b>>>0<=2047){I[a+1|0]=b&63|128;I[a|0]=b>>>6|192;a=2;break a}if(!((b&-8192)!=57344&b>>>0>=55296)){I[a+2|0]=b&63|128;I[a|0]=b>>>12|224;I[a+1|0]=b>>>6&63|128;a=3;break a}if(b-65536>>>0<=1048575){I[a+3|0]=b&63|128;I[a|0]=b>>>18|240;I[a+2|0]=b>>>6&63|128;I[a+1|0]=b>>>12&63|128;a=4;break a}}K[6585]=25;a=-1;break a}I[a|0]=b;a=1}return a}function ce(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(!$a(K[a+8>>2],54,c)){return 0}h=K[a+8>>2];d=K[h>>2];f=K[h+8>>2];a:{if(d){e=1;i=d&1;if((d|0)==1){d=0}else{d=d&-2;while(1){g=0;b:{if(!e){break b}g=0;if(!(va[K[f>>2]](a,b,c)|0)){break b}g=(va[K[f+4>>2]](a,b,c)|0)!=0}e=g;f=f+8|0;j=j+2|0;if((d|0)!=(j|0)){continue}break}d=!e}e=i?0:e;if(!(d|!i)){e=(va[K[f>>2]](a,b,c)|0)!=0}Ta(h);if(e){break a}return 0}Ta(h)}return 1}function Ee(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=ra-16|0;ra=e;g=K[K[a+96>>2]+16>>2];f=g>>>0<257?1:2;a:{if((f+2|0)!=(c|0)){a=0;Fa(d,1,4248,0);break a}if(K[a+8>>2]==16){c=K[a+180>>2]+Q(K[a+228>>2],5644)|0}else{c=K[a+12>>2]}Ha(b,e+12|0,f);a=1;b=b+f|0;Ha(b,e+8|0,1);f=K[e+12>>2];if(f>>>0>=g>>>0){K[e+4>>2]=g;K[e>>2]=f;Fa(d,1,14886,e);a=0;break a}Ha(b+1|0,(K[c+5584>>2]+Q(f,1080)|0)+808|0,1)}ra=e+16|0;return a|0}function Mb(a,b,c){var d=0,e=0,f=0;d=K[c+16>>2];a:{if(!d){if(Nb(c)){break a}d=K[c+16>>2]}e=K[c+20>>2];if(d-e>>>0<b>>>0){return va[K[c+36>>2]](c,a,b)|0}b:{c:{if(!b|K[c+80>>2]<0){break c}d=b;while(1){f=a+d|0;if(L[f-1|0]!=10){d=d-1|0;if(d){continue}break c}break}e=va[K[c+36>>2]](c,a,d)|0;if(e>>>0<d>>>0){break a}b=b-d|0;e=K[c+20>>2];break b}f=a;d=0}hb(e,f,b);K[c+20>>2]=K[c+20>>2]+b;e=b+d|0}return e}function Qe(a,b,c){var d=0,e=0,f=0,g=0;g=c&63;f=g;e=f&31;if(f>>>0>=32){f=-1>>>e|0}else{d=-1>>>e|0;f=d|(1<<e)-1<<32-e}f=f&a;d=b&d;e=g&31;if(g>>>0>=32){d=f<<e;g=0}else{d=(1<<e)-1&f>>>32-e|d<<e;g=f<<e}f=d;e=0-c&63;d=e&31;if(e>>>0>=32){d=-1<<d;c=0}else{c=-1<<d;d=c|(1<<d)-1&-1>>>32-d}a=c&a;b=b&d;d=e&31;if(e>>>0>=32){c=0;a=b>>>d|0}else{c=b>>>d|0;a=((1<<d)-1&b)<<32-d|a>>>d}a=a|g;ua=c|f;return a}
function lb(a,b,c){var d=0;if(!K[a+12>>2]){va[b|0](c,K[a+36>>2]);return}d=Ja(8);a:{if(!d){break a}K[d+4>>2]=c;K[d>>2]=b;b=Ja(8);if(!b){Ga(d);return}K[b>>2]=d;c=Q(K[a+4>>2],100);K[a+40>>2]=c;while(1){if((c|0)<K[a+24>>2]){continue}break}K[b+4>>2]=K[a+20>>2];K[a+20>>2]=b;K[a+24>>2]=K[a+24>>2]+1;b=K[a+28>>2];if(!b){break a}K[K[b>>2]+8>>2]=0;K[a+28>>2]=K[b+4>>2];K[a+32>>2]=K[a+32>>2]-1;Ga(b)}}function $c(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;K[a+184>>2]=b;d=K[a+96>>2];a:{if(!d){break a}f=K[d+24>>2];if(!f){break a}e=K[a+12>>2];if(!e|!K[e+5584>>2]){break a}e=K[d+16>>2];if(!e){return 1}d=0;while(1){if(N[(K[K[a+12>>2]+5584>>2]+Q(d,1080)|0)+4>>2]<=b>>>0){Fa(c,1,9140,0);return 0}K[(Q(d,52)+f|0)+40>>2]=b;g=1;d=d+1|0;if((e|0)!=(d|0)){continue}break}}return g|0}function Qc(a){var b=0,c=0;b=K[a+76>>2];if(!((b|0)>=0&(!b|K[6855]!=(b&1073741823)))){a:{if(K[a+80>>2]==10){break a}b=K[a+20>>2];if((b|0)==K[a+16>>2]){break a}K[a+20>>2]=b+1;I[b|0]=10;return}Rc(a);return}b=a+76|0;c=K[b>>2];K[b>>2]=c?c:1073741823;b:{c:{if(K[a+80>>2]==10){break c}c=K[a+20>>2];if((c|0)==K[a+16>>2]){break c}K[a+20>>2]=c+1;I[c|0]=10;break b}Rc(a)}K[b>>2]=0}function Qb(){var a=0,b=0,c=0;while(1){b=a<<4;c=b+26352|0;K[b+26356>>2]=c;K[b+26360>>2]=c;a=a+1|0;if((a|0)!=64){continue}break}Pc(48);a=ra-16|0;ra=a;a:{if(pa(a+12|0,a+8|0)|0){break a}b=Ab((K[a+12>>2]<<2)+4|0);K[6848]=b;if(!b){break a}b=Ab(K[a+8>>2]);if(b){c=K[6848];K[c+(K[a+12>>2]<<2)>>2]=0;if(!(oa(c|0,b|0)|0)){break a}}K[6848]=0}ra=a+16|0;K[6855]=42;K[6873]=27560}function Oa(a,b,c,d,e,f,g,h){var i=0,j=0;i=+R(e-a|0);j=i*1.402;if(S(j)<2147483647){e=~~j}else{e=-2147483648}e=e+c|0;K[f>>2]=(e|0)>=0?(b|0)>(e|0)?e:b:0;j=+R(d-a|0);i=j*.344+i*.714;if(S(i)<2147483647){a=~~i}else{a=-2147483648}a=c-a|0;K[g>>2]=(a|0)>=0?(a|0)<(b|0)?a:b:0;i=j*1.772;if(S(i)<2147483647){a=~~i}else{a=-2147483648}a=a+c|0;K[h>>2]=(a|0)>=0?(a|0)<(b|0)?a:b:0}function sd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;e=K[a+84>>2];f=K[e>>2];d=K[e+4>>2];h=K[a+28>>2];g=K[a+20>>2]-h|0;g=d>>>0<g>>>0?d:g;if(g){hb(f,h,g);f=g+K[e>>2]|0;K[e>>2]=f;d=K[e+4>>2]-g|0;K[e+4>>2]=d}d=c>>>0>d>>>0?d:c;if(d){hb(f,b,d);f=d+K[e>>2]|0;K[e>>2]=f;K[e+4>>2]=K[e+4>>2]-d}I[f|0]=0;b=K[a+44>>2];K[a+28>>2]=b;K[a+20>>2]=b;return c|0}function Gb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;if(a){c=K[a+4>>2];if(c){Ga(c);K[a+4>>2]=0}if(b){c=a;while(1){d=K[c+200>>2];if(d){e=0;f=K[c+196>>2];if(f){while(1){g=K[d+12>>2];if(g){Ga(g);K[d+12>>2]=0;f=K[c+196>>2]}d=d+16|0;e=e+1|0;if(e>>>0<f>>>0){continue}break}d=K[c+200>>2]}Ga(d);K[c+200>>2]=0}c=c+240|0;h=h+1|0;if((h|0)!=(b|0)){continue}break}}Ga(a)}}function Gd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0;e=K[c+8>>2];d=e>>>0<=1?1:e;f=K[c+4>>2];g=f-K[c>>2]|0;while(1){h=d;d=d<<1;if(h-g>>>0<b>>>0){continue}break}if((e|0)!=(h|0)){d=Ja(h);if(!d){return-1}e=K[c>>2];if(e){if(g){E(d,e,g)}Ga(K[c>>2])}K[c+8>>2]=h;K[c>>2]=d;f=d+g|0;K[c+4>>2]=f}if(b){E(f,a,b)}K[c+4>>2]=K[c+4>>2]+b;return b|0}function mc(a){K[a+100>>2]=20784;K[a+96>>2]=20784;K[a+92>>2]=20784;K[a+88>>2]=20784;K[a+84>>2]=20784;K[a+80>>2]=20784;K[a+76>>2]=20784;K[a+72>>2]=20784;K[a+68>>2]=20784;K[a+64>>2]=20784;K[a+60>>2]=20784;K[a+56>>2]=20784;K[a+52>>2]=20784;K[a+48>>2]=20784;K[a+44>>2]=20784;K[a+40>>2]=20784;K[a+36>>2]=20784;K[a+32>>2]=20784;K[a+28>>2]=20784}function Wa(a,b){var c=0,d=0,e=0,f=0;if((b|0)<=0){return 0}c=K[a+12>>2];d=K[a+16>>2];while(1){e=b;a:{if(d){break a}c=c<<8&65280;K[a+12>>2]=c;d=(c|0)==65280?7:8;K[a+16>>2]=d;b=K[a+8>>2];if(b>>>0>=N[a+4>>2]){break a}K[a+8>>2]=b+1;c=L[b|0]|c;K[a+12>>2]=c}d=d-1|0;K[a+16>>2]=d;b=e-1|0;f=(c>>>d&1)<<b|f;if(e>>>0>1){continue}break}return f}function Md(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;f=ra-16|0;ra=f;e=K[a+24>>2];if((e|0)!=255){K[f>>2]=e;Fa(d,2,2641,f)}a:{b:{if(K[a+20>>2]==(c|0)){if(c){break b}e=1;break a}e=0;Fa(d,1,14473,0);break a}c=0;while(1){e=1;Ha(b,(K[a+72>>2]+Q(c,12)|0)+8|0,1);b=b+1|0;c=c+1|0;if(c>>>0<N[a+20>>2]){continue}break}}ra=f+16|0;return e|0}function Ha(a,b,c){var d=0,e=0;K[b>>2]=0;a:{if(!c){break a}d=c&3;b=b+c|0;if(c>>>0>=4){e=c&-4;c=0;while(1){I[b-1|0]=L[a|0];I[b-2|0]=L[a+1|0];I[b-3|0]=L[a+2|0];b=b-4|0;I[b|0]=L[a+3|0];a=a+4|0;c=c+4|0;if((e|0)!=(c|0)){continue}break}}if(!d){break a}c=0;while(1){b=b-1|0;I[b|0]=L[a|0];a=a+1|0;c=c+1|0;if((d|0)!=(c|0)){continue}break}}}function we(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=ra-16|0;ra=e;a:{if(!c){Fa(d,1,4069,0);a=0;break a}Ha(b,e+12|0,1);f=c-1|0;a=1;if(!f){break a}a=0;c=0;while(1){b=b+1|0;Ha(b,e+8|0,1);g=K[e+8>>2];c=g<<24>>31&(g&127|c)<<7;a=a+1|0;if((f|0)!=(a|0)){continue}break}a=1;if(!c){break a}Fa(d,1,4069,0);a=0}ra=e+16|0;return a|0}function rc(a,b,c,d){var e=0,f=0,g=R(0),h=0,i=R(0),j=0,k=R(0);if(d){while(1){e=f<<2;h=e+b|0;i=O[h>>2];j=a+e|0;g=O[j>>2];e=c+e|0;k=O[e>>2];O[j>>2]=R(k*R(1.4019999504089355))+g;O[h>>2]=R(g+R(i*R(-.3441300094127655)))+R(k*R(-.714139997959137));O[e>>2]=g+R(i*R(1.7719999551773071));f=f+1|0;if((f|0)!=(d|0)){continue}break}}}function Jb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;f=ra-240|0;ra=f;K[f>>2]=a;g=1;a:{if((b|0)<2){break a}d=a;while(1){d=d-8|0;h=b-2|0;e=d-K[(h<<2)+c>>2]|0;if((gb(a,e)|0)>=0){if((gb(a,d)|0)>=0){break a}}i=e;e=(gb(e,d)|0)>=0;d=e?i:d;K[(g<<2)+f>>2]=d;g=g+1|0;b=e?b-1|0:h;if((b|0)>1){continue}break}}Mc(f,g);ra=f+240|0}function Mc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;c=8;f=ra-256|0;ra=f;if((b|0)>=2){h=(b<<2)+a|0;K[h>>2]=f;while(1){e=c>>>0>=256?256:c;hb(K[h>>2],K[a>>2],e);d=0;while(1){g=(d<<2)+a|0;d=d+1|0;hb(K[g>>2],K[(d<<2)+a>>2],e);K[g>>2]=K[g>>2]+e;if((b|0)!=(d|0)){continue}break}c=c-e|0;if(c){continue}break}}ra=f+256|0}function gd(a){a=a|0;var b=0,c=0,d=0,e=0;b=K[a+24>>2];if(b){c=K[a+28>>2];e=(c>>>0)/52|0;if(c>>>0>=52){while(1){c=K[b>>2];if(c){Ga(c-1|0);K[b>>2]=0}c=K[b+4>>2];if(c){Ga(c);K[b+4>>2]=0}c=K[b+8>>2];if(c){Ga(c);K[b+8>>2]=0}b=b+52|0;d=d+1|0;if((e|0)!=(d|0)){continue}break}b=K[a+24>>2]}Ga(b);K[a+24>>2]=0}}function hd(a){a=a|0;var b=0,c=0,d=0,e=0;b=K[a+24>>2];if(b){c=K[a+28>>2];e=(c>>>0)/68|0;if(c>>>0>=68){while(1){c=K[b>>2];if(c){Ga(c);K[b>>2]=0}c=K[b+4>>2];if(c){Ga(c);K[b+4>>2]=0}Ga(K[b+60>>2]);K[b+60>>2]=0;b=b+68|0;d=d+1|0;if((e|0)!=(d|0)){continue}break}b=K[a+24>>2]}Ga(b);K[a+24>>2]=0}}function md(a,b){a=a|0;b=b|0;var c=0,d=0;c=K[a+32>>2];b=K[a+28>>2];d=b+8|0;if(c>>>0>=d>>>0){while(1){rb(a,K[a+24>>2]+(b<<2)|0,K[a+20>>2],8);c=K[a+32>>2];b=d;d=b+8|0;if(c>>>0>=d>>>0){continue}break}}if(b>>>0<c>>>0){rb(a,K[a+24>>2]+(b<<2)|0,K[a+20>>2],c-b|0)}Ga(K[a>>2]);Ga(a)}function fb(a,b,c){var d=0,e=0,f=0;a:{if(!b){d=a;e=b;break a}while(1){d=Ne(a,b,10,0);e=ua;a=Le(d,e,246)+a|0;c=c-1|0;I[c|0]=a|48;f=b>>>0>9;a=d;b=e;if(f){continue}break}}if(d|e){while(1){c=c-1|0;a=(d>>>0)/10|0;I[c|0]=Q(a,246)+d|48;b=d>>>0>9;d=a;if(b){continue}break}}return c}function Rd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=ra-16|0;ra=e;a:{if(K[a+100>>2]){Fa(d,1,11265,0);a=0;break a}if((c|0)!=4){Fa(d,1,5954,0);a=0;break a}Ha(b,e+12|0,4);if(K[e+12>>2]!=218793738){Fa(d,1,4970,0);a=0;break a}K[a+100>>2]=K[a+100>>2]|1;a=1}ra=e+16|0;return a|0}function $a(a,b,c){var d=0,e=0;a:{d=K[a>>2];e=K[a+4>>2];b:{if((d|0)!=(e|0)){e=K[a+8>>2];break b}d=e+10|0;K[a+4>>2]=d;e=La(K[a+8>>2],d<<2);if(!e){break a}K[a+8>>2]=e;d=K[a>>2]}K[(d<<2)+e>>2]=b;K[a>>2]=d+1;return 1}Ga(K[a+8>>2]);K[a>>2]=0;K[a+4>>2]=0;Fa(c,1,6086,0);return 0}function Rc(a){var b=0,c=0,d=0;c=ra-16|0;ra=c;I[c+15|0]=10;b=K[a+16>>2];a:{if(!b){if(Nb(a)){break a}b=K[a+16>>2]}d=b;b=K[a+20>>2];if(!((d|0)==(b|0)|K[a+80>>2]==10)){K[a+20>>2]=b+1;I[b|0]=10;break a}if((va[K[a+36>>2]](a,c+15|0,1)|0)!=1){break a}}ra=c+16|0}function Ic(a){var b=0,c=0,d=0,e=0,f=0;d=K[a>>2];b=I[d|0]-48|0;if(b>>>0>9){return 0}while(1){e=-1;if(c>>>0<=214748364){c=Q(c,10);e=(c^2147483647)>>>0<b>>>0?-1:c+b|0}b=d+1|0;K[a>>2]=b;f=I[d+1|0];c=e;d=b;b=f-48|0;if(b>>>0<10){continue}break}return c}function Fc(a,b){var c=0,d=0,e=0;A(+a);d=v(1)|0;e=v(0)|0;c=d>>>20&2047;if((c|0)!=2047){if(!c){if(a==0){c=0}else{a=Fc(a*0x10000000000000000,b);c=K[b>>2]+-64|0}K[b>>2]=c;return a}K[b>>2]=c-1022;x(0,e|0);x(1,d&-2146435073|1071644672);a=+z()}return a}function he(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=R(0),f=0,g=0;d=ra-16|0;ra=d;if(c){while(1){ad(a,d+12|0);e=O[d+12>>2];if(R(S(e))<R(2147483648)){f=~~e}else{f=-2147483648}K[b>>2]=f;b=b+4|0;a=a+4|0;g=g+1|0;if((g|0)!=(c|0)){continue}break}}ra=d+16|0}function Ya(a){var b=0,c=0,d=0;if(a){b=K[a+24>>2];if(b){c=K[a+16>>2];if(c){b=0;while(1){d=K[(K[a+24>>2]+Q(b,52)|0)+44>>2];if(d){Ga(d);c=K[a+16>>2]}b=b+1|0;if(c>>>0>b>>>0){continue}break}b=K[a+24>>2]}Ga(b)}b=K[a+28>>2];if(b){Ga(b)}Ga(a)}}function ge(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;d=ra-16|0;ra=d;if(c){while(1){Zb(a,d+8|0);e=P[d+8>>3];if(S(e)<2147483647){f=~~e}else{f=-2147483648}K[b>>2]=f;b=b+4|0;a=a+8|0;g=g+1|0;if((g|0)!=(c|0)){continue}break}}ra=d+16|0}function Fd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;d=K[c+4>>2];e=K[c>>2]+K[c+8>>2]|0;if((d|0)==(e|0)){ua=-1;return-1}K[c+4>>2]=a+d;f=a;c=e-d|0;d=c;e=a>>>0<c>>>0;a=c>>31;c=e&(a|0)>=(b|0)|(a|0)>(b|0);d=c?f:d;ua=c?b:a;return d|0}function Me(a,b,c,d){var e=0,f=0,g=0,h=0;f=b^d;g=f>>31;e=b>>31;a=a^e;h=a-e|0;e=(b^e)-((a>>>0<e>>>0)+e|0)|0;a=d>>31;b=c^a;f=f>>31;a=Ne(h,e,b-a|0,(a^d)-((a>>>0>b>>>0)+a|0)|0)^f;b=a-f|0;ua=(g^ua)-((a>>>0<f>>>0)+g|0)|0;return b}function _a(a){var b=0,c=0,d=0,e=0;if(a){b=K[a+20>>2];c=K[a+16>>2];if(Q(b,c)){while(1){e=K[K[a+24>>2]+(d<<2)>>2];if(e){Ga(e);c=K[a+16>>2];b=K[a+20>>2]}d=d+1|0;if(d>>>0<Q(b,c)>>>0){continue}break}}Ga(K[a+24>>2]);Ga(a)}}function sc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0;if(d){while(1){e=f<<2;g=e+a|0;h=c+e|0;i=K[h>>2];j=b+e|0;k=K[j>>2];e=K[g>>2]-(i+k>>2)|0;K[g>>2]=e+i;K[j>>2]=e;K[h>>2]=e+k;f=f+1|0;if((f|0)!=(d|0)){continue}break}}}function ib(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;K[a+48>>2]=0;K[a+36>>2]=K[a+32>>2];e=va[K[a+28>>2]](b,c,K[a>>2])|0;d=K[a+68>>2];if(!e){K[a+68>>2]=d|4;return 0}K[a+56>>2]=b;K[a+60>>2]=c;K[a+68>>2]=d&-5;return 1}function Ra(a,b,c,d,e){var f=0;f=ra-256|0;ra=f;if(!(e&73728|(c|0)<=(d|0))){d=c-d|0;c=d>>>0<256;Sc(f,b,c?d:256);if(!c){while(1){Pa(a,f,256);d=d-256|0;if(d>>>0>255){continue}break}}Pa(a,f,d)}ra=f+256|0}function Le(a,b,c){var d=0,e=0,f=0,g=0,h=0;e=c>>>16|0;d=a>>>16|0;h=Q(e,d);f=c&65535;a=a&65535;g=Q(f,a);d=(g>>>16|0)+Q(d,f)|0;a=(d&65535)+Q(a,e)|0;ua=h+Q(b,c)+(d>>>16)+(a>>>16)|0;return g&65535|a<<16}function Nb(a){var b=0;b=K[a+72>>2];K[a+72>>2]=b-1|b;b=K[a>>2];if(b&8){K[a>>2]=b|32;return-1}K[a+4>>2]=0;K[a+8>>2]=0;b=K[a+44>>2];K[a+28>>2]=b;K[a+20>>2]=b;K[a+16>>2]=b+K[a+48>>2];return 0}function xc(a){var b=0,c=0;a:{if(L[a+12|0]==255){K[a+12>>2]=65280;K[a+16>>2]=7;b=K[a+8>>2];c=0;if(b>>>0>=N[a+4>>2]){break a}K[a+8>>2]=b+1;K[a+12>>2]=L[b|0]|65280}K[a+16>>2]=0;c=1}return c}function Hd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;e=K[c+4>>2];d=K[c>>2]+K[c+8>>2]|0;if((e|0)==(d|0)){return-1}d=d-e|0;b=b>>>0>d>>>0?d:b;if(b){E(a,e,b)}K[c+4>>2]=b+K[c+4>>2];return b|0}function le(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=ra-16|0;ra=d;if(c){while(1){ad(a,d+12|0);O[b>>2]=O[d+12>>2];b=b+4|0;a=a+4|0;e=e+1|0;if((e|0)!=(c|0)){continue}break}}ra=d+16|0}function ke(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=ra-16|0;ra=d;if(c){while(1){Zb(a,d+8|0);O[b>>2]=P[d+8>>3];b=b+4|0;a=a+8|0;e=e+1|0;if((e|0)!=(c|0)){continue}break}}ra=d+16|0}function nd(a,b){a=a|0;b=b|0;b=K[a+28>>2];if(b>>>0<N[a+32>>2]){while(1){pc(a,K[a+24>>2]+(Q(K[a+20>>2],b)<<2)|0);b=b+1|0;if(b>>>0<N[a+32>>2]){continue}break}}Ga(K[a>>2]);Ga(a)}function rd(a,b){a=a|0;b=+b;var c=0;ma(a|0,0)|0;a=(a|0)==2?27:(a|0)==1?26:14;a:{if(K[7158]>>>a-1&1){K[7190]=K[7190]|1<<a-1;break a}c=K[(a<<2)+25760>>2];if(c){va[c|0](a)}}}function Xc(a,b){a=a|0;b=b|0;var c=0,d=0;c=K[a>>2];d=K[b>>2];a=K[a+4>>2];b=K[b+4>>2];return(c>>>0>d>>>0&(a|0)>=(b|0)|(a|0)>(b|0))-(c>>>0<d>>>0&(a|0)<=(b|0)|(a|0)<(b|0))|0}function zd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=ra-16|0;ra=e;a=Kb(ia(K[a+60>>2],b|0,c|0,d&255,e+8|0)|0);ra=e+16|0;ua=a?-1:K[e+12>>2];return(a?-1:K[e+8>>2])|0}function Cc(a,b,c,d){var e=0,f=0;e=ra-16|0;ra=e;if(c){while(1){Ha(a,e+12|0,d);O[b>>2]=N[e+12>>2];b=b+4|0;a=a+d|0;f=f+1|0;if((f|0)!=(c|0)){continue}break}}ra=e+16|0}function Bc(a,b,c,d){var e=0,f=0;e=ra-16|0;ra=e;if(c){while(1){Ha(a,e+12|0,d);K[b>>2]=K[e+12>>2];b=b+4|0;a=a+d|0;f=f+1|0;if((f|0)!=(c|0)){continue}break}}ra=e+16|0}function Zb(a,b){I[b+7|0]=L[a|0];I[b+6|0]=L[a+1|0];I[b+5|0]=L[a+2|0];I[b+4|0]=L[a+3|0];I[b+3|0]=L[a+4|0];I[b+2|0]=L[a+5|0];I[b+1|0]=L[a+6|0];I[b|0]=L[a+7|0]}function Xd(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(c){Fa(d,2,10187,0);if(!Rb(K[a>>2],b,c,d,e)){Fa(d,1,6173,0);return 0}a=Uc(a,c,d)}else{a=0}return a|0}function Va(a){var b=0,c=0,d=0,e=0;b=K[a+12>>2];e=b;c=K[a+8>>2];if(!(b|c)){ua=0;return 0}d=K[a+56>>2];b=c-d|0;ua=e-(K[a+60>>2]+(c>>>0<d>>>0)|0)|0;return b}function $d(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;h=h|0;i=i|0;j=j|0;k=k|0;return ab(K[a>>2],b,c,d,e,f,g,h,i,j,k)|0}function Ac(a,b){var c=0;c=ra-16|0;ra=c;if(a){if(b&3){a=28}else{a=mb(b,a);K[c+12>>2]=a;a=a?0:48}a=a?0:K[c+12>>2]}else{a=0}ra=c+16|0;return a}function id(a){a=a|0;var b=0;if(a){b=K[a+116>>2];if(b){Ga(b);K[a+116>>2]=0}b=K[a+120>>2];if(b){Ga(b);K[a+120>>2]=0}Ga(K[a+148>>2]);Ga(a)}}
function wb(a,b){var c=0,d=0;a:{if(b>>>0<=31){d=K[a>>2];c=a+4|0;break a}b=b-32|0;c=a}c=K[c>>2];K[a>>2]=d<<b;K[a+4>>2]=c<<b|d>>>32-b}function yb(a,b){var c=0,d=0;c=K[a+4>>2];a:{if(b>>>0<=31){d=K[a>>2];break a}b=b-32|0;d=c;c=0}K[a+4>>2]=c>>>b;K[a>>2]=c<<32-b|d>>>b}function fe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(!c){return 0}if(!Tb(K[a>>2],b,c,d)){Fa(d,1,6173,0);return 0}return Uc(a,c,d)|0}function te(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(K[K[a+96>>2]+16>>2]<<2!=(c|0)){Fa(d,1,4427,0);a=0}else{a=1}return a|0}function zc(){var a=0,b=0;a=Ia(1,44);a:{if(a){K[a+16>>2]=0;b=Ia(1,8);K[a+36>>2]=b;if(b){break a}Ga(a)}a=0}return a}function dc(a,b){a=a|0;b=b|0;if(!(!a|!b)){K[a+188>>2]=K[b+4>>2];K[a+184>>2]=K[b>>2];K[a+248>>2]=K[b+8248>>2]&2}}function ub(){var a=0,b=0;a=Ia(1,12);if(a){K[a+4>>2]=10;b=Ia(10,4);K[a+8>>2]=b;if(b){return a}Ga(a)}return 0}function Yd(a,b,c,d,e,f,g){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;g=g|0;return _b(K[a>>2],b,c,d,e,f,g)|0}function zb(a){var b=0;if(a){b=K[a+4>>2];if(b){va[b|0](K[a>>2])}Ga(K[a+32>>2]);K[a+32>>2]=0;Ga(a)}}function cc(a,b){a=a|0;b=b|0;a:{if(!a){break a}K[a+208>>2]=b;if(!b){break a}I[a+92|0]=L[a+92|0]|8}}function Ed(a,b,c){a=a|0;b=b|0;c=c|0;b=K[c+8>>2];K[c+4>>2]=K[c>>2]+(a>>>0>b>>>0?b:a);return 1}function _d(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;return jb(K[a>>2],b,c,d,e,f)|0}function xe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(c){a=1}else{Fa(d,1,4338,0);a=0}return a|0}function ob(a){K[a>>2]=0;K[a+4>>2]=0;K[a+16>>2]=0;K[a+20>>2]=0;K[a+8>>2]=0;K[a+12>>2]=0}function ed(a,b,c){a=a|0;b=b|0;c=c|0;return!K[a+8>>2]&(K[a+216>>2]!=0&K[a+220>>2]!=0)}function Xa(a){if(K[a+12>>2]){K[a+40>>2]=0;while(1){if(K[a+24>>2]>0){continue}break}}}function ad(a,b){I[b+3|0]=L[a|0];I[b+2|0]=L[a+1|0];I[b+1|0]=L[a+2|0];I[b|0]=L[a+3|0]}function Cb(a){if(a){va[K[(K[a+76>>2]?20:16)+a>>2]](K[a+48>>2]);K[a+48>>2]=0;Ga(a)}}function ee(a,b){a=a|0;b=b|0;dc(K[a>>2],b);I[a+124|0]=0;K[a+128>>2]=K[b+8248>>2]&1}function Ia(a,b){if(!a|!b){a=0}else{b=Q(a,b);a=mb(8,b);if(a){Sc(a,0,b)}}return a}function Ka(a,b,c){var d=0;d=ra-16|0;ra=d;K[d+12>>2]=c;Kc(a,b,c,0,0);ra=d+16|0}function Pe(a){var b=0;while(1){if(a){a=a-1&a;b=b+1|0;continue}break}return b}function eb(a){var b=0;if(a){b=K[a+12>>2];if(b){Ga(b);K[a+12>>2]=0}Ga(a)}}function Zd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return $b(K[a>>2],b,c,d)|0}function Sa(a,b,c){a:{if(K[c+76>>2]<0){a=Mb(a,b,c);break a}a=Mb(a,b,c)}}function Nc(a,b){a=Lc(a-1|0);if(!a){a=Lc(b);a=a?a|32:0}return a}function ec(a){return K[a+12>>2]==K[a+4>>2]|K[a+8>>2]==K[a>>2]}function Sd(a,b,c){a=a|0;b=b|0;c=c|0;return $c(K[a>>2],b,c)|0}function tb(a){var b=0;if(a){b=K[a+8>>2];if(b){Ga(b)}Ga(a)}}function Lc(a){var b=0,c=0,d=0;return b=Ke(a),c=0,d=a,d?b:c}function vd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;ua=0;return 0}function db(a,b,c,d,e,f,g,h){return qc(a,b,c,d,e,f,g,h,0)}function bb(a,b,c){K[((b<<2)+a|0)+28>>2]=(c<<5)+20784}function Pb(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return 1}function Dc(a,b,c,d){return va[K[a+44>>2]](a,b,c,d)|0}function Wd(a,b,c){a=a|0;b=b|0;c=c|0;Xb(K[a>>2],b,c)}function vb(a,b,c){return va[K[a+40>>2]](a,b,0,c)|0}function re(a,b,c){a=a|0;b=b|0;c=c|0;ua=-1;return-1}function Ke(a){if(a){return 31-T(a-1^a)|0}return 32}function xd(a){a=a|0;return Kb(aa(K[a+60>>2])|0)|0}function Ua(a,b,c,d,e,f,g,h){qc(a,b,c,d,e,f,g,h,1)}function Kb(a){if(!a){return 0}K[6585]=a;return-1}function ne(a,b,c){a=a|0;b=b|0;c=c|0;Cc(a,b,c,2)}function me(a,b,c){a=a|0;b=b|0;c=c|0;Cc(a,b,c,4)}function je(a,b,c){a=a|0;b=b|0;c=c|0;Bc(a,b,c,2)}function ie(a,b,c){a=a|0;b=b|0;c=c|0;Bc(a,b,c,4)}function Pa(a,b,c){if(!(L[a|0]&32)){Mb(b,c,a)}}function Oe(a,b,c){Je(a,0,b,c);ua=ta;return sa}function bc(a,b,c){a=a|0;b=b|0;c=c|0;return 1}function Yc(a,b,c){a=a|0;b=b|0;c=c|0;return-1}function Be(a,b,c){a=a|0;b=b|0;c=c|0;return 0}function Ne(a,b,c,d){a=Je(a,b,c,d);return a}function Ja(a){if(!a){return 0}return Ab(a)}function de(a,b){a=a|0;b=b|0;cc(K[a>>2],b)}function Sc(a,b,c){if(c){B(a,b<<24>>24,c)}}function yc(a){return K[a+8>>2]-K[a>>2]|0}function pd(a){a=a|0;ka();ja(a+128|0);G()}function Vd(a){a=a|0;return Ub(K[a>>2])|0}function Td(a){a=a|0;return Vb(K[a>>2])|0}function fd(a,b){a=a|0;b=b|0;return 0}function Ab(a){a=a|0;return mb(8,a)|0}function Bd(a,b){a=a|0;b=b|0;ca(a|0)}function Ib(a){return K[a+28>>2]!=2}function Ad(a,b){a=a|0;b=b|0;$(a|0)}function hb(a,b,c){if(c){E(a,b,c)}}function gb(a,b){return Xc(a,b)}function sb(a){return Ac(a,32)}function Ma(a){return Ac(a,16)}function wd(a){a=a|0;return 0}function qd(a){a=a|0;Ec();G()}function Bb(){return Ia(1,36)}function gc(a,b){a=a|0;b=b|0}function kb(a){if(a){Ga(a)}}function Ta(a){K[a>>2]=0}function od(){Ec();G()}function Ec(){la();G()}
// EMSCRIPTEN_END_FUNCS
e=L;p(q);var va=c([null,gc,Be,re,Yc,Yc,ib,Ud,Jd,Dd,nd,md,ld,kd,jd,id,hd,gd,bc,ed,dd,cd,bd,Xc,Ie,He,Ge,Fe,Ee,De,Ce,Ae,ze,ye,xe,we,ve,ue,te,Pb,se,qe,Pb,Pb,pe,oe,ne,me,le,ke,je,ie,he,ge,be,Rd,Qd,Pd,Od,Nd,Md,Ld,Kd,Id,Hd,Gd,Fd,Ed,Ub,Vb,Xb,bc,Tb,cc,dc,Eb,ac,fd,$b,$c,Rb,_b,jb,ab,Vd,Td,Wd,ce,fe,fd,Zd,Sd,Xd,Yd,de,ee,Tc,_d,$d,ae,gc,Bd,Ad,sd,ud,td,od,xd,yd,zd,wd,vd,pd,qd]);function wa(){return H.byteLength/65536|0}function Ba(Ca){Ca=Ca|0;var xa=wa()|0;var ya=xa+Ca|0;if(xa<ya&&ya<65536){var za=new ArrayBuffer(Q(ya,65536));var Aa=new Int8Array(za);Aa.set(I);I=new Int8Array(za);J=new Int16Array(za);K=new Int32Array(za);L=new Uint8Array(za);M=new Uint16Array(za);N=new Uint32Array(za);O=new Float32Array(za);P=new Float64Array(za);H=za;e=L}return xa}return{s:Object.create(Object.prototype,{grow:{value:Ba},buffer:{get:function(){return H}}}),t:Qb,u:Ab,v:Ga,w:va,x:Cd,y:rd}}return Da(Ea)}
// EMSCRIPTEN_END_ASM


)(info)},instantiate:function(binary,info){return{then:function(ok){var module=new WebAssembly.Module(binary);ok({instance:new WebAssembly.Instance(module,info)})}}},RuntimeError:Error,isWasm2js:true};if(WebAssembly.isWasm2js){wasmBinary=[]}var wasmMemory;var ABORT=false;var EXITSTATUS;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;var runtimeInitialized=false;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(onPreRuns)}function initRuntime(){runtimeInitialized=true;wasmExports["t"]()}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(onPostRuns)}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies)}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var wasmBinaryFile;function findWasmBinary(){if(Module["locateFile"]){return locateFile("openjpeg_nowasm_fallback.wasm")}return new URL("openjpeg_nowasm_fallback.wasm",import.meta.url).href}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw'sync fetching of the wasm failed: you can preload it to Module["wasmBinary"] manually, or emcc.py will do that for you when generating HTML (but not JS)'}function instantiateSync(file,info){var module;var binary=getBinarySync(file);module=new WebAssembly.Module(binary);var instance=new WebAssembly.Instance(module,info);return[instance,module]}function getWasmImports(){return{a:wasmImports}}function createWasm(){function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["s"];updateMemoryViews();removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");var info=getWasmImports();if(Module["instantiateWasm"]){return new Promise((resolve,reject)=>{Module["instantiateWasm"](info,(mod,inst)=>{receiveInstance(mod,inst);resolve(mod.exports)})})}wasmBinaryFile??=findWasmBinary();var result=instantiateSync(wasmBinaryFile,info);return receiveInstance(result[0])}class ExitStatus{name="ExitStatus";constructor(status){this.message=`Program terminated with exit(${status})`;this.status=status}}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var onPostRuns=[];var addOnPostRun=cb=>onPostRuns.unshift(cb);var onPreRuns=[];var addOnPreRun=cb=>onPreRuns.unshift(cb);var noExitRuntime=Module["noExitRuntime"]||true;var __abort_js=()=>abort("");var runtimeKeepaliveCounter=0;var __emscripten_runtime_keepalive_clear=()=>{noExitRuntime=false;runtimeKeepaliveCounter=0};var timers={};var handleException=e=>{if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)};var keepRuntimeAlive=()=>noExitRuntime||runtimeKeepaliveCounter>0;var _proc_exit=code=>{EXITSTATUS=code;if(!keepRuntimeAlive()){Module["onExit"]?.(code);ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=(status,implicit)=>{EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var maybeExit=()=>{if(!keepRuntimeAlive()){try{_exit(EXITSTATUS)}catch(e){handleException(e)}}};var callUserCallback=func=>{if(ABORT){return}try{func();maybeExit()}catch(e){handleException(e)}};var _emscripten_get_now=()=>performance.now();var __setitimer_js=(which,timeout_ms)=>{if(timers[which]){clearTimeout(timers[which].id);delete timers[which]}if(!timeout_ms)return 0;var id=setTimeout(()=>{delete timers[which];callUserCallback(()=>__emscripten_timeout(which,_emscripten_get_now()))},timeout_ms);timers[which]={id,timeout_ms};return 0};function _copy_pixels_1(compG_ptr,nb_pixels){compG_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);imageData.set(compG)}function _copy_pixels_3(compR_ptr,compG_ptr,compB_ptr,nb_pixels){compR_ptr>>=2;compG_ptr>>=2;compB_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*3);const compR=HEAP32.subarray(compR_ptr,compR_ptr+nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compB=HEAP32.subarray(compB_ptr,compB_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[3*i]=compR[i];imageData[3*i+1]=compG[i];imageData[3*i+2]=compB[i]}}function _copy_pixels_4(compR_ptr,compG_ptr,compB_ptr,compA_ptr,nb_pixels){compR_ptr>>=2;compG_ptr>>=2;compB_ptr>>=2;compA_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compR=HEAP32.subarray(compR_ptr,compR_ptr+nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compB=HEAP32.subarray(compB_ptr,compB_ptr+nb_pixels);const compA=HEAP32.subarray(compA_ptr,compA_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=compR[i];imageData[4*i+1]=compG[i];imageData[4*i+2]=compB[i];imageData[4*i+3]=compA[i]}}var getHeapMax=()=>2147483648;var alignMemory=(size,alignment)=>Math.ceil(size/alignment)*alignment;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536|0;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignMemory(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:lang,_:getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++]=str.charCodeAt(i)}HEAP8[buffer]=0};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var _fd_close=fd=>52;var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);return 70}var printCharBuffers=[null,[],[]];var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder:undefined;var UTF8ArrayToString=(heapOrArray,idx=0,maxBytesToRead=NaN)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var printChar=(stream,curr)=>{var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer));buffer.length=0}else{buffer.push(curr)}};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):"";var _fd_write=(fd,iov,iovcnt,pnum)=>{var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){printChar(fd,HEAPU8[ptr+j])}num+=len}HEAPU32[pnum>>2]=num;return 0};function _gray_to_rgba(compG_ptr,nb_pixels){compG_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=imageData[4*i+1]=imageData[4*i+2]=compG[i];imageData[4*i+3]=255}}function _graya_to_rgba(compG_ptr,compA_ptr,nb_pixels){compG_ptr>>=2;compA_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compA=HEAP32.subarray(compA_ptr,compA_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=imageData[4*i+1]=imageData[4*i+2]=compG[i];imageData[4*i+3]=compA[i]}}function _jsPrintWarning(message_ptr){const message=UTF8ToString(message_ptr);(Module.warn||console.warn)(`OpenJPEG: ${message}`)}function _rgb_to_rgba(compR_ptr,compG_ptr,compB_ptr,nb_pixels){compR_ptr>>=2;compG_ptr>>=2;compB_ptr>>=2;const imageData=Module.imageData=new Uint8ClampedArray(nb_pixels*4);const compR=HEAP32.subarray(compR_ptr,compR_ptr+nb_pixels);const compG=HEAP32.subarray(compG_ptr,compG_ptr+nb_pixels);const compB=HEAP32.subarray(compB_ptr,compB_ptr+nb_pixels);for(let i=0;i<nb_pixels;i++){imageData[4*i]=compR[i];imageData[4*i+1]=compG[i];imageData[4*i+2]=compB[i];imageData[4*i+3]=255}}function _storeErrorMessage(message_ptr){const message=UTF8ToString(message_ptr);if(!Module.errorMessages){Module.errorMessages=message}else{Module.errorMessages+="\n"+message}}var wasmImports={m:__abort_js,l:__emscripten_runtime_keepalive_clear,n:__setitimer_js,g:_copy_pixels_1,f:_copy_pixels_3,e:_copy_pixels_4,o:_emscripten_resize_heap,p:_environ_get,q:_environ_sizes_get,b:_fd_close,j:_fd_seek,c:_fd_write,r:_gray_to_rgba,i:_graya_to_rgba,d:_jsPrintWarning,k:_proc_exit,h:_rgb_to_rgba,a:_storeErrorMessage};var wasmExports=createWasm();var ___wasm_call_ctors=wasmExports["t"];var _malloc=Module["_malloc"]=wasmExports["u"];var _free=Module["_free"]=wasmExports["v"];var _jp2_decode=Module["_jp2_decode"]=wasmExports["x"];var __emscripten_timeout=wasmExports["y"];function run(){if(runDependencies>0){dependenciesFulfilled=run;return}preRun();if(runDependencies>0){dependenciesFulfilled=run;return}function doRun(){Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);Module["onRuntimeInitialized"]?.();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(()=>{setTimeout(()=>Module["setStatus"](""),1);doRun()},1)}else{doRun()}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();moduleRtn=Module;


  return moduleRtn;
}
);
})();
export default OpenJPEG;
