{"name": "tiny-invariant", "version": "1.3.3", "description": "A tiny invariant function", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["invariant", "error", "assert", "asserts"], "repository": {"type": "git", "url": "https://github.com/alexreardon/tiny-invariant.git"}, "bugs": {"url": "https://github.com/alexreardon/tiny-invariant/issues"}, "main": "dist/tiny-invariant.cjs.js", "module": "dist/tiny-invariant.esm.js", "types": "dist/tiny-invariant.d.ts", "exports": {".": {"import": "./dist/esm/tiny-invariant.js", "default": {"types": "./dist/tiny-invariant.d.ts", "default": "./dist/tiny-invariant.cjs.js"}}}, "sideEffects": false, "files": ["/dist", "/src"], "size-limit": [{"path": "dist/tiny-invariant.min.js", "limit": "217B"}, {"path": "dist/tiny-invariant.js", "limit": "267B"}, {"path": "dist/tiny-invariant.cjs.js", "limit": "171B"}, {"path": "dist/tiny-invariant.esm.js", "import": "foo", "limit": "112B"}], "scripts": {"test": "yarn jest", "test:size": "yarn build && yarn size-limit", "prettier:write": "yarn prettier --debug-check src/** test/**", "prettier:check": "yarn prettier --write src/** test/**", "typescript:check": "tsc --noEmit", "validate": "yarn prettier:check && yarn typescript:check", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:flow": "cp src/tiny-invariant.js.flow dist/tiny-invariant.cjs.js.flow", "build:typescript": "tsc ./src/tiny-invariant.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:typescript:esm": "tsc ./src/tiny-invariant.ts --emitDeclarationOnly --declaration --outDir ./dist/esm", "build:dist": "yarn rollup --config rollup.config.mjs", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:typescript:esm", "prepublishOnly": "yarn build"}, "devDependencies": {"@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-typescript": "^11.1.6", "@size-limit/preset-small-lib": "^11.0.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.20", "@types/rollup": "^0.54.0", "expect-type": "^0.17.3", "jest": "^29.7.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.12.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^11.0.2", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "typescript": "^5.3.3"}}